var e = require("../@babel/runtime/helpers/objectSpread2"), t = require("../@babel/runtime/helpers/typeof"), r = {}, n = {
    promise_list: [],
    page_route_map: [],
    global_title: {},
    preset_properties: {
        lib: "MiniProgram",
        lib_version: "1.0.0",
        platform_type: "client_2"
    }
}, o = {
    name: "sensors",
    server_url: "https://sdc.12580.com/datagateway/api/accept/applet",
    send_timeout: 1e3,
    show_log: !1,
    login_id_key: "$identity_login_id",
    allow_amend_share_path: !0,
    max_string_length: 500,
    datasend_timeout: 3e3,
    autoTrack: {
        appLaunch: !0,
        appShow: !0,
        appHide: !0,
        pageShow: !0,
        pageShare: !0,
        mpClick: !1,
        mpFavorite: !0,
        pageLeave: !1
    },
    autotrack_exclude_page: {
        pageShow: [],
        pageLeave: []
    },
    is_persistent_save: {
        share: !1,
        utm: !1
    },
    storage_store_key: "sensorsdata2015_wechat"
}, a = (Array.isArray, Object.prototype), i = Array.prototype, p = i.forEach, u = (i.indexOf, 
a.toString), c = a.hasOwnProperty, s = i.slice;

function l() {
    if (o.show_log && "object" == ("undefined" == typeof console ? "undefined" : t(console)) && console.log) try {
        return console.log.apply(console, arguments);
    } catch (e) {
        e = VM2_INTERNAL_STATE_DO_NOT_USE_OR_PROGRAM_WILL_FAIL.handleException(e);
        console.log(arguments[0]);
    }
}

function f(e) {
    return null != e && "[object Object]" == u.call(e);
}

function _(e) {
    return "[object String]" == u.call(e);
}

function g(e) {
    return _(e) ? e.toLocaleUpperCase() : e;
}

function h(e) {
    return d(s.call(arguments, 1), function(t) {
        for (var r in t) void 0 !== t[r] && (e[r] = t[r]);
    }), e;
}

function d(e, t, r) {
    if (null == e) return !1;
    var n = {};
    if (p && e.forEach === p) e.forEach(t, r); else if (e.length === +e.length) {
        for (var o = 0, a = e.length; o < a; o++) if (o in e && t.call(r, e[o], o, e) === n) return !1;
    } else for (var i in e) if (c.call(e, i) && t.call(r, e[i], i, e) === n) return !1;
}

function m() {
    var e = {};
    try {
        var t = getCurrentPages();
        e = t[t.length - 1];
    } catch (e) {
        e = VM2_INTERNAL_STATE_DO_NOT_USE_OR_PROGRAM_WILL_FAIL.handleException(e);
        l(e);
    }
    return e;
}

function v() {
    var e = "未取到";
    try {
        var t = m();
        e = t ? t.route : e;
    } catch (e) {
        e = VM2_INTERNAL_STATE_DO_NOT_USE_OR_PROGRAM_WILL_FAIL.handleException(e);
        l(e);
    }
    return e;
}

function w(e) {
    if ("未取到" === e || !e) return "";
    var t = "";
    try {
        if (__wxConfig) {
            var r = __wxConfig, o = __wxConfig.page || {}, a = o[e] || o[e + ".html"], i = {}, p = {};
            if (r.global && r.global.window && r.global.window.navigationBarTitleText && (i.titleVal = r.global.window.navigationBarTitleText), 
            a && a.window && a.window.navigationBarTitleText && (p.titleVal = a.window.navigationBarTitleText), 
            !p.titleVal && __wxAppCode__) {
                var u = __wxAppCode__[e + ".json"];
                u && u.navigationBarTitleText && (p.titleVal = u.navigationBarTitleText);
            }
            if (d(n.global_title, function(r, n) {
                if (n === e) return t = r;
            }), 0 === t.length) {
                var c = h(i, p);
                t = c.titleVal || "";
            }
        }
    } catch (e) {
        e = VM2_INTERNAL_STATE_DO_NOT_USE_OR_PROGRAM_WILL_FAIL.handleException(e);
        l(e);
    }
    return t;
}

function y() {
    var e = n.preset_properties;
    return new Promise(function(t) {
        wx.getSystemInfo({
            success: function(r) {
                var o, a;
                e.brand = g(r.brand), e.manufacturer = r.brand, e.model = r.model, e.screen_width = Number(r.screenWidth), 
                e.screen_height = Number(r.screenHeight), e.os = "ios" === (a = (o = r.platform).toLowerCase()) ? "iOS" : "android" === a ? "Android" : o, 
                n.wx_sdk_version = r.SDKVersion;
                var i = new Date().getTimezoneOffset(), p = function() {
                    if (wx.getAccountInfoSync) {
                        var e = wx.getAccountInfoSync(), t = e && e.miniProgram ? e.miniProgram : {};
                        return {
                            appId: t.appId,
                            appEnv: t.envVersion,
                            appVersion: t.version
                        };
                    }
                    return {};
                }();
                (function(e) {
                    return "[object Number]" == u.call(e) && /[\d\\.]+/.test(String(e));
                })(i) && (e.timezone_offset = i), p.appId && (e.app_id = p.appId), p.appVersion && (e.app_version = p.appVersion), 
                t();
            }
        });
    });
}

var x, b = {
    appLaunch: function(e, t) {},
    appShow: function(e, t) {},
    appHide: function(e) {},
    pageLoad: function(e) {},
    pageShow: function() {
        !function() {
            var e = {
                route: "直接打开",
                path: "直接打开",
                title: ""
            };
            try {
                var t = m();
                if (t && t.route) {
                    var r = t.sensors_mp_url_query ? "?" + t.sensors_mp_url_query : "", o = t.route, a = w(o);
                    e.route = o + r, e.path = o, e.title = a, n.page_route_map.length >= 2 ? (n.page_route_map.shift(), 
                    n.page_route_map.push(e)) : n.page_route_map.push(e);
                }
            } catch (e) {
                e = VM2_INTERNAL_STATE_DO_NOT_USE_OR_PROGRAM_WILL_FAIL.handleException(e);
                l(e);
            }
        }();
        var e, t = function(e) {
            var t = v(), r = "";
            return f(e) && e.sensors_mp_encode_url_query && (r = e.sensors_mp_encode_url_query), 
            t ? r ? t + "?" + r : t : "未取到";
        }(), r = w(t), o = (e = {
            route: "直接打开",
            path: "直接打开",
            title: ""
        }, n.page_route_map.length > 1 && (e.title = n.page_route_map[0].title, e.route = n.page_route_map[0].route, 
        e.path = n.page_route_map[0].path), e);
        n.preset_properties = h(n.preset_properties, {
            title: r,
            url: t,
            url_path: t,
            referrer: o.path
        }), l(n.preset_properties, "页面加载上报参数"), H.track("pageview", n.preset_properties);
    },
    pageShare: function(e) {},
    pageShareTimeline: function(e) {},
    pageAddFavorites: function() {},
    pageHide: function() {}
}, S = (x = new Date().getTime(), function(e) {
    return Math.ceil((x = (9301 * x + 49297) % 233280) / 233280 * e);
});

function T() {
    if ("function" == typeof Uint32Array) {
        var e = "";
        if ("undefined" != typeof crypto ? e = crypto : "undefined" != typeof msCrypto && (e = msCrypto), 
        f(e) && e.getRandomValues) {
            var t = new Uint32Array(1);
            return e.getRandomValues(t)[0] / Math.pow(2, 32);
        }
    }
    return S(1e19) / 1e19;
}

function A() {
    var e = wx.getStorageSync(o.storage_store_key);
    return e || (e = Date.now() + "-" + Math.floor(1e7 * T()) + "-" + T().toString(16).replace(".", "") + "-" + String(31242 * T()).replace(".", "").slice(0, 8), 
    wx.setStorageSync(o.storage_store_key, e), e);
}

function P(e) {
    k(e, "onLoad", "pageLoad"), k(e, "onShow", "pageShow"), k(e, "onHide", "pageHide"), 
    k(e, "onUnload", "pageHide");
}

function k(e, t, r) {
    var n = b[r];
    if (e[t]) {
        var o = e[t];
        e[t] = function() {
            o.apply(this, arguments), n.apply(this, arguments);
        };
    } else e[t] = function() {
        n.apply(this, arguments);
    };
}

var C, V, j, H = {};

H.setPara = function(e) {
    r.para = function(e) {
        return d(s.call(arguments, 1), function(t) {
            for (var r in t) void 0 !== t[r] && null !== t[r] && (f(t[r]) && f(e[r]) ? h(e[r], t[r]) : e[r] = t[r]);
        }), e;
    }(o, e);
}, H.setUserPhone = function(e) {
    if (!e) return !0;
    n.preset_properties.phone_number = e;
}, H.track = function(t, r) {
    try {
        !function(e) {
            f(e) ? wx.request({
                url: o.server_url,
                method: "POST",
                dataType: "text",
                data: e,
                success: function() {
                    l("数据上报成功");
                },
                fail: function() {
                    l("数据上报失败");
                }
            }) : l("数据上报失败");
        }(e({
            event: t
        }, h(r, n.preset_properties)));
    } catch (e) {
        e = VM2_INTERNAL_STATE_DO_NOT_USE_OR_PROGRAM_WILL_FAIL.handleException(e);
        lod(e);
    }
}, H.setProfile = function(e) {
    return n.preset_properties = h(n.preset_properties, e);
}, H.init = function() {
    n.preset_properties.distinct_id = A();
    var e = [ new Promise(function(e, t) {
        wx.getNetworkType({
            success: function(t) {
                n.preset_properties.network_type = g(t.networkType), e();
            },
            fail: function(e) {
                l("获取网络信息失败", e), t();
            }
        });
    }), y() ].concat(n.promise_list);
    Promise.all(e).then(function() {
        console.log("返回基本信息可以准备发请求了。", n.preset_properties);
    }).catch(function() {});
}, V = Page, j = Component, Page = function(e) {
    try {
        e || (e = {}), P(e), V.apply(this, arguments);
    } catch (e) {
        e = VM2_INTERNAL_STATE_DO_NOT_USE_OR_PROGRAM_WILL_FAIL.handleException(e);
        V.apply(this, arguments);
    }
}, Component = function(e) {
    try {
        e || (e = {}), P(e.methods), j.apply(this, arguments);
    } catch (e) {
        e = VM2_INTERNAL_STATE_DO_NOT_USE_OR_PROGRAM_WILL_FAIL.handleException(e);
        j.apply(this, arguments);
    }
}, C = App, App = function(e) {
    return e[o.name] = H, C(e);
}, wx.onAppShow(function() {
    b.appShow();
}), wx.onAppHide(function() {
    l("wx.hide"), b.appHide();
}), function() {
    try {
        var e = wx.setNavigationBarTitle;
        Object.defineProperty(wx, "setNavigationBarTitle", {
            get: function() {
                return function(t) {
                    var r = v();
                    t = f(t) ? t : {}, n.global_title[r] = t.title, e.call(this, t);
                };
            }
        });
    } catch (e) {
        e = VM2_INTERNAL_STATE_DO_NOT_USE_OR_PROGRAM_WILL_FAIL.handleException(e);
        l(e);
    }
}(), module.exports = H;