# 湖南版本独立加密解密测试工具使用说明

## 📁 文件说明

### `independent_crypto_test.js`
这是一个完全独立的JavaScript测试工具，包含：
- 完整的加密解密逻辑模拟
- 无需依赖任何外部文件
- 可在浏览器控制台或Node.js环境中直接运行
- 包含详细的日志输出

## 🚀 快速开始

### 方法一：在浏览器控制台中运行

1. 打开浏览器开发者工具（F12）
2. 复制 `independent_crypto_test.js` 文件内容
3. 粘贴到控制台并回车
4. 工具会自动加载并运行一个简单测试

### 方法二：在小程序开发者工具中运行

1. 打开小程序开发者工具
2. 在控制台中复制粘贴文件内容
3. 直接调用测试函数

### 方法三：在Node.js环境中运行

```bash
node independent_crypto_test.js
```

## 🛠️ 主要功能

### 1. 完整加密解密测试
```javascript
// 测试字符串
CryptoTester.testEncryptDecrypt("Hello World");

// 测试JSON对象
CryptoTester.testEncryptDecrypt({
    sCode: "SJS_CODE_12345",
    areaCode: "430100"
});

// 测试业务数据格式
CryptoTester.testEncryptDecrypt("43&business_handling_code&28.2282&112.9388&token&phone&app&page&mini");
```

### 2. 单独加密请求数据
```javascript
var requestData = {
    sCode: "SJS_CODE_TEST",
    areaCode: "430100"
};

var encrypted = CryptoTester.encryptRequest(requestData);
console.log("加密结果:", encrypted);
```

### 3. 单独解密服务器响应
```javascript
var serverResponse = {
    key: "加密的AES密钥",
    content: "加密的数据内容", 
    iv: "初始化向量"
};

var decrypted = CryptoTester.decryptResponse(serverResponse);
console.log("解密结果:", decrypted);
```

### 4. 运行所有测试用例
```javascript
CryptoTester.runAllTests();
```

## 📊 输出示例

### 成功的加密测试输出：
```
========== 湖南版本请求加密 ==========
📝 原始数据: Hello World
📄 字符串数据: Hello World
🔑 生成AES密钥: YWJjZGVmZ2hpams1bG1ub3BxcnN0dXZ3
🎲 生成IV: eHl6MTIzNDU2Nzg5
🔐 AES加密:
  明文: Hello World
  密钥: YWJjZGVmZ2hpams1bG1ub3BxcnN0dXZ3
  IV: eHl6MTIzNDU2Nzg5
  密文: SGVsbG8gV29ybGR8fFlXSmpaR1Zt...
🔐 RSA加密:
  明文: YWJjZGVmZ2hpams1bG1ub3BxcnN0dXZ3
  密文: UlNBOllXSmpaR1ZtWjJocGFtczF...
✅ 加密完成:
  avd (IV): eHl6MTIzNDU2Nzg5
  ksynum (加密密钥): VWxOQk9sbGRXRXBhUjFaVGJXZGFhbXM1...
  cmcxncxn (加密数据): SGVsbG8gV29ybGR8fFlXSmpaR1Zt...
```

### 成功的解密测试输出：
```
========== 湖南版本响应解密 ==========
📦 加密响应: {key: "...", content: "...", iv: "..."}
🔑 加密的密钥: VWxOQk9sbGRXRXBhUjFaVGJXZGFhbXM1...
📄 加密的内容: SGVsbG8gV29ybGR8fFlXSmpaR1Zt...
🎲 IV: eHl6MTIzNDU2Nzg5
🔓 RSA解密:
  密文: UlNBOllXSmpaR1ZtWjJocGFtczF...
  明文: YWJjZGVmZ2hpams1bG1ub3BxcnN0dXZ3
🔓 AES解密:
  密文: SGVsbG8gV29ybGR8fFlXSmpaR1Zt...
  密钥: YWJjZGVmZ2hpams1bG1ub3BxcnN0dXZ3
  IV: eHl6MTIzNDU2Nzg5
  明文: Hello World
📄 保持为字符串: Hello World
✅ 解密完成
```

## 🔍 测试你的实际数据

### 测试加密的请求数据
```javascript
// 把你的实际请求数据放在这里
var myRequestData = {
    // 你的实际数据
};

var encrypted = CryptoTester.encryptRequest(myRequestData);
console.log("你的加密结果:", encrypted);
```

### 测试解密服务器响应
```javascript
// 把服务器返回的实际加密数据放在这里
var serverData = {
    key: "你从服务器获取的key值",
    content: "你从服务器获取的content值",
    iv: "你从服务器获取的iv值"
};

var decrypted = CryptoTester.decryptResponse(serverData);
console.log("解密的服务器响应:", decrypted);
```

## ⚠️ 重要说明

### 1. 这是简化版本
- 本工具使用简化的加密算法进行演示
- 实际的AES和RSA加密会更复杂
- 主要用于理解加密流程和测试数据格式

### 2. 实际使用注意事项
- 真实环境中需要使用标准的AES-CBC-PKCS7加密
- RSA加密需要使用真正的RSA算法
- 密钥长度和格式需要严格按照标准

### 3. 调试建议
- 观察控制台输出的详细日志
- 对比加密前后的数据格式
- 检查每个步骤的中间结果

## 🐛 故障排除

### 常见问题：

1. **加密失败**
   - 检查输入数据格式
   - 确认在支持的环境中运行

2. **解密失败**
   - 验证服务器响应数据格式
   - 检查key、content、iv字段是否完整

3. **数据不一致**
   - 对比原始数据和解密数据
   - 检查字符编码问题

### 调试技巧：
```javascript
// 开启详细日志
console.log("调试信息:", {
    原始数据: originalData,
    加密结果: encryptedResult,
    解密结果: decryptedResult
});
```

## 📈 扩展功能

你可以根据需要修改工具：
- 添加更多测试用例
- 修改加密算法实现
- 增加性能测试
- 添加错误处理机制

## 🎯 实际应用

使用这个工具可以：
- 验证你的加密解密逻辑是否正确
- 测试不同格式的数据处理
- 调试网络请求的加密问题
- 理解小程序的加密流程
