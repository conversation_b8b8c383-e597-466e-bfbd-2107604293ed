var e = require("../../utils/http.js"), t = require("../../utils/transmission.js").rsaAesJs, n = require("../../utils/util.js"), o = n.unction, a = n.delayAwait, i = getApp();

Page({
    data: {
        token: "",
        url: "",
        latitude: "",
        longitude: "",
        timer: null,
        timerText: "",
        source: "",
        signature: "",
        encrypt: ""
    },
    onLoad: function() {
        var e = this;
        wx.setStorageSync("getRestOilDrop", "1"), wx.setStorageSync("m", ""), wx.setStorageSync("ip_hn", "");
        var t = wx.getStorageSync("provinceCode");
        [ "selectTextContentByCode", "selectAllBannerList", "selectHomeSecondZoneGoodsList", "selectCouponSpeicalZone", "selectAllNoticeList", "privilegeSetting", "selectBasicPrivilegeSetting", "selectBusinessDetails", "getPersonCenterMenu", "getBotton", "getTopList" ].forEach(function(e) {
            wx.setStorageSync(e + t, "");
        }), wx.setStorageSync("isLoginTo", !1);
        var n = wx.getLaunchOptionsSync();
        a(function() {
            return !0 === i.globalData.nowTimeCompleted;
        }).then(function() {
            if (e.verification(), n.query.hcsh && "1" == n.query.hcsh) e.getLocationStr(); else if (null != n.referrerInfo.extraData) {
                var t = n.referrerInfo.extraData.source;
                if (console.log("source1=" + t), "HAC" == t) e.getLocationStr(); else {
                    null != t && "undefined" != t || (t = "WeChat_Official");
                    var o = n.referrerInfo.extraData.signature, a = n.referrerInfo.extraData.encrypt;
                    e.setData({
                        source: t,
                        signature: o,
                        encrypt: a
                    }, function() {
                        e.handleLogin();
                    });
                }
            } else e.getLocationStr();
        });
    },
    handleLogin: o(function() {
        var n = this, o = {
            source: n.data.source,
            signature: n.data.signature,
            encrypt: n.data.encrypt
        }, a = JSON.stringify(o);
        console.log("handleLogin:" + a), (0, e.POSTHN)("/singleLogin/thirdPlatFormLogin", a).then(function(o) {
            if (console.log("----登录--thirdPlatFormLogin-", o), o && o.successful) {
                if (wx.setStorageSync("isSuperVip", o.data.isSuperVip), wx.setStorageSync("provinceListVip", o.data.provinceList), 
                wx.setStorageSync("userOutLogin", "0"), wx.setStorageSync("provinceName", o.data.provinceName), 
                wx.setStorageSync("provinceCode", o.data.provinceCode), wx.setStorageSync("provinceCenter", o.data.provinceCenter), 
                wx.setStorageSync("loginStatus_hn", "1"), wx.setStorageSync("memberTypeList_hn", o.data.userCodeList), 
                wx.setStorageSync("mbmsAccessToken", o.data.mbmsAccessToken), wx.setStorageSync("mbmsRefreshToken", o.data.mbmsRefreshToken), 
                0 == o.data.userCodeList.length) wx.setStorageSync("userTypeIndex", 0), wx.setStorageSync("vipType_hn", o.data.defaultVipType), 
                wx.setStorageSync("vipId_hn", "*"), wx.setStorageSync("feeAppCode_hn", o.data.defaultMemberGrade); else {
                    wx.setStorageSync("userTypeIndex", 0), wx.setStorageSync("vipType_hn", o.data.userCodeList[0].vipType), 
                    wx.setStorageSync("vipId_hn", o.data.userCodeList[0].codeId), wx.setStorageSync("feeAppCode_hn", o.data.userCodeList[0].feeAppCode);
                }
                wx.setStorageSync("token_hn", o.data.token), wx.setStorageSync("telephone_hn", o.data.telephone), 
                wx.setStorageSync("cityCode_hn", o.data.cityCode), wx.setStorageSync("cityName_hn", o.data.cityName), 
                wx.setStorageSync("defaultCityName", o.data.defaultCityName), wx.setStorageSync("defaultCityId", o.data.defaultCityId), 
                wx.setStorageSync("isLoginTo", !0);
                var a = o.data.provinceCode;
                [ "selectTextContentByCode", "selectAllBannerList", "selectHomeSecondZoneGoodsList", "selectCouponSpeicalZone", "selectAllNoticeList", "privilegeSetting", "selectBasicPrivilegeSetting", "selectBusinessDetails", "getPersonCenterMenu" ].forEach(function(e) {
                    wx.setStorageSync(e + a, "");
                }), n.getLocationStr();
                var i = t.EncryptDataHn(o.data.token);
                (0, e.POSTHN)("/newUser/notifyLoginIsSuccess", i).then(function(e) {
                    console.log("----调试数据--9999-", e);
                });
            }
        });
    }),
    verification: function() {
        wx.getStorage({
            key: "token_hn",
            success: function(e) {
                i.verificationTokenHN(!1).then(function(e) {
                    console.log("---", e);
                });
            }
        });
    },
    wxGetLocation: function() {
        var e = this;
        wx.getLocation({
            type: "gcj02",
            timeout: 3e3,
            success: function(t) {
                var n = t.latitude, o = t.longitude;
                e.setData({
                    latitude: n,
                    longitude: o
                }), e.getContent();
            },
            fail: function() {
                e.getContent();
            }
        });
    },
    getLocationStr: function() {
        var e = this;
        wx.getSetting({
            success: function(t) {
                -1 === [ void 0, !0 ].indexOf(t.authSetting["scope.userLocation"]) ? wx.showModal({
                    title: "请求授权当前位置",
                    content: "需要获取您的地理位置，请确认授权",
                    success: function(t) {
                        t.cancel ? wx.showToast({
                            title: "拒绝授权",
                            icon: "none",
                            duration: 1e3
                        }) : t.confirm && wx.openSetting({
                            success: function(t) {
                                1 == t.authSetting["scope.userLocation"] ? (wx.showToast({
                                    title: "授权成功",
                                    icon: "success",
                                    duration: 1e3
                                }), e.setData({
                                    storeList: [],
                                    pageNo: 1
                                }), e.wxGetLocation()) : wx.showToast({
                                    title: "授权失败",
                                    icon: "none",
                                    duration: 1e3
                                });
                            }
                        }), e.getContent();
                    }
                }) : e.wxGetLocation();
            },
            fail: function() {
                wx.showToast({
                    title: "调用失败请稍后再试",
                    icon: "none"
                });
            }
        });
    },
    getContent: function() {
        var t = this, n = 2, o = this.data, a = o.latitude, s = o.longitude;
        this.setData({
            timerText: n + "s"
        }), wx.setStorageSync("latitude", a), wx.setStorageSync("longitude", s);
        var c = {
            lat: a,
            lng: s
        };
        console.log("-地址obj--", c), (0, e.POSTHN)("/getCoupon/getCityInfoByLonAndLat", c).then(function(e) {
            console.log("-地址--", e), wx.setStorageSync("userOutLogin", "0");
            var t = wx.getStorageSync("provinceCode"), n = wx.getStorageSync("loginStatus_hn"), o = e.isDefault, a = e.provinceName, i = e.provinceCode, s = e.cityName, c = e.cityCode, r = e.defaultMemberGrade;
            ("yes" === o && [ "", void 0, null ].indexOf(t) > -1 || "no" === o && "1" !== n) && [ [ "provinceName", a ], [ "provinceCode", i ], [ "cityCode_hn", c ], [ "cityName_hn", s ], [ "defaultCityName", s ], [ "defaultCityId", c ], [ "feeAppCode_hn", r ] ].forEach(function(e) {
                wx.setStorageSync(e[0], e[1]);
            });
        }), this.data.timer = setInterval(function() {
            (n--, t.setData({
                timerText: n + "s"
            }), n <= 0) && (clearInterval(t.data.timer), "" == wx.getStorageSync("ip_hn") && i.globalData.isWorkTime && wx.switchTab({
                url: "/pages/index/index"
            }));
        }, 1e3);
    },
    onReady: function() {},
    onShow: function() {},
    onHide: function() {},
    onUnload: function() {},
    onPullDownRefresh: function() {},
    onReachBottom: function() {},
    onShareAppMessage: function() {}
});