var style = require('p_./miniprogram_npm/@vant/weapp/wxs/style.wxs')();
var addUnit = require('p_./miniprogram_npm/@vant/weapp/wxs/add-unit.wxs')();

function spinnerStyle(data) {
    return (style(({
        color: data.color,
        width: addUnit(data.size),
        height: addUnit(data.size),
    })))
};

function textStyle(data) {
    return (style(({
        'font-size': addUnit(data.textSize),
    })))
};
module.exports = ({
    spinnerStyle: spinnerStyle,
    textStyle: textStyle,
});