<view style="height:{{height}}px;" wx:if="{{fixed&&placeholder}}"></view>
<view class="{{utils.bem( 'nav-bar',{fixed:fixed} )}} custom-class {{border?'van-hairline--bottom':''}}" style="{{computed.barStyle( {zIndex:zIndex,statusBarHeight:statusBarHeight,safeAreaInsetTop:safeAreaInsetTop} )}};{{customStyle}}">
    <view class="van-nav-bar__content">
        <view bind:tap="onClickLeft" class="van-nav-bar__left">
            <block wx:if="{{leftArrow||leftText}}">
                <van-icon customClass="van-nav-bar__arrow" name="arrow-left" size="16px" wx:if="{{leftArrow}}"></van-icon>
                <view class="van-nav-bar__text" hoverClass="van-nav-bar__text--hover" hoverStayTime="70" wx:if="{{leftText}}">{{leftText}}</view>
            </block>
            <slot name="left" wx:else></slot>
        </view>
        <view class="van-nav-bar__title title-class van-ellipsis">
            <block wx:if="{{title}}">{{title}}</block>
            <slot name="title" wx:else></slot>
        </view>
        <view bind:tap="onClickRight" class="van-nav-bar__right">
            <view class="van-nav-bar__text" hoverClass="van-nav-bar__text--hover" hoverStayTime="70" wx:if="{{rightText}}">{{rightText}}</view>
            <slot name="right" wx:else></slot>
        </view>
    </view>
</view>
<wxs module="utils" src="../wxs/utils.wxs" />
<wxs module="computed" src="index.wxs" />