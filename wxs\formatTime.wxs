var filter = ({
    getDateTime: (function(value) {
        var time = getDate(value);
        var year = time.getFullYear();
        var month = time.getMonth() + 1;
        var date = time.getDate();
        month = month < 10 ? "0" + month : month;
        date = date < 10 ? "0" + date : date;
        return (year + "-" + month + "-" + date)
    }),
    timestampToTime: (function(timestamp) {
        var date = getDate(timestamp);
        var Y = date.getFullYear() + '-';
        var M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
        var D = date.getDate() + ' ';
        var h = date.getHours() + ':';
        var m = date.getMinutes() + ':';
        var s = date.getSeconds();
        return (Y + M + D + h + m + s)
    }),
    timestampToTime1: (function(timestamp) {
        var date = getDate(timestamp);
        var h = (date.getHours() < 10 ? '0' + date.getHours() : date.getHours()) + ':';
        var m = (date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()) + ':';
        var s = (date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds());
        return (h + m + s)
    }),
    getStr: (function(value) {
        return (value.replace("_", ""))
    }),
    stringToLong: (function(strTime) {
        Date;
        date = stringToDate(strTime, "yyyy-MM-dd HH:mm:ss");
        if (date == null) {
            return (0)
        } else {
            long;
            currentTime = dateToLong(date);
            return (currentTime)
        }
    }),
});
module.exports = ({
    getDateTime: filter.getDateTime,
    timestampToTime: filter.timestampToTime,
    timestampToTime1: filter.timestampToTime1,
    stringToLong: filter.stringToLong,
    getStr: filter.getStr,
});