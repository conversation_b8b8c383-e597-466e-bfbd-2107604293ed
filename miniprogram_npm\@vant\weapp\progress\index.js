var e = require("../../../../@babel/runtime/helpers/slicedToArray"), t = require("../common/component"), o = require("../common/color"), r = require("../common/utils");

(0, t.VantComponent)({
    props: {
        inactive: <PERSON>olean,
        percentage: {
            type: Number,
            observer: "setLeft"
        },
        pivotText: String,
        pivotColor: String,
        trackColor: String,
        showPivot: {
            type: Boolean,
            value: !0
        },
        color: {
            type: String,
            value: o.BLUE
        },
        textColor: {
            type: String,
            value: "#fff"
        },
        strokeWidth: {
            type: null,
            value: 4
        }
    },
    data: {
        right: 0
    },
    mounted: function() {
        this.setLeft();
    },
    methods: {
        setLeft: function() {
            var t = this;
            Promise.all([ (0, r.getRect)(this, ".van-progress"), (0, r.getRect)(this, ".van-progress__pivot") ]).then(function(o) {
                var r = e(o, 2), i = r[0], n = r[1];
                i && n && t.setData({
                    right: n.width * (t.data.percentage - 100) / 100
                });
            });
        }
    }
});