var object = require('p_./miniprogram_npm/@vant/weapp/wxs/object.wxs')();
var array = require('p_./miniprogram_npm/@vant/weapp/wxs/array.wxs')();

function kebabCase(word) {
    var newWord = word.replace(getRegExp("[A-Z]", 'g'), (function(i) {
        return ('-' + i)
    })).toLowerCase();
    return (newWord)
};

function style(styles) {
    if (array.isArray(styles)) {
        return (styles.filter((function(item) {
            return (item != null && item !== '')
        })).map((function(item) {
            return (style(item))
        })).join(';'))
    };
    if ('Object' === styles.constructor) {
        return (object.keys(styles).filter((function(key) {
            return (styles[((nt_0 = (key), null == nt_0 ? undefined : 'number' === typeof nt_0 ? nt_0 : "" + nt_0))] != null && styles[((nt_1 = (key), null == nt_1 ? undefined : 'number' === typeof nt_1 ? nt_1 : "" + nt_1))] !== '')
        })).map((function(key) {
            return ([kebabCase(key), [styles[((nt_2 = (key), null == nt_2 ? undefined : 'number' === typeof nt_2 ? nt_2 : "" + nt_2))]]].join(':'))
        })).join(';'))
    };
    return (styles)
};
module.exports = style;