Object.defineProperty(exports, "__esModule", {
    value: !0
}), exports.rsaAesJs = void 0;

var e = require("./wx_rsa"), A = require("./aesFunction").AESUtil;

require("./aes"), exports.rsaAesJs = {
    DecryptKey: function(t) {
        var a = new e.RSAKey();
        a = e.KEYUTIL.getKey("-----BEGIN PRIVATE KEY-----\n    MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCUphDT9zC0Zen/e9HTtucsGM7YvRUvY8Uhj5jVUy2iH+DTguOf3QcNYRgrQcogplIT6f+WFKfZ/NmjGZIegVgivFp4UpJmqPOaz7LbddkJGDTT9QbWXEaKVUXqCr49wTgpF2h3j8kd4SpNekJE/kbwFIFQBUJG1Va/3+JnrDsfKZ7SiE+7nAO/GRLsfTBRCh6zpATyC9pB3I3ixMyHKkhg/dXeSo/HaYxXso3izv1BuQ2OZSxq1Wq8auuZfXKtki8saoy+1hO8ajtnv0S+CPOzpfJvmuaN5otsyVXlsJJ8Mdppy1v2J6/zqvFb2bSHYhoRotlO9BI/QAxPjBVkYQd5AgMBAAECggEAfwPTNzttq4e1xW9IoI4Vw/FCtaIDeyXWKNIpdoaHZJO2qSSbWVskjoMbgsLJctKLKab1lIj7z+ElGh9cYqKE69FjwJJVtFDDLtIwcyOkmwfLBB3HhNz+DW/FuLELU3tJgJsW0arT36spWWCRRaC8fqIWyIhl1F6wrG0iaRGaxwmY+6FaZ7RpQSw7kc/rtiprZbo+l0/zPThML7ki1l4nPKqmqugvsp5PuwuBFNxb331FsjyG2GY294K9XZeJs7LXS/Xv+7+n0JVPyen9WgN+r8VMiGanEy4pw4zaTEmHghrPbq9xEQLN08JzJQpnisVbjHFtCVw6oe2Vt8GK6T0FXQKBgQDjT1jIWb+IT6YN15gOr7vr9OayvOf6YxxGi5SR74IRipc65oMl7Ie4xwpAkZVkkWQnTLJlFfAHaGj+tQ72a2DqI2Cp29Ydwqn2FZZJmNlFgWDXED89qwiAaufqpiY3WGTMld1YTIywQrdeH7tOBUE+oWs3PGRwXJkW6qYMZNBpcwKBgQCnaRSvTOkumECbPedgztd4DZYEtXNoOB35eClF5mftRzU47Lr0D9H3XOlTnE9eABzroo3Ny8vSi4xS5kPkKvwoaya7lj3D3JIOQY2EN6fmGaODBOaV8nuIIq2ltBZa2VRi9MyqkPheOQ/1x9gt6rKRatiNeWjhNGKduYed4AbAYwKBgQDUWcPvJyivBvlx5P7AeZttCxSZ1bBBUWWQ4cShsMYp7Pbqp33NZ/JCxG7ncxnFIa4WqKffl2btBLVRfYPE4PtgwDwFcsHMe5abtf74MjqHnB4Dy8HIsob2sOiHZUA+Y+2G0lo9qwWjhDAZVjt835vcDqBCscCC136M0hjdazDn0QKBgQCIbiF2T0aFO30DRuqmQUrlwqSsh7daiskawmtYVNIDVBGyp3Igbjgdz/Eyu2MO5yaqfZn0yg38Ms+Ty6Yubz16LwCsxV7mnH3G8nNC+hxxcBmKL9c/AhLcOe6r6NyDAzQ7ROb6ZhSSrQwtr8Skfu0xhxKeEQCA77Eies1jwuudowKBgD0UCgUv8sv/Z+E6vApTRdDf/vTiRHkh2s2D42FbB15rTVO3T1yC8/zxzM0C7VAH9A31EPL3lNd9ZoDbfcK8hrlqLMkhcCQk9pR10lbUDhgiUdiODEEdvq6LpwxhoqOhQXLf8/9OguE4hwkuxTOtfxVFS9wB018sG0VULhxHPlDk-----END PRIVATE KEY-----");
        var B = e.b64tohex(t.key), E = a.decrypt(B), I = A.decrypt(t.content, E, t.iv);
        return JSON.parse(I);
    },
    EncryptData: function(t) {
        var a = A.getAesKey(), B = A.getIv(), E = A.encrypt(t, a, B), I = (new e.RSAKey(), 
        e.KEYUTIL.getKey("-----BEGIN PUBLIC KEY-----MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA3wzK0OjysbU742VeREaGU9QSf/+T6OVTXKMY1qOOqUdP3yK0HaXbqAXIuIIOoy2vCsQU7KKs5RGbPVskjq8mkPJWxvuDayuATJl8OUdWnwQthcDwuJD1n//Bz9oVSJCIZ2So8OdqgcqELY715nLU5fpEnyk9QgIzjCjaZkOLd5gdzD2rUItCEt4WPGTAWNLMQmvI5Kvma0Ndrh1dGJlFxN5osDA60cSYThaoxL9pSpUv86XIB3QZTEn7wPXUe/roFouixs470k33kfK3UgyqDbVtL1EgYUpzL/xiF/HA5w7mXuB26Tuc1JaQ21BHqlsjbmaoaKtDylxWrZtUmmolGQIDAQAB-----END PUBLIC KEY-----").encrypt(a));
        return {
            avd: B,
            ksynum: I = e.hex2b64(I),
            cmcxncxn: E
        };
    },
    DecryptKeyHn: function(t) {
        var a = new e.RSAKey();
        a = e.KEYUTIL.getKey("-----BEGIN PRIVATE KEY-----MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQCAGbO/XDMBn9n56a3YWf47b//8QldG5hBUF0+aUSadgueMXiB78jK8A+dzktR7EPC3O8p5bS9BhY957zlwF+zTNqHkoLdMJIQsSBUXylN0ZKTUEda/iSHLle9rlqVHhabWoVq9TNl6/nQP9iBO0nVZuuf8P626M69DjaSkOkw5R5/1XYlIpyak6DyjfBdqKD6v2lefLry7LAOF893Q/09anyZ3TKrlHah09fbX6XbEfxkxqDAYzT0HIAh7lekML0d4Tekhrxrl+5IxKc6kSHHHP9GaE7GOMFBXWfdUoOj2FizHdpYsVvUvsFEBmq2MM2hHuh78/dSLeYMlPdtaYQfrAgMBAAECggEADK5+tPsTsZ15ntwbcce2+3tjBD4tFb/A7hJAty5BfGZxNdw7qwmXLDXWvAJVNrm+muU4bjz46KuoQJIGRaqYr3BBJrkibE9RJnEcZXaKPIOaLcR2Vg/lYJo/UY9gXWxF0Am5SfRJs3ttUgczu5nqNC/kt3w2XrCvMSjcm8WJtkHVHK2C09GuiDA8OJcsf8khOgbxqOwLom7gzbCSWaICcAziz2OHYyRK6EMUNwtibuI4mxKut1j15MEGHY/J4WqT8Jnh5KvBaGXsyv0Dim2Wl5N4CZwwhgQOpuHnxGFz2MWI/L//xDYUAF0KiE08911LFheCuiJ3afLvqWmDfz6nAQKBgQC6zJAcrpZ3EZDNKYH3i1fL313gdtiUgLSXYdjS26vfKBR1IOMsXqqCFxekgGI2vccFGZathIzIEf+aC4WBEmC/HEyFTVkHWXStdnRVfwmEAAuz4wB4s/E4oDNEh0F02m03vMD47xFcSSkRvliB+xqGS1zjWC+W8xLY43PLQCm3kQKBgQCvjldjFFz7k2PjSn6AMAhHwpcmq20FQAyaDA+tb6E+34zKGyI0FPuXgj+vD+OuGhs3PS++KJ/lG109kPmzXvUlv4YvK93ixgDZVF0lhIfOUvWhdeHlHZ3sCdT+8aAnoXH2h6v3WhT6L2eRH5kVmssiPy57qGN8jZNs2PGkbrphuwKBgAfNHUOlYvFMEYEP97aP4jXIeVIzhG4HRgOAeZ6sv+vM77XIjtQ+i95s+nbLSc7BdDtZSzqp63s7jTIwwv2V9+ZjVyrGBS7SZJYN+WPMr2gM3HBBzbY8xZk4z4tQHsKzkJYgUurSiSYShB2Sln2vL/dl1FZGS46BTksyjJZzcoqRAoGAfknuUUcv0IZp3/MR51MmUcEJDPda2OEHb7XnfUOFgn48gJNeWosRph87gsryZadAFs+8TZZZ1+2EeKqXBlL0FXCercZnux/nMnA/BaM+Ms15B+qOU0yn80nrYG8fYAMCwW895RV3a7a4USyi6mjNYliDLs+mqc4K1M2Hn1tMVssCgYBVw2RAtx5ZEDLOIBaIUCDww9ds8i5tzo7UTz9aPgXedTHgTzFb4hAucSocuSsIJO0qS3qoM24FD7YmmBekiZEDle84BNk6SjaRoPy4PJ6v77VR6ceb2BR7FGfseaACJbH1++TfaKrHA32ubxcmehqU10dUZJ/tOuErSklQzfehGw==-----END PRIVATE KEY-----");
        var B = e.b64tohex(t.key), E = a.decrypt(B), I = A.decrypt(t.content, E, t.iv);
        return JSON.parse(I);
    },
    EncryptDataHn: function(t) {
        var a = A.getAesKey(), B = A.getIv(), E = A.encrypt(t, a, B), I = (new e.RSAKey(), 
        e.KEYUTIL.getKey("-----BEGIN PUBLIC KEY-----MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAu1l6RjXiJBajEvWqcymhwlBX55m3HFUaGC7F2G51ytNJwsFON9jJqeAwB83+uUVJYM15MrBBMWAul0lGSmoRMsVnT1KR7p7UxnbGgdZT1NJNZgTu1J16MV98EUhImSk8jxrmoRdUGgDE1vvvlcOack8+xDDrF9x6tIl1Kyr7uFz1VXD17LTwJ5ajRtkp5Tszl+xs0fqKhxbXVknqCMGQcW9IGwNrr4/coaP+SmEUkIUQ5jKr69PI2ATjXghC7SUIfVOpplM0AV0PV7qvPYysyAXMjjieQ98jIOOYrEo7ay6qhtgiCiVr7k+bBi3gv9+2vlpMhPjdkAaIPwEnE40FOwIDAQAB-----END PUBLIC KEY-----").encrypt(a));
        return {
            avd: B,
            ksynum: I = e.hex2b64(I),
            cmcxncxn: E
        };
    }
};