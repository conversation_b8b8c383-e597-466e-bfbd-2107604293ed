var e = "https://czfw.12580.com/hczh-czfw-all", t = require("./transmission.js").rsaAesJs;

function o(o) {
    var n = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {}, s = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : "GET", r = "application/json", i = "0K24bZFGGMk6jpAXEny6w7BvjQOgH3Ieu2ehHm4LKzbH7L", u = wx.getStorageSync("token_hn"), a = {
        "Content-Type": r,
        Authorization: i
    };
    return null != u && "" != u && null != u && (a = {
        "Content-Type": r,
        Authorization: i,
        Token: u
    }), new Promise(function(r, i) {
        wx.request({
            url: e + o,
            data: n,
            method: s,
            header: a,
            timeout: 2e4,
            success: function(e) {
                if (e.data) if (200 == e.statusCode) {
                    "text/html; charset=uft-8" === e.header["Content-Type"] && console.log("304----", e.data);
                    var n = t.DecryptKeyHn(e.data);
                    if ("-300" == n.resultCode) wx.setStorageSync("loginStatus_hn", "0"), wx.setStorageSync("token_hn", ""), 
                    wx.setStorageSync("provinceName", ""), wx.showToast({
                        title: n.resultMsg,
                        icon: "none"
                    }); else if ("403" == n.resultCode) {
                        wx.setStorageSync("ip_hn", n.resultMsg);
                        var s = new Date().getMinutes().toString();
                        return s != wx.getStorageSync("m") && (wx.setStorageSync("m", s), wx.navigateTo({
                            url: "/packagehn/pages/errProhibithn/errProhibit"
                        }), !1);
                    }
                    if (n) {
                        if (500 == n.resultCode) return console.log("--解密失败-"), "/getCoupon/userGetCouponByVipIdNew" == o || "/checkCode/tencentCloudCheckCode" == o || "/getCoupon/userGetGiftBagCouponByVipId" == o ? wx.showToast({
                            title: "亲，当前领券人数较多，建议您稍后再试哦！",
                            icon: "none",
                            duration: 3e3
                        }) : "/newUser/getRestOilDrop" != o && "/newUser/selectUserInfoByToken" != o && wx.showToast({
                            title: "亲，系统开小差了哦！请稍后再试，或联系客服！",
                            icon: "none",
                            duration: 3e3
                        }), r(n.resultMsg), !1;
                        r(n);
                    }
                } else "/getCoupon/userGetCouponByVipIdNew" == o || "/checkCode/tencentCloudCheckCode" == o || "/getCoupon/userGetGiftBagCouponByVipId" == o ? wx.showToast({
                    title: "亲，当前领券人数较多，建议您稍后再试哦！",
                    icon: "none",
                    duration: 3e3
                }) : "/newUser/getRestOilDrop" != o && "/newUser/selectUserInfoByToken" != o && wx.showToast({
                    title: "亲，系统开小差了哦！请稍后再试，或联系客服！",
                    icon: "none",
                    duration: 3e3
                }), i("请求失败：" + e), console.log("--请求失败-"); else "/getCoupon/userGetCouponByVipIdNew" == o || "/checkCode/tencentCloudCheckCode" == o || "/getCoupon/userGetGiftBagCouponByVipId" == o ? wx.showToast({
                    title: "亲，当前领券人数较多，建议您稍后再试哦！",
                    icon: "none",
                    duration: 3e3
                }) : "/newUser/getRestOilDrop" != o && "/newUser/selectUserInfoByToken" != o && wx.showToast({
                    title: "亲，系统开小差了哦！请稍后再试，或联系客服！",
                    icon: "none",
                    duration: 3e3
                }), r(e.data);
            },
            fail: function(e) {
                "/getCoupon/userGetCouponByVipIdNew" == o || "/checkCode/tencentCloudCheckCode" == o || "/getCoupon/userGetGiftBagCouponByVipId" == o ? wx.showToast({
                    title: "亲，当前领券人数较多，建议您稍后再试哦！",
                    icon: "none",
                    duration: 3e3
                }) : "/newUser/getRestOilDrop" != o && "/newUser/selectUserInfoByToken" != o && wx.showToast({
                    title: "亲，系统开小差了哦！请稍后再试，或联系客服！",
                    icon: "none",
                    duration: 3e3
                }), r();
            },
            complete: function() {}
        });
    });
}

module.exports = {
    baseUrlImg: "https://czfw.12580.com/file/",
    GETHN: function(e) {
        var t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {};
        return o(e, t, "GET");
    },
    POSTHN: function(e) {
        var t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {};
        return o(e, t, "POST");
    },
    baseUrlHN: e
};