var t = require("../../../utils/http.js"), n = require("../../../utils/transmission.js").rsaAesJs, e = getApp();

Page({
    data: {
        isLoading: !1,
        rescueT: {}
    },
    onLoad: function() {
        wx.showShareMenu({
            withShareTicket: !0
        }), this.rescueText();
    },
    rescueText: function() {
        var e = this;
        this.setData({
            isLoading: !0
        });
        var o = 43, a = wx.getStorageSync("latitude"), c = wx.getStorageSync("longitude"), i = wx.getStorageSync("token_hn");
        "" == i && (i = "*"), "" != o && null != o || (o = "*");
        var s = "".concat(o, "&").concat("registration_agreement_content", "&").concat(a, "&").concat(c, "&").concat(i);
        console.log(s);
        var r = n.EncryptDataHn(s);
        (0, t.POSTHN)("/activityRulesInfo/selectTextContentByCode", r).then(function(t) {
            console.log(t), e.setData({
                rescueT: t.data,
                isLoading: !1
            });
        });
    },
    onReady: function() {},
    onShow: function() {
        e.getServeTime();
    },
    onHide: function() {},
    onUnload: function() {},
    onPullDownRefresh: function() {},
    onReachBottom: function() {},
    onShareAppMessage: function() {}
});