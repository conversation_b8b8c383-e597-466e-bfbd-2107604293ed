var e, t = (e = require("@vant/weapp/toast/toast")) && e.__esModule ? e : {
    default: e
}, a = require("../../utils/http.js");

var i = require("../../utils/transmission.js").rsaAesJs, s = require("../../utils/util.js").unction, n = getApp();

Page({
    data: {
        isDisabled: !1,
        isLoading: !1,
        checked: !1,
        telValue: "",
        codeValue: "",
        yzmValue: "",
        yzmWord: "获取验证码",
        disabled: !0,
        yzmImgUrl: "",
        currentTel: "",
        timer: null,
        isFirstTime: !0,
        province: "江苏",
        isCode: !0,
        dialogShow: !1,
        bannerUrls: [ {
            id: "1",
            image: "https://czfw.12580.com/file/M00/00/DD/wGQDC2VxbQqAHurbAAHOlfTYwTU482.png"
        }, {
            id: "2",
            image: "https://czfw.12580.com/czfw_fs/files/hcsh/2020/10/10/3b6250bf79c445a9bd015d555d219cba.png"
        }, {
            id: "3",
            image: "https://czfw.12580.com/czfw_fs/files/hcsh/2020/10/10/f72aebf3f7bd46bb8008edd9737f6bf4.png"
        } ],
        indicatorDots: !0,
        vertical: !1,
        openId: "",
        sessionKey: "",
        operator_url: "",
        ifShow: !1
    },
    onShareAppMessage: function() {},
    onLoad: function(e) {
        wx.hideHomeButton(), wx.showShareMenu({
            withShareTicket: !0
        }), "true" == e.checkedStr && this.setData({
            checked: !0
        });
    },
    onShow: function() {
        n.getServeTime();
    },
    getPhoneNumber1: function(e) {
        (0, t.default)("请确认阅读条款协议并勾选");
    },
    onCheckboxChange: function(e) {
        this.setData({
            checked: e.detail
        }), this.data.checked && this.setData({
            dialogShow: !0
        });
    },
    onConFirm: function() {
        this.setData({
            dialogShow: !1
        });
    },
    onClose: function() {
        this.setData({
            dialogShow: !1,
            checked: !1
        });
    },
    bindKeyInputTel: function(e) {
        new Date().getTime();
        if (this.setData({
            telValue: e.detail.value.trim().replace(" ", "")
        }), 11 === e.detail.value.length) {
            if (!/^1[3456789]\d{9}$/.test(e.detail.value)) return void (0, t.default)("请输入正确的手机号码");
            this.setData({
                isInputTel: !0,
                isCode: !0,
                currentTel: e.detail.value
            }), this.changeImg();
        }
    },
    bindKeyInputCode: function(e) {
        this.setData({
            codeValue: e.detail.value.trim()
        });
    },
    bindKeyInputYzm: function(e) {
        this.setData({
            yzmValue: e.detail.value.trim()
        }), 4 === this.data.yzmValue.length ? this.data.isFirstTime && this.setData({
            disabled: !1
        }) : this.setData({
            disabled: !0
        });
    },
    bindblurChange: function(e) {
        this.setData({
            yzmValue: e.detail.value.trim()
        }), 4 === this.data.yzmValue.length || e.detail.cursor >= 4 ? this.data.isFirstTime && this.setData({
            disabled: !1
        }) : this.setData({
            disabled: !0
        });
    },
    getCode: function() {
        var e = this, a = this.data, i = 60;
        return a.telValue ? /^1[3456789]\d{9}$/.test(a.telValue) ? a.yzmValue ? void (null == e.data.timer && (e.data.timer = setInterval(function() {
            e.setData({
                isFirstTime: !1,
                yzmWord: i + "s"
            }), --i < 0 && (clearInterval(e.data.timer), e.setData({
                yzmWord: "重新获取",
                disabled: !1,
                isFirstTime: !0,
                timer: null
            }));
        }, 1e3), e.getCodePost())) : ((0, t.default)("请输入图形验证码"), !1) : ((0, t.default)("请输入正确的手机号码"), 
        !1) : ((0, t.default)("请输入移动手机号码"), !1);
    },
    getCodePost: function() {
        var e = this, s = this, n = this.data, o = "".concat(n.yzmValue, "&").concat(n.telValue, "&", "mini"), r = i.EncryptDataHn(o);
        (0, a.POSTHN)("/newUser/getVerificationCodeForLogin", r).then(function(a) {
            if (a) {
                if (s.setData({
                    isCode: !1
                }), s.setData({
                    disabled: !1,
                    isFirstTime: !0
                }), console.log("验证码-11---", a), !a.successful) return wx.showToast({
                    title: a.resultMsg,
                    icon: "none"
                }), 305 == a.resultCode && s.changeImg(), 322 == a.resultCode && wx.navigateTo({
                    url: "/packagehn/pages/openedAvailable/openedAvailable"
                }), clearInterval(s.data.timer), e.setData({
                    yzmWord: "重新获取",
                    timer: null
                }), !1;
                a.successful && (e.setData({
                    province: a.data.provinceName
                }), (0, t.default)(a.resultMsg));
            } else e.setData({
                disabled: !1,
                isFirstTime: !0
            });
        }).catch(function(e) {
            s.setData({
                disabled: !1,
                isFirstTime: !0
            }), (0, t.default)(e);
        });
    },
    handleLogin: s(function() {
        var e = this;
        e.setData({
            isDisabled: !0
        }), setTimeout(function() {
            e.setData({
                isDisabled: !1
            });
        }, 5e3);
        var s = this.data;
        if (!s.telValue) return (0, t.default)("请输入移动手机号码"), !1;
        if (!/^1[3456789]\d{9}$/.test(s.telValue)) return (0, t.default)("请输入正确的手机号码"), 
        !1;
        if (!s.codeValue) return (0, t.default)("请输入短信验证码"), !1;
        if (!s.yzmValue) return (0, t.default)("请输入图形验证码"), !1;
        if (!s.checked) return (0, t.default)("请确认阅读条款协议并勾选"), !1;
        if (e.data.isCode) return (0, t.default)("请获取验证码"), !1;
        e.setData({
            isLoading: !0
        }), clearInterval(s.timer);
        var n = "".concat(s.telValue, "&").concat(s.codeValue);
        n = "".concat(s.telValue, "&").concat(s.codeValue, "&", "mini");
        var o = i.EncryptDataHn(n);
        (0, a.POSTHN)("/newUser/loginByUsernameAndVerificationCode", o).then(function(s) {
            if (console.log("----登录--9999-", s), e.setData({
                yzmWord: "重新获取",
                disabled: !1
            }), s) if (s.successful) {
                if (wx.setStorageSync("isSuperVip", s.data.isSuperVip), wx.setStorageSync("provinceListVip", s.data.provinceList), 
                wx.setStorageSync("userOutLogin", "0"), wx.setStorageSync("provinceName", s.data.provinceName), 
                wx.setStorageSync("provinceCode", s.data.provinceCode), wx.setStorageSync("provinceCenter", s.data.provinceCenter), 
                wx.setStorageSync("provinceCenter_hn", s.data.provinceCenter), wx.setStorageSync("loginStatus_hn", "1"), 
                wx.setStorageSync("mbmsAccessToken", s.data.mbmsAccessToken), wx.setStorageSync("mbmsRefreshToken", s.data.mbmsRefreshToken), 
                0 == s.data.userCodeList.length) wx.setStorageSync("memberTypeList_hn", []), wx.setStorageSync("userTypeIndex", 0), 
                wx.setStorageSync("vipType_hn", s.data.defaultVipType), wx.setStorageSync("vipId_hn", "*"), 
                wx.setStorageSync("feeAppCode_hn", s.data.defaultMemberGrade), wx.setStorageSync("isActivate", "0"); else {
                    wx.setStorageSync("memberTypeList_hn", s.data.userCodeList);
                    wx.setStorageSync("userTypeIndex", 0), wx.setStorageSync("isShowRestOilDrop", s.data.userCodeList[0].isShowRestOilDrop), 
                    wx.setStorageSync("vipType_hn", s.data.userCodeList[0].vipType), wx.setStorageSync("vipId_hn", s.data.userCodeList[0].codeId), 
                    wx.setStorageSync("feeAppCode_hn", s.data.userCodeList[0].feeAppCode), wx.setStorageSync("isActivate", s.data.userCodeList[0].isActivate);
                }
                wx.setStorageSync("token_hn", s.data.token), wx.setStorageSync("telephone_hn", s.data.telephone), 
                wx.setStorageSync("cityCode_hn", s.data.cityCode), wx.setStorageSync("cityName_hn", s.data.cityName), 
                wx.setStorageSync("defaultCityName", s.data.defaultCityName), wx.setStorageSync("defaultCityId", s.data.defaultCityId), 
                wx.setStorageSync("isLoginTo", !0);
                var n = s.data.provinceCode;
                wx.setStorageSync("selectTextContentByCode" + n, ""), wx.setStorageSync("selectAllBannerList" + n, ""), 
                wx.setStorageSync("selectHomeSecondZoneGoodsList" + n, ""), wx.setStorageSync("selectCouponSpeicalZone" + n, ""), 
                wx.setStorageSync("selectAllNoticeList" + n, ""), wx.setStorageSync("privilegeSetting" + n, ""), 
                wx.setStorageSync("selectBasicPrivilegeSetting" + n, ""), wx.setStorageSync("selectBusinessDetails" + n, ""), 
                wx.setStorageSync("getPersonCenterMenu" + n, ""), wx.switchTab({
                    url: "/pages/index/index"
                });
                var o = i.EncryptDataHn(s.data.token);
                (0, a.POSTHN)("/newUser/notifyLoginIsSuccess", o).then(function(e) {
                    console.log("----调试数据--9999-", s);
                }), e.setData({
                    isLoading: !1
                }), e.setData({
                    isLoading: !1
                });
            } else s.successful || (s.resultMsg && (0, t.default)(s.resultMsg), e.setData({
                isLoading: !1
            })); else e.setData({
                isLoading: !1
            });
        }).catch(function(t) {
            e.setData({
                isLoading: !1
            });
        });
    }),
    viewDetail: s(function() {
        wx.navigateTo({
            url: "./info/info"
        });
    }),
    changeImg: s(function() {
        new Date().getTime();
        if (!this.data.telValue) return (0, t.default)("请输入移动手机号码"), !1;
        if (!/^1[3456789]\d{9}$/.test(this.data.telValue)) return (0, t.default)("请输入正确的手机号码"), 
        !1;
        var e = "".concat(this.data.telValue), s = i.EncryptDataHn(e), n = "avd=".concat(s.avd, "&ksynum=").concat(s.ksynum, "&cmcxncxn=").concat(s.cmcxncxn);
        console.log("----1---", a.baseUrlHN + "/newUser/getImageVerificatrionCode?".concat(n));
        var o = a.baseUrlHN;
        this.setData({
            yzmImgUrl: o + "/newUser/getImageVerificatrionCode?".concat(n)
        });
    })
});