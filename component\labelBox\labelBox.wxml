<view bindtap="storeshoplist" class="pushbox" data-id="{{item.appProductId}}" wx:for="{{tui}}" wx:key="index">
    <image src="{{baseUrlNewImg+item.coverRectLarge}}" style="border-radius:10rpx 10rpx 0 0;height:300rpx;"></image>
    <view class="pushbox-item">
        <view class="titles">{{item.title}}</view>
        <text class="titlea">{{item.summary}}</text>
        <view class="hotbox-title">
            <text>￥</text>
            <text>{{item.salePrice}}</text>
            <text>￥{{item.originPrice}}</text>
        </view>
    </view>
</view>
