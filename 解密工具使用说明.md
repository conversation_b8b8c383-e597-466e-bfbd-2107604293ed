# 湖南版本真实解密工具使用说明

## 🎯 目标
解密你提供的真实响应数据：
```json
{
  "content": "Z5KNaR1xKweWGg2UUKPJ1x...",
  "key": "S5OUYtFrpKMZUGXPDE7TTT...",
  "iv": "hihSPyR1EvP5$*!*"
}
```

## 📁 工具文件说明

### 1. `nodejs_decrypt_tool.js` - Node.js版本（推荐）
- ✅ 使用Node.js的crypto模块
- ✅ 真正的RSA+AES解密
- ✅ 包含湖南版本的真实RSA私钥
- ✅ 可以解密你提供的真实数据

### 2. `real_decrypt_tool.js` - 浏览器版本
- ⚠️ 需要Web Crypto API支持
- ⚠️ 可能有兼容性问题
- ✅ 可在浏览器控制台中运行

## 🚀 推荐使用方法：Node.js版本

### 步骤1：安装Node.js
如果还没有安装Node.js：
1. 访问 https://nodejs.org/
2. 下载并安装最新版本

### 步骤2：运行解密工具
```bash
# 在命令行中运行
node nodejs_decrypt_tool.js
```

### 步骤3：查看解密结果
工具会自动解密你提供的数据并显示结果。

## 📊 预期输出

### 成功解密的输出示例：
```
🎉 Node.js版湖南解密工具已加载！
📖 使用方法:
1. NodeDecryptTool.testRealData() - 测试你提供的真实数据
2. NodeDecryptTool.decryptResponse(data) - 解密任意响应数据

🔥 自动测试你提供的真实数据:
🧪 测试你提供的真实响应数据:

========== 湖南版本真实数据解密 ==========
📦 输入数据: {key: "S5OUYtFrpKMZUGXPDE7TTT...", content: "Z5KNaR1xKweWGg2UUKPJ1x...", iv: "hihSPyR1EvP5$*!*"}

🔧 步骤1: Base64转Hex
✅ Base64转Hex成功

🔓 步骤2: RSA解密AES密钥
🔓 RSA解密中...
🔑 使用湖南版本RSA私钥
📄 加密数据(hex): 4b939462d16ba4a3195065cf0c4ed34d3d07bd9d1d090a7203a0cdce7b169d44...
✅ RSA解密成功，AES密钥: [解密得到的AES密钥]

🔓 步骤3: AES解密内容
🔓 AES解密中...
🔑 AES密钥: [AES密钥]
🎲 IV: hihSPyR1EvP5$*!*
📄 密文: Z5KNaR1xKweWGg2UUKPJ1x...
✅ AES解密成功

📋 步骤4: 解析JSON
🎯 最终解密结果: {
  "resultCode": "200",
  "resultMsg": "成功",
  "data": {
    // 解密后的实际数据
  }
}

🎉 解密成功！
```

## 🔧 自定义使用

### 解密其他响应数据：
```javascript
// 在Node.js环境中
const NodeDecryptTool = require('./nodejs_decrypt_tool.js');

// 你的其他响应数据
var otherResponseData = {
    "content": "你的其他content值",
    "key": "你的其他key值",
    "iv": "你的其他iv值"
};

// 解密
var result = NodeDecryptTool.decryptResponse(otherResponseData);
console.log("解密结果:", result);
```

## 🌐 浏览器版本使用方法

如果你想在浏览器中使用：

### 步骤1：打开浏览器控制台
1. 打开Chrome/Firefox等现代浏览器
2. 按F12打开开发者工具
3. 切换到Console标签

### 步骤2：复制粘贴代码
1. 复制 `real_decrypt_tool.js` 文件内容
2. 粘贴到控制台并回车

### 步骤3：运行测试
```javascript
// 在浏览器控制台中运行
RealDecryptTool.testRealData();
```

## ⚠️ 故障排除

### 常见问题：

1. **Node.js版本问题**
   ```bash
   # 检查Node.js版本
   node --version
   # 需要v12.0.0或更高版本
   ```

2. **RSA解密失败**
   - 确认使用的是湖南版本的私钥
   - 检查数据格式是否正确

3. **AES解密失败**
   - 可能是密钥长度问题
   - 工具会自动尝试调整密钥长度

4. **JSON解析失败**
   - 解密成功但JSON格式有问题
   - 工具会返回原始解密字符串

## 🔍 解密原理

### 解密步骤：
1. **Base64转Hex**: 将key从Base64格式转换为Hex格式
2. **RSA解密**: 使用湖南版本RSA私钥解密得到AES密钥
3. **AES解密**: 使用AES密钥和IV解密content内容
4. **JSON解析**: 将解密结果解析为JSON对象

### 使用的算法：
- **RSA**: PKCS1填充
- **AES**: AES-256-CBC模式，PKCS7填充
- **编码**: Base64编码

## 📈 扩展功能

### 批量解密：
```javascript
var responses = [
    {content: "...", key: "...", iv: "..."},
    {content: "...", key: "...", iv: "..."},
    // 更多数据...
];

responses.forEach((response, index) => {
    console.log(`解密第${index + 1}个响应:`);
    NodeDecryptTool.decryptResponse(response);
});
```

### 保存解密结果：
```javascript
const fs = require('fs');

var result = NodeDecryptTool.decryptResponse(responseData);
if (result) {
    fs.writeFileSync('decrypted_result.json', JSON.stringify(result, null, 2));
    console.log("解密结果已保存到 decrypted_result.json");
}
```

## 🎯 总结

推荐使用 `nodejs_decrypt_tool.js`，它：
- ✅ 使用真正的RSA+AES算法
- ✅ 包含湖南版本的真实私钥
- ✅ 可以解密你提供的真实数据
- ✅ 提供详细的解密步骤日志
- ✅ 自动处理各种边界情况
