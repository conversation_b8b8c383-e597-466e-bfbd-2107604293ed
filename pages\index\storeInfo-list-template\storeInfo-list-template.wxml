<template name="storeInfoListTemplate">
    <view class="store-list-content" wx:for="{{storeList}}" wx:key="index">
        <view class="list-left-info">
            <view class="list-left-info-img">
                <image class="list-left-info-smallImg" data-index="{{index}}" src="{{item.mdImg?pathImg+item.mdImg:'https://czfw.12580.com/file/M00/00/DD/wGQDC2VEpHSABw8uAABKgSJV-gM822.png'}}"></image>
            </view>
            <view class="store-list-item-info">
                <view class="storename">{{item.mdName}}</view>
                <view class="storeinfo">
                    <view catchtap="Calling" class="address">
                        <text class="gray-word1 ft13">{{item.mdAddr}}</text>
                    </view>
                    <view class="tel">
                        <image bindtap="callStroe" class="tel-icon" data-tel="{{item.mdTelephone?item.mdTelephone:4000125806}}" src="/static/img/icon_ihoneone.png"></image>
                        <view class="gray-word ft13">
                            <text class="tel-word">{{item.serviceTime}}</text>
                        </view>
                    </view>
                </view>
            </view>
        </view>
        <view class="list-right-distantce tc">
            <view class="line"></view>
            <view catchtap="handleGo" data-lbsx="{{item.lbsX}}" data-lbsy="{{item.lbsY}}" data-name="{{item.mdName}}">
                <image src="/static/imagehn/navigation.png" style="width:68rpx;height:68rpx;"></image>
                <view class="gray-word nobreak ft12" wx:if="{{item.distance>=0}}">{{item.distance==0?'0.1':item.distance}}km</view>
                <view class="gray-word ft12" wx:else>未定位</view>
            </view>
        </view>
    </view>
</template>
