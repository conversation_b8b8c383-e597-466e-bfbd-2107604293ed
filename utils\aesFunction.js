Object.defineProperty(exports, "__esModule", {
    value: !0
}), exports.AESUtil = void 0;

var e = require("./aes.js");

exports.AESUtil = {
    encrypt: function(t, r, n) {
        var s = e.enc.Utf8.parse(t), i = e.enc.Utf8.parse(r), a = e.enc.Utf8.parse(n), c = e.AES.encrypt(s, i, {
            iv: a,
            mode: e.mode.CBC,
            padding: e.pad.Pkcs7
        });
        return e.enc.Base64.stringify(c.ciphertext);
    },
    decrypt: function(t, r, n) {
        var s = e.enc.Utf8.parse(r), i = e.enc.Utf8.parse(n);
        return e.AES.decrypt(t, s, {
            iv: i,
            mode: e.mode.CBC,
            padding: e.pad.Pkcs7
        }).toString(e.enc.Utf8);
    },
    getAesKey: function() {
        var t = this.uuid(), r = e.enc.Utf8.parse(t);
        return r = e.enc.Base64.stringify(r).substring(2, 34);
    },
    getIv: function() {
        var t = this.uuid(), r = e.enc.Utf8.parse(t);
        return r = e.enc.Base64.stringify(r).substring(2, 18);
    },
    getAESKeyAndIv: function() {
        return {
            iv: this.getIv(),
            aesKey: this.getAesKey()
        };
    },
    uuid: function() {
        for (var e = [], t = 0; t < 36; t++) e[t] = "0123456789abcdef".substr(Math.floor(16 * Math.random()), 1);
        e[14] = "4", e[19] = "0123456789abcdef".substr(3 & e[19] | 8, 1), e[8] = e[13] = e[18] = e[23] = "-";
        var r = e.join("");
        return r;
    }
};