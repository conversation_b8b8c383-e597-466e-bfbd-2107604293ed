/**
 * 12580小程序加密解密工具使用示例
 * 
 * 运行方法：
 * npm install jsrsasign crypto-js
 * node usage_examples.js
 */

const CompleteCryptoTool = require('./complete_crypto_tool.js');

console.log("🎯 12580小程序加密解密工具使用示例\n");

// ==================== 示例1：生成加密请求数据 ====================
console.log("=".repeat(60));
console.log("📋 示例1：生成加密请求数据（湖南版本）");

// 模拟优惠券查询请求
var couponQueryData = {
    sCode: "SJS_CODE_12345",
    areaCode: "430100"
};

console.log("🎯 原始请求数据:", couponQueryData);

var encryptedRequest = CompleteCryptoTool.EncryptDataHn(couponQueryData);
if (encryptedRequest) {
    console.log("\n🔐 加密后的请求数据:");
    console.log("  avd:", encryptedRequest.avd);
    console.log("  ksynum:", encryptedRequest.ksynum.substring(0, 50) + "...");
    console.log("  cmcxncxn:", encryptedRequest.cmcxncxn);
    
    console.log("\n💡 这个加密数据可以直接发送给服务器！");
} else {
    console.log("❌ 请求数据加密失败");
}

// ==================== 示例2：解密响应数据 ====================
console.log("\n" + "=".repeat(60));
console.log("📋 示例2：解密服务器响应数据");

// 你提供的真实响应数据
var realResponseData = {
    "content": "Z5KNaR1xKweWGg2UUKPJ1xuppg7NE+MSeduoUrt7OlfofxEG6MA/EmFSVN1PN3TPOk2L9yWmtpwyxCPCrO77GhHZ889e7oTvXRVRM6F5vsQKIOuCY2NxFgqCt996rz1jCjP2nNkexi5UDgC+FYLGtAjsyR0bymB93y2TfMz/JK78PO47882bxE+2odl3BrIUD3QZ299aMMtsfirDcFmdgoS5Y72DGUHul1o6KCtP2AK7JmObE9oJUeodcspplF2PJ4Lg7s/kTgXxruU/fnfwUo5QrzTCmRaHK9QOBMTn4hxGXbNWXjhSB92e30K/E07zyLZBk8yfqSLlM6U94T/128M8i0Lnc9aCKIM7YmeSeHiZh+BlXM4O4gj8bk51PNNKsVeW7owo5TCBUkbyXLb6zA==",
    "key": "S5OUYtFrpKMZUGXPDE7TTT0HvZ0dCQpyA6DNznsWnUTMSFtkAXvRSPMWSve+z8Gsv4xOJJtgPLrAw5qeqly0HHgZpWyjDzOjahjeaJdkA7zoUtayySGX3yfUSCja1NtJOPGCn5OzJ6JJTg5OCfDdH0rj1KB06zHQ0YtsP5MTaMGSBj2ZkU4AkbHF3/MfpSuDnUYIevWg+uv3ywi3h5zBwyL8RjiqoVzGDy6pwLmp8o2Z+Gj6IJ8QYooItYNaQ7SG196vO8xfZd+sRm/RHen9dk4WqAEV8z6/FHkgvoGAVPrJVHLcNBEJee5irNIrQxeNP17UkOytbEvSxAkNXUPK4A==",
    "iv": "hihSPyR1EvP5$*!*"
};

console.log("🎯 服务器响应数据（部分）:");
console.log("  key:", realResponseData.key.substring(0, 50) + "...");
console.log("  content:", realResponseData.content.substring(0, 50) + "...");
console.log("  iv:", realResponseData.iv);

var decryptedResponse = CompleteCryptoTool.smartDecryptResponse(realResponseData);
if (decryptedResponse) {
    console.log("\n🎉 响应数据解密成功！");
    console.log("📋 检测到版本:", decryptedResponse.version);
    console.log("🔓 解密结果:");
    console.log(JSON.stringify(decryptedResponse.data, null, 2));
} else {
    console.log("❌ 响应数据解密失败");
}

// ==================== 示例3：不同类型的请求数据加密 ====================
console.log("\n" + "=".repeat(60));
console.log("📋 示例3：不同类型的请求数据加密");

var testCases = [
    {
        name: "字符串数据",
        data: "测试字符串数据"
    },
    {
        name: "JSON对象",
        data: {name: "张三", age: 25, city: "长沙"}
    },
    {
        name: "业务数据格式",
        data: "43&business_handling_code&28.2282&112.9388&test_token&***********&APP001&/pages/index/index&mini"
    },
    {
        name: "数组数据",
        data: ["item1", "item2", "item3"]
    }
];

testCases.forEach(function(testCase, index) {
    console.log("\n--- 测试用例 " + (index + 1) + ": " + testCase.name + " ---");
    console.log("原始数据:", testCase.data);
    
    var encrypted = CompleteCryptoTool.EncryptDataHn(testCase.data);
    if (encrypted) {
        console.log("✅ 加密成功");
        console.log("  avd:", encrypted.avd);
        console.log("  ksynum长度:", encrypted.ksynum.length);
        console.log("  cmcxncxn长度:", encrypted.cmcxncxn.length);
    } else {
        console.log("❌ 加密失败");
    }
});

// ==================== 示例4：完整的请求-响应循环测试 ====================
console.log("\n" + "=".repeat(60));
console.log("📋 示例4：完整的请求-响应循环测试");

var testData = {
    action: "query_coupon",
    params: {
        sCode: "TEST_CODE_001",
        areaCode: "430100",
        userId: "12345"
    }
};

console.log("🎯 测试数据:", testData);

// 1. 加密请求
var encryptedRequest2 = CompleteCryptoTool.EncryptDataHn(testData);
if (encryptedRequest2) {
    console.log("✅ 请求加密成功");
    
    // 2. 模拟服务器处理（这里我们模拟一个响应格式）
    console.log("🔄 模拟服务器处理...");
    
    // 模拟服务器响应（使用相同的密钥和IV进行响应加密）
    var mockServerResponse = {
        key: encryptedRequest2.ksynum,  // 使用相同的加密密钥
        content: encryptedRequest2.cmcxncxn,  // 模拟响应内容
        iv: encryptedRequest2.avd  // 使用相同的IV
    };
    
    // 3. 解密响应
    var decryptedResponse2 = CompleteCryptoTool.DecryptKeyHn(mockServerResponse);
    if (decryptedResponse2) {
        console.log("✅ 响应解密成功");
        console.log("🔓 解密结果:", decryptedResponse2);
        
        // 验证数据一致性
        var originalStr = JSON.stringify(testData);
        var decryptedStr = JSON.stringify(decryptedResponse2);
        console.log("🔍 数据一致性:", originalStr === decryptedStr ? "✅ 通过" : "❌ 失败");
    } else {
        console.log("❌ 响应解密失败");
    }
} else {
    console.log("❌ 请求加密失败");
}

// ==================== 示例5：实际使用场景 ====================
console.log("\n" + "=".repeat(60));
console.log("📋 示例5：实际使用场景示例");

console.log("\n💡 实际使用场景:");
console.log("1. 📱 小程序发送请求:");
console.log("   var requestData = {sCode: 'SJS_CODE_12345', areaCode: '430100'};");
console.log("   var encrypted = CompleteCryptoTool.EncryptDataHn(requestData);");
console.log("   // 发送 encrypted 到服务器");

console.log("\n2. 🖥️  服务器返回响应:");
console.log("   // 服务器返回加密的响应数据");
console.log("   var responseData = {key: '...', content: '...', iv: '...'};");

console.log("\n3. 📱 小程序解密响应:");
console.log("   var decrypted = CompleteCryptoTool.DecryptKeyHn(responseData);");
console.log("   // 处理解密后的数据");

console.log("\n🎯 总结:");
console.log("✅ 可以生成与小程序完全一致的加密请求");
console.log("✅ 可以解密服务器返回的所有响应数据");
console.log("❌ 无法解密抓包得到的请求数据（需要服务器私钥）");

console.log("\n🎉 所有示例运行完成！");
console.log("💡 你现在拥有了完整的12580小程序加密解密能力！");
