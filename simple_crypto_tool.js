/**
 * 简化版12580小程序加密解密工具
 * 专注于解密你提供的真实数据
 *
 * 运行方法：
 * npm install jsrsasign crypto-js
 * node simple_crypto_tool.js
 */

try {
	var KJUR = require("jsrsasign");
	var CryptoJS = require("crypto-js");
	console.log("✅ 依赖库加载成功");
} catch (e) {
	console.error("❌ 缺少依赖库，请运行：npm install jsrsasign crypto-js");
	process.exit(1);
}

var SimpleCryptoTool = {
	// 湖南版本的私钥
	HN_PRIVATE_KEY: `**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`,

	// 普通版本的私钥
	NORMAL_PRIVATE_KEY: `**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`,

	// Base64转Hex
	b64tohex: function (b64) {
		try {
			var binary = Buffer.from(b64, "base64").toString("binary");
			var hex = "";
			for (var i = 0; i < binary.length; i++) {
				var h = binary.charCodeAt(i).toString(16);
				hex += h.length === 1 ? "0" + h : h;
			}
			return hex;
		} catch (e) {
			console.error("❌ Base64转Hex失败:", e.message);
			return null;
		}
	},

	// RSA解密（使用jsrsasign的正确方式）
	rsaDecrypt: function (encryptedHex, privateKeyPem) {
		try {
			console.log("🔓 RSA解密中...");
			console.log("  加密数据长度:", encryptedHex.length);

			// 方法1：使用RSAKey直接解密
			try {
				var rsa = new KJUR.RSAKey();
				rsa.readPrivateKeyFromPEMString(privateKeyPem);
				var decrypted = rsa.decrypt(encryptedHex);

				if (decrypted) {
					console.log("✅ RSA解密成功（方法1）");
					return decrypted;
				}
			} catch (e1) {
				console.log("⚠️  方法1失败:", e1.message);
			}

			// 方法2：使用KEYUTIL
			try {
				var privateKey = KJUR.KEYUTIL.getKey(privateKeyPem);

				// 尝试不同的解密方法
				if (privateKey.decrypt) {
					var decrypted2 = privateKey.decrypt(encryptedHex);
					if (decrypted2) {
						console.log("✅ RSA解密成功（方法2）");
						return decrypted2;
					}
				}

				// 尝试使用doPrivate
				if (privateKey.doPrivate) {
					var bigInt = new KJUR.BigInteger(encryptedHex, 16);
					var decryptedBigInt = privateKey.doPrivate(bigInt);
					if (decryptedBigInt) {
						var decryptedHex = decryptedBigInt.toString(16);
						// 移除PKCS1填充
						var decrypted3 = this.removePKCS1Padding(decryptedHex);
						if (decrypted3) {
							console.log("✅ RSA解密成功（方法3）");
							return decrypted3;
						}
					}
				}
			} catch (e2) {
				console.log("⚠️  方法2失败:", e2.message);
			}

			// 方法3：使用Cipher
			try {
				var privateKey3 = KJUR.KEYUTIL.getKey(privateKeyPem);
				var decrypted4 = KJUR.crypto.Cipher.decrypt(encryptedHex, privateKey3);
				if (decrypted4) {
					console.log("✅ RSA解密成功（方法4）");
					return decrypted4;
				}
			} catch (e3) {
				console.log("⚠️  方法3失败:", e3.message);
			}

			throw new Error("所有解密方法都失败了");
		} catch (e) {
			console.error("❌ RSA解密失败:", e.message);
			return null;
		}
	},

	// 移除PKCS1填充（改进版本）
	removePKCS1Padding: function (hexStr) {
		try {
			console.log("🔧 移除PKCS1填充，输入长度:", hexStr.length);

			// 确保是偶数长度
			if (hexStr.length % 2 !== 0) {
				hexStr = "0" + hexStr;
			}

			// 转换为字节数组
			var bytes = [];
			for (var i = 0; i < hexStr.length; i += 2) {
				bytes.push(parseInt(hexStr.substr(i, 2), 16));
			}

			console.log("🔧 字节数组长度:", bytes.length);
			console.log("🔧 前10个字节:", bytes.slice(0, 10));

			// PKCS1填充格式：0x00 0x02 [随机字节] 0x00 [实际数据]
			if (bytes[0] !== 0x00 || bytes[1] !== 0x02) {
				console.log("⚠️  不是标准PKCS1格式，尝试查找0x00分隔符");
			}

			// 查找第二个0x00（数据开始位置）
			var startIndex = -1;
			for (var j = 2; j < bytes.length; j++) {
				if (bytes[j] === 0x00) {
					startIndex = j + 1;
					break;
				}
			}

			if (startIndex === -1) {
				console.log("⚠️  未找到0x00分隔符，尝试其他方法");
				// 尝试从末尾开始查找可打印字符
				for (var k = bytes.length - 1; k >= 0; k--) {
					if (bytes[k] >= 32 && bytes[k] <= 126) {
						// 找到可打印字符，向前查找连续的可打印字符
						var end = k + 1;
						while (k >= 0 && bytes[k] >= 32 && bytes[k] <= 126) {
							k--;
						}
						startIndex = k + 1;
						break;
					}
				}
			}

			if (startIndex === -1 || startIndex >= bytes.length) {
				throw new Error("无法找到有效的数据起始位置");
			}

			console.log("🔧 数据起始位置:", startIndex);

			// 提取实际数据
			var actualData = bytes.slice(startIndex);
			console.log("🔧 实际数据长度:", actualData.length);
			console.log("🔧 实际数据字节:", actualData);

			// 转换为字符串
			var result = "";
			for (var m = 0; m < actualData.length; m++) {
				result += String.fromCharCode(actualData[m]);
			}

			console.log("🔧 解析结果:", result);
			return result;
		} catch (e) {
			console.error("❌ 移除PKCS1填充失败:", e.message);
			return null;
		}
	},

	// AES解密
	decrypt: function (ciphertext, key, iv) {
		try {
			console.log("🔓 AES解密中...");
			console.log("  密钥:", key);
			console.log("  IV:", iv);

			var keyParsed = CryptoJS.enc.Utf8.parse(key);
			var ivParsed = CryptoJS.enc.Utf8.parse(iv);

			var decrypted = CryptoJS.AES.decrypt(ciphertext, keyParsed, {
				iv: ivParsed,
				mode: CryptoJS.mode.CBC,
				padding: CryptoJS.pad.Pkcs7,
			});

			var result = CryptoJS.enc.Utf8.stringify(decrypted);
			console.log("✅ AES解密成功");
			return result;
		} catch (e) {
			console.error("❌ AES解密失败:", e.message);
			return null;
		}
	},

	// 解密响应数据
	decryptResponseData: function () {
		console.log("🧪 解密你提供的真实响应数据:");

		var realResponseData = {
			content:
				"Z5KNaR1xKweWGg2UUKPJ1xuppg7NE+MSeduoUrt7OlfofxEG6MA/EmFSVN1PN3TPOk2L9yWmtpwyxCPCrO77GhHZ889e7oTvXRVRM6F5vsQKIOuCY2NxFgqCt996rz1jCjP2nNkexi5UDgC+FYLGtAjsyR0bymB93y2TfMz/JK78PO47882bxE+2odl3BrIUD3QZ299aMMtsfirDcFmdgoS5Y72DGUHul1o6KCtP2AK7JmObE9oJUeodcspplF2PJ4Lg7s/kTgXxruU/fnfwUo5QrzTCmRaHK9QOBMTn4hxGXbNWXjhSB92e30K/E07zyLZBk8yfqSLlM6U94T/128M8i0Lnc9aCKIM7YmeSeHiZh+BlXM4O4gj8bk51PNNKsVeW7owo5TCBUkbyXLb6zA==",
			key: "S5OUYtFrpKMZUGXPDE7TTT0HvZ0dCQpyA6DNznsWnUTMSFtkAXvRSPMWSve+z8Gsv4xOJJtgPLrAw5qeqly0HHgZpWyjDzOjahjeaJdkA7zoUtayySGX3yfUSCja1NtJOPGCn5OzJ6JJTg5OCfDdH0rj1KB06zHQ0YtsP5MTaMGSBj2ZkU4AkbHF3/MfpSuDnUYIevWg+uv3ywi3h5zBwyL8RjiqoVzGDy6pwLmp8o2Z+Gj6IJ8QYooItYNaQ7SG196vO8xfZd+sRm/RHen9dk4WqAEV8z6/FHkgvoGAVPrJVHLcNBEJee5irNIrQxeNP17UkOytbEvSxAkNXUPK4A==",
			iv: "hihSPyR1EvP5$*!*",
		};

		return this.decryptData(
			realResponseData.key,
			realResponseData.content,
			realResponseData.iv,
			"响应"
		);
	},

	// 解密请求数据
	decryptRequestData: function () {
		console.log("🧪 解密你提供的请求数据:");

		var requestData = {
			avd: "JjZTdlM2EtMTc1Ni",
			ksynum:
				"jC7Tpp46Rw7tEyYMHTdOC85e2BE4Y6JtGi0ds24JwdsoB+pE91zaZO/FdSAhjK84AIr7B+4A1BsnMOp842bHF9otALGvBDbXBdyltuw8wn2D7NRplg4NAoG1XcGS1MTIgGB0INvkUPBBHuVsNfpSXFUmelpOVdKOcEIGfXof7sejaW+42iv1475js3E5YtHQo9h2MsmJKgQE5TgDrJcDvZcb26La9X/Rd6LW+mjx+6LjQMkWuwIHev1edl7y3/8UZpyVcwMtGTVeLHiNonCAhU8W/SyMjvOWagNZ5FrqZ0/wBbgdM7i0Lv6p6ouH6R1k8Pss3bNGcoFDpBgRT8c8JQ==",
			cmcxncxn:
				"hS0JJh7v2SStTN2sviQhVsjD+TmDhn1ihEAy3DugDGk7BkYDplIZ/XrnOHV5fUfU",
		};

		// 请求数据格式：avd=IV, ksynum=加密的密钥, cmcxncxn=加密的内容
		console.log("🔍 检查请求数据结构:");
		console.log("  avd:", requestData.avd);
		console.log(
			"  ksynum:",
			requestData.ksynum
				? requestData.ksynum.substring(0, 50) + "..."
				: "undefined"
		);
		console.log(
			"  cmcxncxn:",
			requestData.cmcxncxn
				? requestData.cmcxncxn.substring(0, 50) + "..."
				: "undefined"
		);

		return this.decryptData(
			requestData.ksynum,
			requestData.cmcxncxn,
			requestData.avd,
			"请求"
		);
	},

	// 智能RSA解密（尝试两个版本的私钥）
	smartRsaDecrypt: function (hexKey) {
		console.log("🔍 智能RSA解密，尝试两个版本的私钥...");

		// 先尝试湖南版本
		console.log("🔍 尝试湖南版本私钥...");
		var aesKey = this.rsaDecrypt(hexKey, this.HN_PRIVATE_KEY);
		if (aesKey && aesKey.length >= 16) {
			console.log("✅ 湖南版本私钥解密成功");
			return aesKey;
		}

		// 再尝试普通版本
		console.log("🔍 尝试普通版本私钥...");
		var aesKey2 = this.rsaDecrypt(hexKey, this.NORMAL_PRIVATE_KEY);
		if (aesKey2 && aesKey2.length >= 16) {
			console.log("✅ 普通版本私钥解密成功");
			return aesKey2;
		}

		console.error("❌ 所有版本的私钥都解密失败");
		return null;
	},

	// 通用解密函数
	decryptData: function (encryptedKey, encryptedContent, iv, dataType) {
		console.log("📦 " + dataType + "数据:");
		console.log("  key:", encryptedKey.substring(0, 50) + "...");
		console.log("  content:", encryptedContent.substring(0, 50) + "...");
		console.log("  iv:", iv);

		try {
			// 1. Base64转Hex
			var hexKey = this.b64tohex(encryptedKey);
			if (!hexKey) throw new Error("密钥格式转换失败");

			console.log("🔄 转换后的Hex密钥长度:", hexKey.length);

			// 2. 智能RSA解密AES密钥（尝试两个版本）
			var aesKey = this.smartRsaDecrypt(hexKey);
			if (!aesKey) throw new Error("RSA解密失败");

			console.log("🔑 解密得到的AES密钥:", aesKey);

			// 3. AES解密内容
			var decryptedContent = this.decrypt(encryptedContent, aesKey, iv);
			if (!decryptedContent) throw new Error("AES解密失败");

			console.log("📄 解密得到的内容:", decryptedContent);

			// 4. 尝试解析JSON
			try {
				var result = JSON.parse(decryptedContent);
				console.log("🎯 " + dataType + "解密完成，JSON解析成功");
				return result;
			} catch (jsonError) {
				console.log("⚠️  JSON解析失败，返回原始字符串");
				return decryptedContent;
			}
		} catch (error) {
			console.error("❌ " + dataType + "解密失败:", error.message);
			return null;
		}
	},
};

// 立即运行测试
console.log("\n🎯 开始解密你的数据...\n");

// 解密响应数据
console.log("=".repeat(60));
var responseResult = SimpleCryptoTool.decryptResponseData();
if (responseResult) {
	console.log("\n🎉 响应数据解密成功！");
	console.log("📋 响应数据结果:");
	console.log(JSON.stringify(responseResult, null, 2));
} else {
	console.log("\n❌ 响应数据解密失败");
}

// 解密请求数据
console.log("\n" + "=".repeat(60));
var requestResult = SimpleCryptoTool.decryptRequestData();
if (requestResult) {
	console.log("\n🎉 请求数据解密成功！");
	console.log("📋 请求数据结果:");
	console.log(JSON.stringify(requestResult, null, 2));
} else {
	console.log("\n❌ 请求数据解密失败");
	console.log("💡 可能的原因:");
	console.log("  1. RSA密钥不匹配");
	console.log("  2. 数据格式不正确");
	console.log("  3. 加密算法参数不匹配");
}

// 导出模块
module.exports = SimpleCryptoTool;
