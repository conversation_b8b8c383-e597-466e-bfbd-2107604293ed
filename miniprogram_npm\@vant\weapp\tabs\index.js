var t = require("../../../../@babel/runtime/helpers/slicedToArray"), e = require("../common/component"), i = require("../mixins/touch"), n = require("../common/utils"), r = require("../common/validator"), a = require("../common/relation");

(0, e.VantComponent)({
    mixins: [ i.touch ],
    classes: [ "nav-class", "tab-class", "tab-active-class", "line-class" ],
    relation: (0, a.useChildren)("tab", function() {
        this.updateTabs();
    }),
    props: {
        sticky: Boolean,
        border: Boolean,
        swipeable: <PERSON><PERSON><PERSON>,
        titleActiveColor: String,
        titleInactiveColor: String,
        color: String,
        animated: {
            type: <PERSON><PERSON><PERSON>,
            observer: function() {
                var t = this;
                this.children.forEach(function(e, i) {
                    return e.updateRender(i === t.data.currentIndex, t);
                });
            }
        },
        lineWidth: {
            type: null,
            value: 40,
            observer: "resize"
        },
        lineHeight: {
            type: null,
            value: -1
        },
        active: {
            type: null,
            value: 0,
            observer: function(t) {
                t !== this.getCurrentName() && this.setCurrentIndexByName(t);
            }
        },
        type: {
            type: String,
            value: "line"
        },
        ellipsis: {
            type: Boolean,
            value: !0
        },
        duration: {
            type: Number,
            value: .3
        },
        zIndex: {
            type: Number,
            value: 1
        },
        swipeThreshold: {
            type: Number,
            value: 5,
            observer: function(t) {
                this.setData({
                    scrollable: this.children.length > t || !this.data.ellipsis
                });
            }
        },
        offsetTop: {
            type: Number,
            value: 0
        },
        lazyRender: {
            type: Boolean,
            value: !0
        }
    },
    data: {
        tabs: [],
        scrollLeft: 0,
        scrollable: !1,
        currentIndex: 0,
        container: null,
        skipTransition: !0,
        scrollWithAnimation: !1,
        lineOffsetLeft: 0
    },
    mounted: function() {
        var t = this;
        (0, n.requestAnimationFrame)(function() {
            t.swiping = !0, t.setData({
                container: function() {
                    return t.createSelectorQuery().select(".van-tabs");
                }
            }), t.resize(), t.scrollIntoView();
        });
    },
    methods: {
        updateTabs: function() {
            var t = this.children, e = void 0 === t ? [] : t, i = this.data;
            this.setData({
                tabs: e.map(function(t) {
                    return t.data;
                }),
                scrollable: this.children.length > i.swipeThreshold || !i.ellipsis
            }), this.setCurrentIndexByName(i.active || this.getCurrentName());
        },
        trigger: function(t, e) {
            var i = this.data.currentIndex, n = e || this.children[i];
            (0, r.isDef)(n) && this.$emit(t, {
                index: n.index,
                name: n.getComputedName(),
                title: n.data.title
            });
        },
        onTap: function(t) {
            var e = this, i = t.currentTarget.dataset.index, r = this.children[i];
            r.data.disabled ? this.trigger("disabled", r) : (this.setCurrentIndex(i), (0, n.nextTick)(function() {
                e.trigger("click");
            }));
        },
        setCurrentIndexByName: function(t) {
            var e = this.children, i = (void 0 === e ? [] : e).filter(function(e) {
                return e.getComputedName() === t;
            });
            i.length && this.setCurrentIndex(i[0].index);
        },
        setCurrentIndex: function(t) {
            var e = this, i = this.data, a = this.children, s = void 0 === a ? [] : a;
            if (!(!(0, r.isDef)(t) || t >= s.length || t < 0) && ((0, n.groupSetData)(this, function() {
                s.forEach(function(i, n) {
                    var r = n === t;
                    r === i.data.active && i.inited || i.updateRender(r, e);
                });
            }), t !== i.currentIndex)) {
                var o = null !== i.currentIndex;
                this.setData({
                    currentIndex: t
                }), (0, n.requestAnimationFrame)(function() {
                    e.resize(), e.scrollIntoView();
                }), (0, n.nextTick)(function() {
                    e.trigger("input"), o && e.trigger("change");
                });
            }
        },
        getCurrentName: function() {
            var t = this.children[this.data.currentIndex];
            if (t) return t.getComputedName();
        },
        resize: function() {
            var e = this;
            if ("line" === this.data.type) {
                var i = this.data, r = i.currentIndex, a = i.ellipsis, s = i.skipTransition;
                Promise.all([ (0, n.getAllRect)(this, ".van-tab"), (0, n.getRect)(this, ".van-tabs__line") ]).then(function(i) {
                    var o = t(i, 2), l = o[0], c = void 0 === l ? [] : l, u = o[1], h = c[r];
                    if (null != h) {
                        var d = c.slice(0, r).reduce(function(t, e) {
                            return t + e.width;
                        }, 0);
                        d += (h.width - u.width) / 2 + (a ? 0 : 8), e.setData({
                            lineOffsetLeft: d
                        }), e.swiping = !0, s && (0, n.nextTick)(function() {
                            e.setData({
                                skipTransition: !1
                            });
                        });
                    }
                });
            }
        },
        scrollIntoView: function() {
            var e = this, i = this.data, r = i.currentIndex, a = i.scrollable, s = i.scrollWithAnimation;
            a && Promise.all([ (0, n.getAllRect)(this, ".van-tab"), (0, n.getRect)(this, ".van-tabs__nav") ]).then(function(i) {
                var a = t(i, 2), o = a[0], l = a[1], c = o[r], u = o.slice(0, r).reduce(function(t, e) {
                    return t + e.width;
                }, 0);
                e.setData({
                    scrollLeft: u - (l.width - c.width) / 2
                }), s || (0, n.nextTick)(function() {
                    e.setData({
                        scrollWithAnimation: !0
                    });
                });
            });
        },
        onTouchScroll: function(t) {
            this.$emit("scroll", t.detail);
        },
        onTouchStart: function(t) {
            this.data.swipeable && (this.swiping = !0, this.touchStart(t));
        },
        onTouchMove: function(t) {
            this.data.swipeable && this.swiping && this.touchMove(t);
        },
        onTouchEnd: function() {
            if (this.data.swipeable && this.swiping) {
                var t = this.direction, e = this.deltaX, i = this.offsetX;
                if ("horizontal" === t && i >= 50) {
                    var n = this.getAvaiableTab(e);
                    -1 !== n && this.setCurrentIndex(n);
                }
                this.swiping = !1;
            }
        },
        getAvaiableTab: function(t) {
            for (var e = this.data, i = e.tabs, n = e.currentIndex, r = t > 0 ? -1 : 1, a = r; n + a < i.length && n + a >= 0; a += r) {
                var s = n + a;
                if (s >= 0 && s < i.length && i[s] && !i[s].disabled) return s;
            }
            return -1;
        }
    }
});