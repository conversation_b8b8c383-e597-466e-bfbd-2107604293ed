	<style> </style> 	<page></page> 	<script> 	var __setCssStartTime__ = Date.now();	setCssToHead([],undefined,{path:"./miniprogram_npm/@vant/weapp/picker/toolbar.wxss"})()
	var __setCssEndTime__ = Date.now();	(function() { 		var gf = $gwx( './miniprogram_npm/@vant/weapp/picker/toolbar.wxml' ); 		if (window.__wxAppCodeReadyCallback__) { 			window.__wxAppCodeReadyCallback__(gf); 		} else { 			document.dispatchEvent(new CustomEvent( "generateFuncReady", { 				detail: { 					generateFunc: gf 			}})); 		} 	})(); 	</script> 	 