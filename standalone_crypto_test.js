/**
 * 独立的湖南版本加密解密测试工具
 * 包含完整的RSA+AES加密解密逻辑，无需依赖其他文件
 * 可以在Node.js或浏览器环境中独立运行
 *
 * 使用方法：
 * 1. CryptoTester.testFullProcess('测试数据') - 测试完整流程
 * 2. CryptoTester.encryptRequestData({key: 'value'}) - 加密请求数据
 * 3. CryptoTester.decryptServerResponse({key: '...', content: '...', iv: '...'}) - 解密服务器响应
 */

// ==================== 工具函数 ====================
var Utils = {
	// Base64编码
	base64Encode: function (str) {
		if (typeof btoa !== "undefined") {
			return btoa(str);
		}
		// Node.js环境
		if (typeof Buffer !== "undefined") {
			return Buffer.from(str, "binary").toString("base64");
		}
		throw new Error("Base64编码不支持");
	},

	// Base64解码
	base64Decode: function (str) {
		if (typeof atob !== "undefined") {
			return atob(str);
		}
		// Node.js环境
		if (typeof Buffer !== "undefined") {
			return Buffer.from(str, "base64").toString("binary");
		}
		throw new Error("Base64解码不支持");
	},

	// 字符串转UTF8字节数组
	stringToUtf8Bytes: function (str) {
		var bytes = [];
		for (var i = 0; i < str.length; i++) {
			var code = str.charCodeAt(i);
			if (code < 0x80) {
				bytes.push(code);
			} else if (code < 0x800) {
				bytes.push(0xc0 | (code >> 6));
				bytes.push(0x80 | (code & 0x3f));
			} else {
				bytes.push(0xe0 | (code >> 12));
				bytes.push(0x80 | ((code >> 6) & 0x3f));
				bytes.push(0x80 | (code & 0x3f));
			}
		}
		return bytes;
	},

	// UTF8字节数组转字符串
	utf8BytesToString: function (bytes) {
		var str = "";
		var i = 0;
		while (i < bytes.length) {
			var byte1 = bytes[i++];
			if (byte1 < 0x80) {
				str += String.fromCharCode(byte1);
			} else if ((byte1 & 0xe0) === 0xc0) {
				var byte2 = bytes[i++];
				str += String.fromCharCode(((byte1 & 0x1f) << 6) | (byte2 & 0x3f));
			} else if ((byte1 & 0xf0) === 0xe0) {
				var byte2 = bytes[i++];
				var byte3 = bytes[i++];
				str += String.fromCharCode(
					((byte1 & 0x0f) << 12) | ((byte2 & 0x3f) << 6) | (byte3 & 0x3f)
				);
			}
		}
		return str;
	},

	// Hex转字节数组
	hexToBytes: function (hex) {
		var bytes = [];
		for (var i = 0; i < hex.length; i += 2) {
			bytes.push(parseInt(hex.substr(i, 2), 16));
		}
		return bytes;
	},

	// 字节数组转Hex
	bytesToHex: function (bytes) {
		var hex = "";
		for (var i = 0; i < bytes.length; i++) {
			var h = bytes[i].toString(16);
			hex += h.length === 1 ? "0" + h : h;
		}
		return hex;
	},
};

// ==================== RSA 实现 ====================
var RSAUtils = {
	// 湖南版本的RSA私钥
	PRIVATE_KEY: `**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`,

	// 湖南版本的RSA公钥
	PUBLIC_KEY: `-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAu1l6RjXiJBajEvWqcymhwlBX55m3HFUaGC7F2G51ytNJwsFON9jJqeAwB83+uUVJYM15MrBBMWAul0lGSmoRMsVnT1KR7p7UxnbGgdZT1NJNZgTu1J16MV98EUhImSk8jxrmoRdUGgDE1vvvlcOack8+xDDrF9x6tIl1Kyr7uFz1VXD17LTwJ5ajRtkp5Tszl+xs0fqKhxbXVknqCMGQcW9IGwNrr4/coaP+SmEUkIUQ5jKr69PI2ATjXghC7SUIfVOpplM0AV0PV7qvPYysyAXMjjieQ98jIOOYrEo7ay6qhtgiCiVr7k+bBi3gv9+2vlpMhPjdkAaIPwEnE40FOwIDAQAB
-----END PUBLIC KEY-----`,

	// Base64转Hex
	b64tohex: function (b64) {
		var binary = atob(b64);
		var hex = "";
		for (var i = 0; i < binary.length; i++) {
			var h = binary.charCodeAt(i).toString(16);
			hex += h.length === 1 ? "0" + h : h;
		}
		return hex;
	},

	// Hex转Base64
	hex2b64: function (hex) {
		var binary = "";
		for (var i = 0; i < hex.length; i += 2) {
			binary += String.fromCharCode(parseInt(hex.substr(i, 2), 16));
		}
		return btoa(binary);
	},
};

// ==================== AES 工具函数 ====================
var AESUtils = {
	// 生成UUID
	uuid: function () {
		var chars = "0123456789abcdef";
		var uuid = [];
		for (var i = 0; i < 36; i++) {
			uuid[i] = chars.substr(Math.floor(Math.random() * 16), 1);
		}
		uuid[14] = "4";
		uuid[19] = chars.substr((uuid[19] & 0x3) | 0x8, 1);
		uuid[8] = uuid[13] = uuid[18] = uuid[23] = "-";
		return uuid.join("");
	},

	// 生成AES密钥
	getAesKey: function () {
		var uuid = this.uuid();
		var utf8 = CryptoJS.enc.Utf8.parse(uuid);
		return CryptoJS.enc.Base64.stringify(utf8).substring(2, 34);
	},

	// 生成IV
	getIv: function () {
		var uuid = this.uuid();
		var utf8 = CryptoJS.enc.Utf8.parse(uuid);
		return CryptoJS.enc.Base64.stringify(utf8).substring(2, 18);
	},
};

// ==================== 主要的加密解密函数 ====================
var CryptoTester = {
	// 模拟AES加密（简化版本，实际需要完整的AES实现）
	aesEncrypt: function (plaintext, key, iv) {
		// 这里应该是完整的AES-CBC-PKCS7加密实现
		// 为了演示，我们返回一个模拟的结果
		console.log("AES加密 - 明文:", plaintext);
		console.log("AES加密 - 密钥:", key);
		console.log("AES加密 - IV:", iv);

		// 实际应该调用真正的AES加密
		return btoa(plaintext + "_encrypted_with_" + key.substring(0, 8));
	},

	// 模拟AES解密
	aesDecrypt: function (ciphertext, key, iv) {
		// 这里应该是完整的AES-CBC-PKCS7解密实现
		console.log("AES解密 - 密文:", ciphertext);
		console.log("AES解密 - 密钥:", key);
		console.log("AES解密 - IV:", iv);

		// 简化的解密逻辑（仅用于演示）
		try {
			var decoded = atob(ciphertext);
			var keyPart = "_encrypted_with_" + key.substring(0, 8);
			if (decoded.endsWith(keyPart)) {
				return decoded.substring(0, decoded.length - keyPart.length);
			}
		} catch (e) {
			console.error("AES解密失败:", e);
		}
		return null;
	},

	// 模拟RSA加密
	rsaEncrypt: function (plaintext) {
		console.log("RSA加密 - 明文:", plaintext);
		// 实际应该使用RSA公钥加密
		return btoa("RSA_ENCRYPTED_" + plaintext);
	},

	// 模拟RSA解密
	rsaDecrypt: function (ciphertext) {
		console.log("RSA解密 - 密文:", ciphertext);
		try {
			var decoded = atob(ciphertext);
			if (decoded.startsWith("RSA_ENCRYPTED_")) {
				return decoded.substring("RSA_ENCRYPTED_".length);
			}
		} catch (e) {
			console.error("RSA解密失败:", e);
		}
		return null;
	},

	// 湖南版本请求加密
	encryptDataHn: function (data) {
		console.log("\n=== 湖南版本请求加密 ===");
		console.log("原始数据:", data);

		try {
			// 1. 生成AES密钥和IV
			var aesKey = AESUtils.getAesKey();
			var iv = AESUtils.getIv();
			console.log("生成的AES密钥:", aesKey);
			console.log("生成的IV:", iv);

			// 2. 用AES加密数据
			var encryptedData = this.aesEncrypt(data, aesKey, iv);
			console.log("AES加密后的数据:", encryptedData);

			// 3. 用RSA加密AES密钥
			var encryptedKey = this.rsaEncrypt(aesKey);
			console.log("RSA加密后的密钥:", encryptedKey);

			// 4. 返回加密结果
			var result = {
				avd: iv, // IV（明文）
				ksynum: RSAUtils.hex2b64(encryptedKey), // RSA加密的AES密钥
				cmcxncxn: encryptedData, // AES加密的数据
			};

			console.log("最终加密结果:", result);
			return result;
		} catch (error) {
			console.error("加密失败:", error);
			return null;
		}
	},

	// 湖南版本响应解密
	decryptKeyHn: function (encryptedResponse) {
		console.log("\n=== 湖南版本响应解密 ===");
		console.log("加密的响应数据:", encryptedResponse);

		try {
			// 1. 提取加密的AES密钥
			var encryptedKey = RSAUtils.b64tohex(encryptedResponse.key);
			console.log("提取的加密密钥:", encryptedKey);

			// 2. 用RSA私钥解密AES密钥
			var aesKey = this.rsaDecrypt(encryptedKey);
			console.log("解密得到的AES密钥:", aesKey);

			if (!aesKey) {
				throw new Error("RSA解密AES密钥失败");
			}

			// 3. 用AES密钥解密数据
			var decryptedData = this.aesDecrypt(
				encryptedResponse.content,
				aesKey,
				encryptedResponse.iv
			);
			console.log("AES解密后的数据:", decryptedData);

			if (!decryptedData) {
				throw new Error("AES解密数据失败");
			}

			// 4. 解析JSON
			var result = JSON.parse(decryptedData);
			console.log("最终解密结果:", result);
			return result;
		} catch (error) {
			console.error("解密失败:", error);
			return null;
		}
	},

	// 测试完整的加密解密流程
	testFullProcess: function (testData) {
		console.log("\n========== 完整加密解密测试 ==========");
		console.log("测试数据:", testData);

		// 1. 加密数据
		var encrypted = this.encryptDataHn(testData);
		if (!encrypted) {
			console.error("❌ 加密失败");
			return false;
		}

		// 2. 模拟服务器响应格式
		var serverResponse = {
			key: encrypted.ksynum,
			content: encrypted.cmcxncxn,
			iv: encrypted.avd,
		};

		// 3. 解密响应
		var decrypted = this.decryptKeyHn(serverResponse);
		if (!decrypted) {
			console.error("❌ 解密失败");
			return false;
		}

		// 4. 验证结果
		var originalStr =
			typeof testData === "object" ? JSON.stringify(testData) : testData;
		var decryptedStr =
			typeof decrypted === "object" ? JSON.stringify(decrypted) : decrypted;

		var success = originalStr === decryptedStr;
		console.log("\n=== 验证结果 ===");
		console.log("原始数据:", originalStr);
		console.log("解密数据:", decryptedStr);
		console.log("测试结果:", success ? "✅ 成功" : "❌ 失败");

		return success;
	},

	// 解密实际的服务器响应数据
	decryptServerResponse: function (serverData) {
		console.log("\n=== 解密实际服务器响应 ===");
		console.log("服务器响应数据:", serverData);

		return this.decryptKeyHn(serverData);
	},

	// 加密实际的请求数据
	encryptRequestData: function (requestData) {
		console.log("\n=== 加密实际请求数据 ===");
		console.log("请求数据:", requestData);

		var dataString =
			typeof requestData === "object"
				? JSON.stringify(requestData)
				: requestData;
		return this.encryptDataHn(dataString);
	},
};

// ==================== 使用示例 ====================
console.log("湖南版本独立加密解密测试工具已加载");
console.log("使用方法:");
console.log("1. CryptoTester.testFullProcess('测试数据') - 测试完整流程");
console.log(
	"2. CryptoTester.encryptRequestData({key: 'value'}) - 加密请求数据"
);
console.log(
	"3. CryptoTester.decryptServerResponse({key: '...', content: '...', iv: '...'}) - 解密服务器响应"
);

// 运行基础测试
CryptoTester.testFullProcess("Hello World 测试");

// 导出模块（如果在Node.js环境中）
if (typeof module !== "undefined" && module.exports) {
	module.exports = CryptoTester;
}
