/**
 * 12580小程序加密解密工具使用示例
 * 
 * 运行方法：
 * node 使用示例.js
 */

const CryptoTool = require('./完整加密解密工具.js');

console.log("🎯 12580小程序加密解密工具使用示例\n");

// ==================== 示例1：湖南版本加密解密 ====================
console.log("=".repeat(60));
console.log("📋 示例1：湖南版本加密解密");

// 模拟优惠券查询数据
var couponData = {
    sCode: "SJS_CODE_12345",
    areaCode: "430100"
};

console.log("🎯 原始优惠券数据:", couponData);

// 加密请求数据
var encryptedRequest = CryptoTool.EncryptDataHn(couponData);
console.log("🔐 加密后的请求数据:", encryptedRequest);

// 模拟服务器响应（将请求格式转换为响应格式）
var mockServerResponse = {
    key: encryptedRequest.ksynum,
    content: encryptedRequest.cmcxncxn,
    iv: encryptedRequest.avd
};

// 解密响应数据
var decryptedResponse = CryptoTool.DecryptKeyHn(mockServerResponse);
console.log("🔓 解密后的响应数据:", decryptedResponse);

// ==================== 示例2：业务办理数据加密 ====================
console.log("\n" + "=".repeat(60));
console.log("📋 示例2：业务办理数据加密");

// 模拟业务办理数据（字符串格式）
var businessData = "43&business_handling_code&28.2282&112.9388&test_token&***********&APP001&/pages/index/index&mini";

console.log("🎯 原始业务数据:", businessData);

// 加密
var encryptedBusiness = CryptoTool.EncryptDataHn(businessData);
console.log("🔐 加密后的业务数据:", {
    avd: encryptedBusiness.avd,
    ksynum: encryptedBusiness.ksynum.substring(0, 50) + "...",
    cmcxncxn: encryptedBusiness.cmcxncxn.substring(0, 50) + "..."
});

// ==================== 示例3：解密真实响应数据 ====================
console.log("\n" + "=".repeat(60));
console.log("📋 示例3：解密你提供的真实响应数据");

var realResponseData = {
    "content": "Z5KNaR1xKweWGg2UUKPJ1xuppg7NE+MSeduoUrt7OlfofxEG6MA/EmFSVN1PN3TPOk2L9yWmtpwyxCPCrO77GhHZ889e7oTvXRVRM6F5vsQKIOuCY2NxFgqCt996rz1jCjP2nNkexi5UDgC+FYLGtAjsyR0bymB93y2TfMz/JK78PO47882bxE+2odl3BrIUD3QZ299aMMtsfirDcFmdgoS5Y72DGUHul1o6KCtP2AK7JmObE9oJUeodcspplF2PJ4Lg7s/kTgXxruU/fnfwUo5QrzTCmRaHK9QOBMTn4hxGXbNWXjhSB92e30K/E07zyLZBk8yfqSLlM6U94T/128M8i0Lnc9aCKIM7YmeSeHiZh+BlXM4O4gj8bk51PNNKsVeW7owo5TCBUkbyXLb6zA==",
    "key": "S5OUYtFrpKMZUGXPDE7TTT0HvZ0dCQpyA6DNznsWnUTMSFtkAXvRSPMWSve+z8Gsv4xOJJtgPLrAw5qeqly0HHgZpWyjDzOjahjeaJdkA7zoUtayySGX3yfUSCja1NtJOPGCn5OzJ6JJTg5OCfDdH0rj1KB06zHQ0YtsP5MTaMGSBj2ZkU4AkbHF3/MfpSuDnUYIevWg+uv3ywi3h5zBwyL8RjiqoVzGDy6pwLmp8o2Z+Gj6IJ8QYooItYNaQ7SG196vO8xfZd+sRm/RHen9dk4WqAEV8z6/FHkgvoGAVPrJVHLcNBEJee5irNIrQxeNP17UkOytbEvSxAkNXUPK4A==",
    "iv": "hihSPyR1EvP5$*!*"
};

console.log("🎯 真实响应数据（部分）:", {
    key: realResponseData.key.substring(0, 50) + "...",
    content: realResponseData.content.substring(0, 50) + "...",
    iv: realResponseData.iv
});

// 使用智能解密
var smartResult = CryptoTool.smartDecrypt(realResponseData);
if (smartResult) {
    console.log("🎉 智能解密成功！");
    console.log("📋 检测到版本:", smartResult.version);
    console.log("🔓 解密结果:", smartResult.data);
} else {
    console.log("❌ 智能解密失败");
}

// ==================== 示例4：普通版本加密解密 ====================
console.log("\n" + "=".repeat(60));
console.log("📋 示例4：普通版本加密解密");

var normalData = {
    username: "testuser",
    password: "testpass"
};

console.log("🎯 普通版本测试数据:", normalData);

// 普通版本加密
var normalEncrypted = CryptoTool.EncryptData(normalData);
if (normalEncrypted) {
    console.log("🔐 普通版本加密成功");
    
    // 模拟响应
    var normalResponse = {
        key: normalEncrypted.ksynum,
        content: normalEncrypted.cmcxncxn,
        iv: normalEncrypted.avd
    };
    
    // 普通版本解密
    var normalDecrypted = CryptoTool.DecryptKey(normalResponse);
    console.log("🔓 普通版本解密结果:", normalDecrypted);
}

// ==================== 示例5：批量测试不同数据类型 ====================
console.log("\n" + "=".repeat(60));
console.log("📋 示例5：批量测试不同数据类型");

var testCases = [
    "简单字符串",
    "包含中文的字符串测试",
    {name: "张三", age: 25, city: "长沙"},
    ["数组", "测试", 123],
    {
        complex: {
            nested: "object",
            with: ["array", "elements"],
            number: 42
        }
    }
];

testCases.forEach(function(testCase, index) {
    console.log("\n--- 测试用例 " + (index + 1) + " ---");
    console.log("数据:", testCase);
    
    // 测试湖南版本
    var encrypted = CryptoTool.EncryptDataHn(testCase);
    if (encrypted) {
        var response = {
            key: encrypted.ksynum,
            content: encrypted.cmcxncxn,
            iv: encrypted.avd
        };
        var decrypted = CryptoTool.DecryptKeyHn(response);
        
        var originalStr = typeof testCase === 'object' ? JSON.stringify(testCase) : String(testCase);
        var decryptedStr = typeof decrypted === 'object' ? JSON.stringify(decrypted) : String(decrypted);
        
        console.log("结果:", originalStr === decryptedStr ? "✅ 成功" : "❌ 失败");
    } else {
        console.log("结果: ❌ 加密失败");
    }
});

// ==================== 示例6：性能测试 ====================
console.log("\n" + "=".repeat(60));
console.log("📋 示例6：性能测试");

var performanceTestData = {
    message: "性能测试数据",
    timestamp: Date.now(),
    data: "这是一个用于性能测试的较长字符串，包含了一些中文字符和数字123456789"
};

console.log("🎯 开始性能测试...");
var startTime = Date.now();

// 执行100次加密解密
var successCount = 0;
for (var i = 0; i < 100; i++) {
    var encrypted = CryptoTool.EncryptDataHn(performanceTestData);
    if (encrypted) {
        var response = {
            key: encrypted.ksynum,
            content: encrypted.cmcxncxn,
            iv: encrypted.avd
        };
        var decrypted = CryptoTool.DecryptKeyHn(response);
        if (decrypted) {
            successCount++;
        }
    }
}

var endTime = Date.now();
var totalTime = endTime - startTime;

console.log("📊 性能测试结果:");
console.log("  总次数: 100");
console.log("  成功次数:", successCount);
console.log("  总耗时:", totalTime + "ms");
console.log("  平均耗时:", Math.round(totalTime / 100) + "ms/次");
console.log("  成功率:", Math.round(successCount / 100 * 100) + "%");

console.log("\n🎉 所有示例运行完成！");
console.log("💡 你可以根据这些示例来使用加密解密工具");
