var style = require('p_./miniprogram_npm/@vant/weapp/wxs/style.wxs')();
var addUnit = require('p_./miniprogram_npm/@vant/weapp/wxs/add-unit.wxs')();

function rootStyle(data) {
    if (!data.gutter) {
        return ('')
    };
    return (style(({
        'padding-right': addUnit(data.gutter / 2),
        'padding-left': addUnit(data.gutter / 2),
    })))
};
module.exports = ({
    rootStyle: rootStyle,
});