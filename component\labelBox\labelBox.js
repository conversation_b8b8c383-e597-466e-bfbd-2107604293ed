var t = require("../../utils/http.js");

Component({
    data: {
        tui: [],
        baseUrlNewImg: t.baseUrlNew
    },
    properties: {
        tui: {
            type: Array,
            value: "",
            observer: function(t, n) {
                this.setData({
                    tui: t
                });
            }
        }
    },
    onLoad: function(t) {},
    methods: {
        storeshoplist: function(t) {
            var n = t.currentTarget.dataset.id;
            this.triggerEvent("storeshoplist", n);
        }
    },
    onReady: function() {},
    onShow: function() {},
    onHide: function() {},
    onUnload: function() {},
    onPullDownRefresh: function() {},
    onReachBottom: function() {
        this.setData({
            int: this.data.int + 1
        }), this.getMore1();
    },
    onShareAppMessage: function() {}
});