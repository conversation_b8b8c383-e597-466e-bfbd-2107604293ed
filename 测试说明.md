# 湖南版本加密解密测试模块使用说明

## 文件说明

### 1. test_crypto.js
完整的测试模块，包含所有测试功能：
- 请求加密测试
- 响应解密测试
- 完整加密解密循环测试
- 密钥生成测试

### 2. test_page.js
可以集成到小程序页面中的测试代码，提供页面级别的测试功能。

### 3. simple_test.js
简化的测试脚本，可以直接在控制台中运行。

## 使用方法

### 方法一：在小程序控制台中直接测试

1. 打开小程序开发者工具
2. 在控制台中复制粘贴以下代码：

```javascript
// 引入测试模块
var transmission = require("./utils/transmission.js").rsaAesJs;

// 测试数据
var testData = "测试数据123";
console.log("原始数据:", testData);

// 加密
var encrypted = transmission.EncryptDataHn(testData);
console.log("加密结果:", encrypted);

// 模拟服务器响应
var serverResponse = {
    key: encrypted.ksynum,
    content: encrypted.cmcxncxn,
    iv: encrypted.avd
};

// 解密
var decrypted = transmission.DecryptKeyHn(serverResponse);
console.log("解密结果:", decrypted);
console.log("测试结果:", testData === decrypted ? "✅ 成功" : "❌ 失败");
```

### 方法二：使用完整测试模块

1. 在任意页面的 js 文件中引入：
```javascript
var CryptoTester = require("../../test_crypto.js");

// 在页面加载时运行测试
onLoad: function() {
    CryptoTester.runAllTests();
}
```

### 方法三：使用简化测试脚本

1. 在控制台中运行：
```javascript
// 复制 simple_test.js 中的代码到控制台
// 然后调用测试函数
testCrypto();
testKeyGeneration();
testRealRequestFormat();
```

## 测试内容

### 1. 基础加密解密测试
- 测试简单字符串的加密解密
- 验证数据完整性
- 检查加密参数（IV、密钥、密文）

### 2. JSON对象测试
- 测试复杂JSON对象的序列化和加密
- 验证对象结构完整性

### 3. 业务数据格式测试
- 测试实际业务中使用的数据格式
- 模拟真实的请求参数

### 4. 密钥生成测试
- 验证AES密钥和IV的随机性
- 检查密钥长度和格式

## 预期输出

### 成功的测试输出示例：
```
=== 测试请求加密 ===
原始请求数据: 测试数据123
原始数据类型: string
待加密字符串: 测试数据123
加密结果:
  - avd (IV): AbCdEfGhIjKlMnOp
  - ksynum (RSA加密的AES密钥): [Base64编码的RSA加密数据]
  - cmcxncxn (AES加密的数据): [Base64编码的AES加密数据]

=== 测试响应解密 ===
加密的响应数据: {key: "...", content: "...", iv: "..."}
解密后的原始数据: 测试数据123
解密数据类型: string

=== 验证结果 ===
原始数据: 测试数据123
解密数据: 测试数据123
数据一致性: ✅ 通过
```

## 故障排除

### 常见错误及解决方法：

1. **模块引入失败**
   - 检查文件路径是否正确
   - 确保在小程序环境中运行

2. **加密失败**
   - 检查RSA公钥是否正确
   - 验证AES密钥生成是否正常

3. **解密失败**
   - 检查RSA私钥是否正确
   - 验证数据格式是否符合预期

4. **数据不一致**
   - 检查字符编码问题
   - 验证JSON序列化/反序列化

## 实际应用测试

### 测试真实网络请求：
```javascript
// 注意：这会发起真实的网络请求
var http = require("./utils/http.js");
var transmission = require("./utils/transmission.js").rsaAesJs;

var testData = {
    sCode: "SJS_CODE_TEST",
    areaCode: "430100"
};

var encryptedData = transmission.EncryptDataHn(JSON.stringify(testData));
console.log("发送的加密数据:", encryptedData);

http.POSTHN("/coupon/selectCodeDetailsBySCode", encryptedData).then(function(response) {
    console.log("服务器响应（已自动解密）:", response);
});
```

## 注意事项

1. **安全性**：测试时避免使用真实的敏感数据
2. **网络请求**：测试真实网络请求时要谨慎，避免对服务器造成压力
3. **日志输出**：生产环境中应该移除详细的加密日志输出
4. **错误处理**：实际使用时要添加完善的错误处理机制

## 扩展功能

可以根据需要添加更多测试：
- 性能测试（加密解密速度）
- 压力测试（大量数据处理）
- 边界测试（特殊字符、空数据等）
- 兼容性测试（不同数据格式）
