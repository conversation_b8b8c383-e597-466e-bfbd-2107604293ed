<view class="container">
    <view class="container1" wx:if="{{wifiBool}}">
        <image class="wifi_img" src="/static/image/wifi.png"></image>
        <view class="view_wifi_text">亲，网络开小差了</view>
        <view class="view_wifi_text1">请检查您的网络设置或退出程序稍后再试~</view>
        <view bindtap="userList" class="view_wifi_text2">点击刷新</view>
    </view>
    <bock wx:else>
        <view class="container">
            <view class="user-info">
                <image class="userinfo-bg" mode="widthFix" src="https://czfw.12580.com/czfw_fs/files/hcsh/2020/12/10/7bee7095775d459d983b0bac3819ffe2.png"></image>
                <view>
                    <van-image round height="123rpx" src="{{avatarUrl==''?'/static/imagehn/icon_portrait.png':avatarUrl}}" width="123rpx" wx:if="{{loginStatus=='1'}}"></van-image>
                    <van-image round bindtap="handleLogin" height="123rpx" src="{{avatarUrl==''?'/static/imagehn/icon_portrait.png':avatarUrl}}" width="123rpx" wx:if="{{loginStatus=='0'}}"></van-image>
                    <view class="userinfo-view" wx:if="{{loginStatus=='1'}}">
                        <text class="userName">{{substr.subTel(userName)}}</text>
                        <view class="userinfo-view1" wx:if="{{isShowOil==1}}">
                            <image mode="widthFix" src="/static/imagehn/oil-hn-other.png" style="margin-right:10rpx;width:18rpx;height:28rpx;"></image>
                            <span class="userName2">账户油滴:{{' '+currentOil}}</span>
                            <view bindtap="goToDropRule" class="userinfo-view2">油滴规则 ></view>
                        </view>
                    </view>
                    <text bindtap="handleLogin" class="userName1" id="1" wx:if="{{loginStatus=='0'}}">{{userName}}</text>
                </view>
                <button bind:chooseavatar="onChooseAvatar" class="avatar-wrapper" openType="chooseAvatar" wx:if="{{loginStatus=='1'}}"></button>
                <view bindtap="handleOut" wx:if="{{loginStatus=='1'}}">退出登录</view>
            </view>
            <view class="user-info-item-l">
                <view class="bgWhite user-info-item">
                    <block wx:for="{{navList}}" wx:key="index">
                        <view wx:if="{{item.modelTypeCode=='personal_center_service_tel'}}">
                            <button bindcontact="handleContact" class="kefubottom" openType="contact" sessionFrom="sessionFrom">
                                <image mode="widthFix" src="{{pathImg+item.iconUrl}}"></image>
                                <text>{{item.modelName}}</text>
                            </button>
                        </view>
                        <view bindtap="userhandNav" data-item="{{item}}" wx:else>
                            <image mode="widthFix" src="{{pathImg+item.iconUrl}}"></image>
                            <text>{{item.modelName}}</text>
                        </view>
                    </block>
                </view>
            </view>
        </view>
        <van-overlay show="{{isOverlay}}">
            <view class="container3">
                <view class="container-dialog">
                    <view class="container-dialog-title">
                        <view class="container-dialog-title-view"></view>
                        <span>注销说明</span>
                        <view class="container-dialog-title-view">
                            <image bindtap="closeOverlay" class="close-dialog" src="/static/imagehn/close-hn.png"></image>
                        </view>
                    </view>
                    <view class="container-dialog-conter-view-txt">您正在注销<span style="color:#FF0000;">{{' '+substr.subTel(userName)+' '}}</span>在本客户端的服务功能，请谨慎操作。</view>
                    <view class="container-dialog-conter-view-line"></view>
                    <view class="container-dialog-conter-view-txt2">
                        <image class="hint-img" src="/static/img/icon-hint.png"></image>注销前，请确认以下信息：</view>
                    <view class="container-dialog-conter-view-txt1">
                        <view>1、注销后本客户端将不再为当前号码提供查询、领取权益等相关服务；</view>
                        <view>2、如您注销后想恢复使用查询、领取权益等相关服务，请您重新登录。</view>
                    </view>
                    <view class="container-checkbox">
                        <van-checkbox bind:change="onCheckboxChange" checkedColor="#DD3232" iconSize="18px" shape="square" value="{{checked}}">
                            <text style="font-size:24rpx;">申请注销即表示您已阅读及同意以上信息</text>
                        </van-checkbox>
                    </view>
                    <view bindtap="submit" class="container-dialog-btn">确定申请</view>
                </view>
            </view>
        </van-overlay>
    </bock>
</view>
<van-loading class="loading" color="#DD3231" type="spinner" wx:if="{{isLoading}}"></van-loading>
<view class="copyright1 tc ft12" style="background-color:#f5f5f5;padding-top:10px;">
    <view style="font-size:22rpx;font-family:PingFang SC;color:rgba(157,156,156,1);">©中国移动通信集团江苏有限公司版权所有</view>
    <view style="font-size:22rpx;font-family:PingFang SC;color:rgba(157,156,156,1);">苏ICP备11070397号-21</view>
    <view style="font-size:22rpx;font-family:PingFang SC;color:rgba(157,156,156,1);">（中国移动通信集团委托江苏有限公司支撑12580惠出行平台）</view>
</view>
<van-dialog id="van-dialog"></van-dialog>
<wxs module="substr" src="../../wxs/handleStr.wxs" />