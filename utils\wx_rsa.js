var t, e, i, n, r, s, a = require("../@babel/runtime/helpers/typeof"), o = {
    appName: "Netscape",
    userAgent: "Mozilla/5.0 (iPhone; CPU iPhone OS 9_1 like Mac OS X) AppleWebKit/601.1.46 (KHTML, like Gecko) Version/9.0 Mobile/13B143 Safari/601.1"
}, u = {
    ASN1: null,
    Base64: null,
    Hex: null,
    crypto: null,
    href: null
}, c = null;

if (void 0 === l || !l) var l = {};

l.namespace = function() {
    var t, e, i, n = arguments, r = null;
    for (t = 0; t < n.length; t += 1) for (i = ("" + n[t]).split("."), r = l, e = "YAHOO" == i[0] ? 1 : 0; e < i.length; e += 1) r[i[e]] = r[i[e]] || {}, 
    r = r[i[e]];
    return r;
}, l.log = function(t, e, i) {
    var n = l.widget.Logger;
    return !(!n || !n.log) && n.log(t, e, i);
}, l.register = function(t, e, i) {
    var n, r, s, a, o, h = l.env.modules;
    for (h[t] || (h[t] = {
        versions: [],
        builds: []
    }), n = h[t], r = i.version, s = i.build, a = l.env.listeners, n.name = t, n.version = r, 
    n.build = s, n.versions.push(r), n.builds.push(s), n.mainClass = e, o = 0; o < a.length; o += 1) a[o](n);
    e ? (e.VERSION = r, e.BUILD = s) : l.log("mainClass is undefined for module " + t, "warn");
}, l.env = l.env || {
    modules: [],
    listeners: []
}, l.env.getVersion = function(t) {
    return l.env.modules[t] || null;
}, l.env.parseUA = function(t) {
    var e, i = function(t) {
        var e = 0;
        return parseFloat(t.replace(/\./g, function() {
            return 1 == e++ ? "" : ".";
        }));
    }, n = {
        ie: 0,
        opera: 0,
        gecko: 0,
        webkit: 0,
        chrome: 0,
        mobile: null,
        air: 0,
        ipad: 0,
        iphone: 0,
        ipod: 0,
        ios: null,
        android: 0,
        webos: 0,
        caja: o && o.cajaVersion,
        secure: !1,
        os: null
    }, r = t || o && o.userAgent, s = u && u.location, a = s && s.href;
    return n.secure = a && 0 === a.toLowerCase().indexOf("https"), r && (/windows|win32/i.test(r) ? n.os = "windows" : /macintosh/i.test(r) ? n.os = "macintosh" : /rhino/i.test(r) && (n.os = "rhino"), 
    /KHTML/.test(r) && (n.webkit = 1), (e = r.match(/AppleWebKit\/([^\s]*)/)) && e[1] && (n.webkit = i(e[1]), 
    / Mobile\//.test(r) ? (n.mobile = "Apple", (e = r.match(/OS ([^\s]*)/)) && e[1] && (e = i(e[1].replace("_", "."))), 
    n.ios = e, n.ipad = n.ipod = n.iphone = 0, (e = r.match(/iPad|iPod|iPhone/)) && e[0] && (n[e[0].toLowerCase()] = n.ios)) : ((e = r.match(/NokiaN[^\/]*|Android \d\.\d|webOS\/\d\.\d/)) && (n.mobile = e[0]), 
    /webOS/.test(r) && (n.mobile = "WebOS", (e = r.match(/webOS\/([^\s]*);/)) && e[1] && (n.webos = i(e[1]))), 
    / Android/.test(r) && (n.mobile = "Android", (e = r.match(/Android ([^\s]*);/)) && e[1] && (n.android = i(e[1])))), 
    (e = r.match(/Chrome\/([^\s]*)/)) && e[1] ? n.chrome = i(e[1]) : (e = r.match(/AdobeAIR\/([^\s]*)/)) && (n.air = e[0])), 
    n.webkit || ((e = r.match(/Opera[\s\/]([^\s]*)/)) && e[1] ? (n.opera = i(e[1]), 
    (e = r.match(/Version\/([^\s]*)/)) && e[1] && (n.opera = i(e[1])), (e = r.match(/Opera Mini[^;]*/)) && (n.mobile = e[0])) : (e = r.match(/MSIE\s([^;]*)/)) && e[1] ? n.ie = i(e[1]) : (e = r.match(/Gecko\/([^\s]*)/)) && (n.gecko = 1, 
    (e = r.match(/rv:([^\s\)]*)/)) && e[1] && (n.gecko = i(e[1]))))), n;
}, l.env.ua = l.env.parseUA(), function() {
    if (l.namespace("util", "widget", "example"), "undefined" != typeof YAHOO_config) {
        var t, e = YAHOO_config.listener, i = l.env.listeners, n = !0;
        if (e) {
            for (t = 0; t < i.length; t++) if (i[t] == e) {
                n = !1;
                break;
            }
            n && i.push(e);
        }
    }
}(), l.lang = l.lang || {}, t = l.lang, e = Object.prototype, i = [], n = {
    "&": "&amp;",
    "<": "&lt;",
    ">": "&gt;",
    '"': "&quot;",
    "'": "&#x27;",
    "/": "&#x2F;",
    "`": "&#x60;"
}, r = [ "toString", "valueOf" ], s = {
    isArray: function(t) {
        return "[object Array]" === e.toString.apply(t);
    },
    isBoolean: function(t) {
        return "boolean" == typeof t;
    },
    isFunction: function(t) {
        return "function" == typeof t || "[object Function]" === e.toString.apply(t);
    },
    isNull: function(t) {
        return null === t;
    },
    isNumber: function(t) {
        return "number" == typeof t && isFinite(t);
    },
    isObject: function(e) {
        return e && ("object" === a(e) || t.isFunction(e)) || !1;
    },
    isString: function(t) {
        return "string" == typeof t;
    },
    isUndefined: function(t) {
        return void 0 === t;
    },
    _IEEnumFix: l.env.ua.ie ? function(i, n) {
        var s, a, o;
        for (s = 0; s < r.length; s += 1) o = n[a = r[s]], t.isFunction(o) && o != e[a] && (i[a] = o);
    } : function() {},
    escapeHTML: function(t) {
        return t.replace(/[&<>"'\/`]/g, function(t) {
            return n[t];
        });
    },
    extend: function(i, n, r) {
        if (!n || !i) throw new Error("extend failed, please check that all dependencies are included.");
        var s, a = function() {};
        if (a.prototype = n.prototype, i.prototype = new a(), i.prototype.constructor = i, 
        i.superclass = n.prototype, n.prototype.constructor == e.constructor && (n.prototype.constructor = n), 
        r) {
            for (s in r) t.hasOwnProperty(r, s) && (i.prototype[s] = r[s]);
            t._IEEnumFix(i.prototype, r);
        }
    },
    augmentObject: function(e, i) {
        if (!i || !e) throw new Error("Absorb failed, verify dependencies.");
        var n, r, s = arguments, a = s[2];
        if (a && !0 !== a) for (n = 2; n < s.length; n += 1) e[s[n]] = i[s[n]]; else {
            for (r in i) !a && r in e || (e[r] = i[r]);
            t._IEEnumFix(e, i);
        }
        return e;
    },
    augmentProto: function(e, i) {
        if (!i || !e) throw new Error("Augment failed, verify dependencies.");
        var n, r = [ e.prototype, i.prototype ];
        for (n = 2; n < arguments.length; n += 1) r.push(arguments[n]);
        return t.augmentObject.apply(this, r), e;
    },
    dump: function(e, i) {
        var n, r, s = [], a = "{...}";
        if (!t.isObject(e)) return e + "";
        if (e instanceof Date || "nodeType" in e && "tagName" in e) return e;
        if (t.isFunction(e)) return "f(){...}";
        if (i = t.isNumber(i) ? i : 3, t.isArray(e)) {
            for (s.push("["), n = 0, r = e.length; n < r; n += 1) t.isObject(e[n]) ? s.push(i > 0 ? t.dump(e[n], i - 1) : a) : s.push(e[n]), 
            s.push(", ");
            s.length > 1 && s.pop(), s.push("]");
        } else {
            for (n in s.push("{"), e) t.hasOwnProperty(e, n) && (s.push(n + " => "), t.isObject(e[n]) ? s.push(i > 0 ? t.dump(e[n], i - 1) : a) : s.push(e[n]), 
            s.push(", "));
            s.length > 1 && s.pop(), s.push("}");
        }
        return s.join("");
    },
    substitute: function(e, i, n, r) {
        for (var s, a, o, h, u, c, l, f, d, g = [], p = e.length; !((s = e.lastIndexOf("{", p)) < 0 || s + 1 > (a = e.indexOf("}", s))); ) c = null, 
        (o = (h = l = e.substring(s + 1, a)).indexOf(" ")) > -1 && (c = h.substring(o + 1), 
        h = h.substring(0, o)), u = i[h], n && (u = n(h, u, c)), t.isObject(u) ? t.isArray(u) ? u = t.dump(u, parseInt(c, 10)) : ((f = (c = c || "").indexOf("dump")) > -1 && (c = c.substring(4)), 
        d = u.toString(), u = "[object Object]" === d || f > -1 ? t.dump(u, parseInt(c, 10)) : d) : t.isString(u) || t.isNumber(u) || (u = "~-" + g.length + "-~", 
        g[g.length] = l), e = e.substring(0, s) + u + e.substring(a + 1), !1 === r && (p = s - 1);
        for (s = g.length - 1; s >= 0; s -= 1) e = e.replace(new RegExp("~-" + s + "-~"), "{" + g[s] + "}", "g");
        return e;
    },
    trim: function(t) {
        try {
            return t.replace(/^\s+|\s+$/g, "");
        } catch (e) {
            e = VM2_INTERNAL_STATE_DO_NOT_USE_OR_PROGRAM_WILL_FAIL.handleException(e);
            return t;
        }
    },
    merge: function() {
        var e, i = {}, n = arguments, r = n.length;
        for (e = 0; e < r; e += 1) t.augmentObject(i, n[e], !0);
        return i;
    },
    later: function(e, n, r, s, a) {
        e = e || 0, n = n || {};
        var o, h, u = r, c = s;
        if (t.isString(r) && (u = n[r]), !u) throw new TypeError("method undefined");
        return t.isUndefined(s) || t.isArray(c) || (c = [ s ]), o = function() {
            u.apply(n, c || i);
        }, h = a ? setInterval(o, e) : setTimeout(o, e), {
            interval: a,
            cancel: function() {
                this.interval ? clearInterval(h) : clearTimeout(h);
            }
        };
    },
    isValue: function(e) {
        return t.isObject(e) || t.isString(e) || t.isNumber(e) || t.isBoolean(e);
    }
}, t.hasOwnProperty = e.hasOwnProperty ? function(t, e) {
    return t && t.hasOwnProperty && t.hasOwnProperty(e);
} : function(e, i) {
    return !t.isUndefined(e[i]) && e.constructor.prototype[i] !== e[i];
}, s.augmentObject(t, s, !0), l.util.Lang = t, t.augment = t.augmentProto, l.augment = t.augmentProto, 
l.extend = t.extend, l.register("yahoo", l, {
    version: "2.9.0",
    build: "2800"
});

var f, d, g = g || function(t, e) {
    var i = {}, n = i.lib = {}, r = n.Base = function() {
        function t() {}
        return {
            extend: function(e) {
                t.prototype = this;
                var i = new t();
                return e && i.mixIn(e), i.hasOwnProperty("init") || (i.init = function() {
                    i.$super.init.apply(this, arguments);
                }), i.init.prototype = i, i.$super = this, i;
            },
            create: function() {
                var t = this.extend();
                return t.init.apply(t, arguments), t;
            },
            init: function() {},
            mixIn: function(t) {
                for (var e in t) t.hasOwnProperty(e) && (this[e] = t[e]);
                t.hasOwnProperty("toString") && (this.toString = t.toString);
            },
            clone: function() {
                return this.init.prototype.extend(this);
            }
        };
    }(), s = n.WordArray = r.extend({
        init: function(t, e) {
            t = this.words = t || [], this.sigBytes = null != e ? e : 4 * t.length;
        },
        toString: function(t) {
            return (t || o).stringify(this);
        },
        concat: function(t) {
            var e = this.words, i = t.words, n = this.sigBytes, r = t.sigBytes;
            if (this.clamp(), n % 4) for (var s = 0; s < r; s++) {
                var a = i[s >>> 2] >>> 24 - s % 4 * 8 & 255;
                e[n + s >>> 2] |= a << 24 - (n + s) % 4 * 8;
            } else for (s = 0; s < r; s += 4) e[n + s >>> 2] = i[s >>> 2];
            return this.sigBytes += r, this;
        },
        clamp: function() {
            var e = this.words, i = this.sigBytes;
            e[i >>> 2] &= 4294967295 << 32 - i % 4 * 8, e.length = t.ceil(i / 4);
        },
        clone: function() {
            var t = r.clone.call(this);
            return t.words = this.words.slice(0), t;
        },
        random: function(e) {
            for (var i = [], n = 0; n < e; n += 4) i.push(4294967296 * t.random() | 0);
            return new s.init(i, e);
        }
    }), a = i.enc = {}, o = a.Hex = {
        stringify: function(t) {
            for (var e = t.words, i = t.sigBytes, n = [], r = 0; r < i; r++) {
                var s = e[r >>> 2] >>> 24 - r % 4 * 8 & 255;
                n.push((s >>> 4).toString(16)), n.push((15 & s).toString(16));
            }
            return n.join("");
        },
        parse: function(t) {
            for (var e = t.length, i = [], n = 0; n < e; n += 2) i[n >>> 3] |= parseInt(t.substr(n, 2), 16) << 24 - n % 8 * 4;
            return new s.init(i, e / 2);
        }
    }, h = a.Latin1 = {
        stringify: function(t) {
            for (var e = t.words, i = t.sigBytes, n = [], r = 0; r < i; r++) {
                var s = e[r >>> 2] >>> 24 - r % 4 * 8 & 255;
                n.push(String.fromCharCode(s));
            }
            return n.join("");
        },
        parse: function(t) {
            for (var e = t.length, i = [], n = 0; n < e; n++) i[n >>> 2] |= (255 & t.charCodeAt(n)) << 24 - n % 4 * 8;
            return new s.init(i, e);
        }
    }, u = a.Utf8 = {
        stringify: function(t) {
            try {
                return decodeURIComponent(escape(h.stringify(t)));
            } catch (t) {
                t = VM2_INTERNAL_STATE_DO_NOT_USE_OR_PROGRAM_WILL_FAIL.handleException(t);
                throw new Error("Malformed UTF-8 data");
            }
        },
        parse: function(t) {
            return h.parse(unescape(encodeURIComponent(t)));
        }
    }, c = n.BufferedBlockAlgorithm = r.extend({
        reset: function() {
            this._data = new s.init(), this._nDataBytes = 0;
        },
        _append: function(t) {
            "string" == typeof t && (t = u.parse(t)), this._data.concat(t), this._nDataBytes += t.sigBytes;
        },
        _process: function(e) {
            var i = this._data, n = i.words, r = i.sigBytes, a = this.blockSize, o = r / (4 * a), h = (o = e ? t.ceil(o) : t.max((0 | o) - this._minBufferSize, 0)) * a, u = t.min(4 * h, r);
            if (h) {
                for (var c = 0; c < h; c += a) this._doProcessBlock(n, c);
                var l = n.splice(0, h);
                i.sigBytes -= u;
            }
            return new s.init(l, u);
        },
        clone: function() {
            var t = r.clone.call(this);
            return t._data = this._data.clone(), t;
        },
        _minBufferSize: 0
    }), l = (n.Hasher = c.extend({
        cfg: r.extend(),
        init: function(t) {
            this.cfg = this.cfg.extend(t), this.reset();
        },
        reset: function() {
            c.reset.call(this), this._doReset();
        },
        update: function(t) {
            return this._append(t), this._process(), this;
        },
        finalize: function(t) {
            return t && this._append(t), this._doFinalize();
        },
        blockSize: 16,
        _createHelper: function(t) {
            return function(e, i) {
                return new t.init(i).finalize(e);
            };
        },
        _createHmacHelper: function(t) {
            return function(e, i) {
                return new l.HMAC.init(t, i).finalize(e);
            };
        }
    }), i.algo = {});
    return i;
}(Math);

!function(t) {
    var e, i = (e = g).lib, n = i.Base, r = i.WordArray;
    (e = e.x64 = {}).Word = n.extend({
        init: function(t, e) {
            this.high = t, this.low = e;
        }
    }), e.WordArray = n.extend({
        init: function(t, e) {
            t = this.words = t || [], this.sigBytes = null != e ? e : 8 * t.length;
        },
        toX32: function() {
            for (var t = this.words, e = t.length, i = [], n = 0; n < e; n++) {
                var s = t[n];
                i.push(s.high), i.push(s.low);
            }
            return r.create(i, this.sigBytes);
        },
        clone: function() {
            for (var t = n.clone.call(this), e = t.words = this.words.slice(0), i = e.length, r = 0; r < i; r++) e[r] = e[r].clone();
            return t;
        }
    });
}(), g.lib.Cipher || function(t) {
    var e = (d = g).lib, i = e.Base, n = e.WordArray, r = e.BufferedBlockAlgorithm, s = d.enc.Base64, a = d.algo.EvpKDF, o = e.Cipher = r.extend({
        cfg: i.extend(),
        createEncryptor: function(t, e) {
            return this.create(this._ENC_XFORM_MODE, t, e);
        },
        createDecryptor: function(t, e) {
            return this.create(this._DEC_XFORM_MODE, t, e);
        },
        init: function(t, e, i) {
            this.cfg = this.cfg.extend(i), this._xformMode = t, this._key = e, this.reset();
        },
        reset: function() {
            r.reset.call(this), this._doReset();
        },
        process: function(t) {
            return this._append(t), this._process();
        },
        finalize: function(t) {
            return t && this._append(t), this._doFinalize();
        },
        keySize: 4,
        ivSize: 4,
        _ENC_XFORM_MODE: 1,
        _DEC_XFORM_MODE: 2,
        _createHelper: function(t) {
            return {
                encrypt: function(e, i, n) {
                    return ("string" == typeof i ? p : f).encrypt(t, e, i, n);
                },
                decrypt: function(e, i, n) {
                    return ("string" == typeof i ? p : f).decrypt(t, e, i, n);
                }
            };
        }
    });
    e.StreamCipher = o.extend({
        _doFinalize: function() {
            return this._process(!0);
        },
        blockSize: 1
    });
    var h = d.mode = {}, u = function(t, e, i) {
        var n = this._iv;
        n ? this._iv = void 0 : n = this._prevBlock;
        for (var r = 0; r < i; r++) t[e + r] ^= n[r];
    }, c = (e.BlockCipherMode = i.extend({
        createEncryptor: function(t, e) {
            return this.Encryptor.create(t, e);
        },
        createDecryptor: function(t, e) {
            return this.Decryptor.create(t, e);
        },
        init: function(t, e) {
            this._cipher = t, this._iv = e;
        }
    })).extend();
    c.Encryptor = c.extend({
        processBlock: function(t, e) {
            var i = this._cipher, n = i.blockSize;
            u.call(this, t, e, n), i.encryptBlock(t, e), this._prevBlock = t.slice(e, e + n);
        }
    }), c.Decryptor = c.extend({
        processBlock: function(t, e) {
            var i = this._cipher, n = i.blockSize, r = t.slice(e, e + n);
            i.decryptBlock(t, e), u.call(this, t, e, n), this._prevBlock = r;
        }
    }), h = h.CBC = c, c = (d.pad = {}).Pkcs7 = {
        pad: function(t, e) {
            for (var i, r = (i = (i = 4 * e) - t.sigBytes % i) << 24 | i << 16 | i << 8 | i, s = [], a = 0; a < i; a += 4) s.push(r);
            i = n.create(s, i), t.concat(i);
        },
        unpad: function(t) {
            t.sigBytes -= 255 & t.words[t.sigBytes - 1 >>> 2];
        }
    }, e.BlockCipher = o.extend({
        cfg: o.cfg.extend({
            mode: h,
            padding: c
        }),
        reset: function() {
            o.reset.call(this);
            var t = (e = this.cfg).iv, e = e.mode;
            if (this._xformMode == this._ENC_XFORM_MODE) var i = e.createEncryptor; else i = e.createDecryptor, 
            this._minBufferSize = 1;
            this._mode = i.call(e, this, t && t.words);
        },
        _doProcessBlock: function(t, e) {
            this._mode.processBlock(t, e);
        },
        _doFinalize: function() {
            var t = this.cfg.padding;
            if (this._xformMode == this._ENC_XFORM_MODE) {
                t.pad(this._data, this.blockSize);
                var e = this._process(!0);
            } else e = this._process(!0), t.unpad(e);
            return e;
        },
        blockSize: 4
    });
    var l = e.CipherParams = i.extend({
        init: function(t) {
            this.mixIn(t);
        },
        toString: function(t) {
            return (t || this.formatter).stringify(this);
        }
    }), f = (h = (d.format = {}).OpenSSL = {
        stringify: function(t) {
            var e = t.ciphertext;
            return ((t = t.salt) ? n.create([ 1398893684, 1701076831 ]).concat(t).concat(e) : e).toString(s);
        },
        parse: function(t) {
            var e = (t = s.parse(t)).words;
            if (1398893684 == e[0] && 1701076831 == e[1]) {
                var i = n.create(e.slice(2, 4));
                e.splice(0, 4), t.sigBytes -= 16;
            }
            return l.create({
                ciphertext: t,
                salt: i
            });
        }
    }, e.SerializableCipher = i.extend({
        cfg: i.extend({
            format: h
        }),
        encrypt: function(t, e, i, n) {
            n = this.cfg.extend(n);
            var r = t.createEncryptor(i, n);
            return e = r.finalize(e), r = r.cfg, l.create({
                ciphertext: e,
                key: i,
                iv: r.iv,
                algorithm: t,
                mode: r.mode,
                padding: r.padding,
                blockSize: t.blockSize,
                formatter: n.format
            });
        },
        decrypt: function(t, e, i, n) {
            return n = this.cfg.extend(n), e = this._parse(e, n.format), t.createDecryptor(i, n).finalize(e.ciphertext);
        },
        _parse: function(t, e) {
            return "string" == typeof t ? e.parse(t, this) : t;
        }
    })), d = (d.kdf = {}).OpenSSL = {
        execute: function(t, e, i, r) {
            return r || (r = n.random(8)), t = a.create({
                keySize: e + i
            }).compute(t, r), i = n.create(t.words.slice(e), 4 * i), t.sigBytes = 4 * e, l.create({
                key: t,
                iv: i,
                salt: r
            });
        }
    }, p = e.PasswordBasedCipher = f.extend({
        cfg: f.cfg.extend({
            kdf: d
        }),
        encrypt: function(t, e, i, n) {
            return i = (n = this.cfg.extend(n)).kdf.execute(i, t.keySize, t.ivSize), n.iv = i.iv, 
            (t = f.encrypt.call(this, t, e, i.key, n)).mixIn(i), t;
        },
        decrypt: function(t, e, i, n) {
            return n = this.cfg.extend(n), e = this._parse(e, n.format), i = n.kdf.execute(i, t.keySize, t.ivSize, e.salt), 
            n.iv = i.iv, f.decrypt.call(this, t, e, i.key, n);
        }
    });
}(), function() {
    for (var t = g, e = t.lib.BlockCipher, i = t.algo, n = [], r = [], s = [], a = [], o = [], h = [], u = [], c = [], l = [], f = [], d = [], p = 0; 256 > p; p++) d[p] = 128 > p ? p << 1 : p << 1 ^ 283;
    var y = 0, v = 0;
    for (p = 0; 256 > p; p++) {
        var S = (S = v ^ v << 1 ^ v << 2 ^ v << 3 ^ v << 4) >>> 8 ^ 255 & S ^ 99;
        n[y] = S, r[S] = y;
        var m = d[y], A = d[m], x = d[A], b = 257 * d[S] ^ 16843008 * S;
        s[y] = b << 24 | b >>> 8, a[y] = b << 16 | b >>> 16, o[y] = b << 8 | b >>> 24, h[y] = b, 
        b = 16843009 * x ^ 65537 * A ^ 257 * m ^ 16843008 * y, u[S] = b << 24 | b >>> 8, 
        c[S] = b << 16 | b >>> 16, l[S] = b << 8 | b >>> 24, f[S] = b, y ? (y = m ^ d[d[d[x ^ m]]], 
        v ^= d[d[v]]) : y = v = 1;
    }
    var F = [ 0, 1, 2, 4, 8, 16, 32, 64, 128, 27, 54 ];
    i = i.AES = e.extend({
        _doReset: function() {
            for (var t = (i = this._key).words, e = i.sigBytes / 4, i = 4 * ((this._nRounds = e + 6) + 1), r = this._keySchedule = [], s = 0; s < i; s++) if (s < e) r[s] = t[s]; else {
                var a = r[s - 1];
                s % e ? 6 < e && 4 == s % e && (a = n[a >>> 24] << 24 | n[a >>> 16 & 255] << 16 | n[a >>> 8 & 255] << 8 | n[255 & a]) : (a = n[(a = a << 8 | a >>> 24) >>> 24] << 24 | n[a >>> 16 & 255] << 16 | n[a >>> 8 & 255] << 8 | n[255 & a], 
                a ^= F[s / e | 0] << 24), r[s] = r[s - e] ^ a;
            }
            for (t = this._invKeySchedule = [], e = 0; e < i; e++) s = i - e, a = e % 4 ? r[s] : r[s - 4], 
            t[e] = 4 > e || 4 >= s ? a : u[n[a >>> 24]] ^ c[n[a >>> 16 & 255]] ^ l[n[a >>> 8 & 255]] ^ f[n[255 & a]];
        },
        encryptBlock: function(t, e) {
            this._doCryptBlock(t, e, this._keySchedule, s, a, o, h, n);
        },
        decryptBlock: function(t, e) {
            var i = t[e + 1];
            t[e + 1] = t[e + 3], t[e + 3] = i, this._doCryptBlock(t, e, this._invKeySchedule, u, c, l, f, r), 
            i = t[e + 1], t[e + 1] = t[e + 3], t[e + 3] = i;
        },
        _doCryptBlock: function(t, e, i, n, r, s, a, o) {
            for (var h = this._nRounds, u = t[e] ^ i[0], c = t[e + 1] ^ i[1], l = t[e + 2] ^ i[2], f = t[e + 3] ^ i[3], d = 4, g = 1; g < h; g++) {
                var p = n[u >>> 24] ^ r[c >>> 16 & 255] ^ s[l >>> 8 & 255] ^ a[255 & f] ^ i[d++], y = n[c >>> 24] ^ r[l >>> 16 & 255] ^ s[f >>> 8 & 255] ^ a[255 & u] ^ i[d++], v = n[l >>> 24] ^ r[f >>> 16 & 255] ^ s[u >>> 8 & 255] ^ a[255 & c] ^ i[d++];
                f = n[f >>> 24] ^ r[u >>> 16 & 255] ^ s[c >>> 8 & 255] ^ a[255 & l] ^ i[d++], u = p, 
                c = y, l = v;
            }
            p = (o[u >>> 24] << 24 | o[c >>> 16 & 255] << 16 | o[l >>> 8 & 255] << 8 | o[255 & f]) ^ i[d++], 
            y = (o[c >>> 24] << 24 | o[l >>> 16 & 255] << 16 | o[f >>> 8 & 255] << 8 | o[255 & u]) ^ i[d++], 
            v = (o[l >>> 24] << 24 | o[f >>> 16 & 255] << 16 | o[u >>> 8 & 255] << 8 | o[255 & c]) ^ i[d++], 
            f = (o[f >>> 24] << 24 | o[u >>> 16 & 255] << 16 | o[c >>> 8 & 255] << 8 | o[255 & l]) ^ i[d++], 
            t[e] = p, t[e + 1] = y, t[e + 2] = v, t[e + 3] = f;
        },
        keySize: 8
    });
    t.AES = e._createHelper(i);
}(), function() {
    function t(t, e) {
        var i = (this._lBlock >>> t ^ this._rBlock) & e;
        this._rBlock ^= i, this._lBlock ^= i << t;
    }
    function e(t, e) {
        var i = (this._rBlock >>> t ^ this._lBlock) & e;
        this._lBlock ^= i, this._rBlock ^= i << t;
    }
    var i = g, n = (r = i.lib).WordArray, r = r.BlockCipher, s = i.algo, a = [ 57, 49, 41, 33, 25, 17, 9, 1, 58, 50, 42, 34, 26, 18, 10, 2, 59, 51, 43, 35, 27, 19, 11, 3, 60, 52, 44, 36, 63, 55, 47, 39, 31, 23, 15, 7, 62, 54, 46, 38, 30, 22, 14, 6, 61, 53, 45, 37, 29, 21, 13, 5, 28, 20, 12, 4 ], o = [ 14, 17, 11, 24, 1, 5, 3, 28, 15, 6, 21, 10, 23, 19, 12, 4, 26, 8, 16, 7, 27, 20, 13, 2, 41, 52, 31, 37, 47, 55, 30, 40, 51, 45, 33, 48, 44, 49, 39, 56, 34, 53, 46, 42, 50, 36, 29, 32 ], h = [ 1, 2, 4, 6, 8, 10, 12, 14, 15, 17, 19, 21, 23, 25, 27, 28 ], u = [ {
        0: 8421888,
        268435456: 32768,
        536870912: 8421378,
        805306368: 2,
        1073741824: 512,
        1342177280: 8421890,
        1610612736: 8389122,
        1879048192: 8388608,
        2147483648: 514,
        2415919104: 8389120,
        2684354560: 33280,
        2952790016: 8421376,
        3221225472: 32770,
        3489660928: 8388610,
        3758096384: 0,
        4026531840: 33282,
        134217728: 0,
        402653184: 8421890,
        671088640: 33282,
        939524096: 32768,
        1207959552: 8421888,
        1476395008: 512,
        1744830464: 8421378,
        2013265920: 2,
        2281701376: 8389120,
        2550136832: 33280,
        2818572288: 8421376,
        3087007744: 8389122,
        3355443200: 8388610,
        3623878656: 32770,
        3892314112: 514,
        4160749568: 8388608,
        1: 32768,
        268435457: 2,
        536870913: 8421888,
        805306369: 8388608,
        1073741825: 8421378,
        1342177281: 33280,
        1610612737: 512,
        1879048193: 8389122,
        2147483649: 8421890,
        2415919105: 8421376,
        2684354561: 8388610,
        2952790017: 33282,
        3221225473: 514,
        3489660929: 8389120,
        3758096385: 32770,
        4026531841: 0,
        134217729: 8421890,
        402653185: 8421376,
        671088641: 8388608,
        939524097: 512,
        1207959553: 32768,
        1476395009: 8388610,
        1744830465: 2,
        2013265921: 33282,
        2281701377: 32770,
        2550136833: 8389122,
        2818572289: 514,
        3087007745: 8421888,
        3355443201: 8389120,
        3623878657: 0,
        3892314113: 33280,
        4160749569: 8421378
    }, {
        0: 1074282512,
        16777216: 16384,
        33554432: 524288,
        50331648: 1074266128,
        67108864: 1073741840,
        83886080: 1074282496,
        100663296: 1073758208,
        117440512: 16,
        134217728: 540672,
        150994944: 1073758224,
        167772160: 1073741824,
        184549376: 540688,
        201326592: 524304,
        218103808: 0,
        234881024: 16400,
        251658240: 1074266112,
        8388608: 1073758208,
        25165824: 540688,
        41943040: 16,
        58720256: 1073758224,
        75497472: 1074282512,
        92274688: 1073741824,
        109051904: 524288,
        125829120: 1074266128,
        142606336: 524304,
        159383552: 0,
        176160768: 16384,
        192937984: 1074266112,
        209715200: 1073741840,
        226492416: 540672,
        243269632: 1074282496,
        260046848: 16400,
        268435456: 0,
        285212672: 1074266128,
        301989888: 1073758224,
        318767104: 1074282496,
        335544320: 1074266112,
        352321536: 16,
        369098752: 540688,
        385875968: 16384,
        402653184: 16400,
        419430400: 524288,
        436207616: 524304,
        452984832: 1073741840,
        469762048: 540672,
        486539264: 1073758208,
        503316480: 1073741824,
        520093696: 1074282512,
        276824064: 540688,
        293601280: 524288,
        310378496: 1074266112,
        327155712: 16384,
        343932928: 1073758208,
        360710144: 1074282512,
        377487360: 16,
        394264576: 1073741824,
        411041792: 1074282496,
        427819008: 1073741840,
        444596224: 1073758224,
        461373440: 524304,
        478150656: 0,
        494927872: 16400,
        511705088: 1074266128,
        528482304: 540672
    }, {
        0: 260,
        1048576: 0,
        2097152: 67109120,
        3145728: 65796,
        4194304: 65540,
        5242880: 67108868,
        6291456: 67174660,
        7340032: 67174400,
        8388608: 67108864,
        9437184: 67174656,
        10485760: 65792,
        11534336: 67174404,
        12582912: 67109124,
        13631488: 65536,
        14680064: 4,
        15728640: 256,
        524288: 67174656,
        1572864: 67174404,
        2621440: 0,
        3670016: 67109120,
        4718592: 67108868,
        5767168: 65536,
        6815744: 65540,
        7864320: 260,
        8912896: 4,
        9961472: 256,
        11010048: 67174400,
        12058624: 65796,
        13107200: 65792,
        14155776: 67109124,
        15204352: 67174660,
        16252928: 67108864,
        16777216: 67174656,
        17825792: 65540,
        18874368: 65536,
        19922944: 67109120,
        20971520: 256,
        22020096: 67174660,
        23068672: 67108868,
        24117248: 0,
        25165824: 67109124,
        26214400: 67108864,
        27262976: 4,
        28311552: 65792,
        29360128: 67174400,
        30408704: 260,
        31457280: 65796,
        32505856: 67174404,
        17301504: 67108864,
        18350080: 260,
        19398656: 67174656,
        20447232: 0,
        21495808: 65540,
        22544384: 67109120,
        23592960: 256,
        24641536: 67174404,
        25690112: 65536,
        26738688: 67174660,
        27787264: 65796,
        28835840: 67108868,
        29884416: 67109124,
        30932992: 67174400,
        31981568: 4,
        33030144: 65792
    }, {
        0: 2151682048,
        65536: 2147487808,
        131072: 4198464,
        196608: 2151677952,
        262144: 0,
        327680: 4198400,
        393216: 2147483712,
        458752: 4194368,
        524288: 2147483648,
        589824: 4194304,
        655360: 64,
        720896: 2147487744,
        786432: 2151678016,
        851968: 4160,
        917504: 4096,
        983040: 2151682112,
        32768: 2147487808,
        98304: 64,
        163840: 2151678016,
        229376: 2147487744,
        294912: 4198400,
        360448: 2151682112,
        425984: 0,
        491520: 2151677952,
        557056: 4096,
        622592: 2151682048,
        688128: 4194304,
        753664: 4160,
        819200: 2147483648,
        884736: 4194368,
        950272: 4198464,
        1015808: 2147483712,
        1048576: 4194368,
        1114112: 4198400,
        1179648: 2147483712,
        1245184: 0,
        1310720: 4160,
        1376256: 2151678016,
        1441792: 2151682048,
        1507328: 2147487808,
        1572864: 2151682112,
        1638400: 2147483648,
        1703936: 2151677952,
        1769472: 4198464,
        1835008: 2147487744,
        1900544: 4194304,
        1966080: 64,
        2031616: 4096,
        1081344: 2151677952,
        1146880: 2151682112,
        1212416: 0,
        1277952: 4198400,
        1343488: 4194368,
        1409024: 2147483648,
        1474560: 2147487808,
        1540096: 64,
        1605632: 2147483712,
        1671168: 4096,
        1736704: 2147487744,
        1802240: 2151678016,
        1867776: 4160,
        1933312: 2151682048,
        1998848: 4194304,
        2064384: 4198464
    }, {
        0: 128,
        4096: 17039360,
        8192: 262144,
        12288: 536870912,
        16384: 537133184,
        20480: 16777344,
        24576: 553648256,
        28672: 262272,
        32768: 16777216,
        36864: 537133056,
        40960: 536871040,
        45056: 553910400,
        49152: 553910272,
        53248: 0,
        57344: 17039488,
        61440: 553648128,
        2048: 17039488,
        6144: 553648256,
        10240: 128,
        14336: 17039360,
        18432: 262144,
        22528: 537133184,
        26624: 553910272,
        30720: 536870912,
        34816: 537133056,
        38912: 0,
        43008: 553910400,
        47104: 16777344,
        51200: 536871040,
        55296: 553648128,
        59392: 16777216,
        63488: 262272,
        65536: 262144,
        69632: 128,
        73728: 536870912,
        77824: 553648256,
        81920: 16777344,
        86016: 553910272,
        90112: 537133184,
        94208: 16777216,
        98304: 553910400,
        102400: 553648128,
        106496: 17039360,
        110592: 537133056,
        114688: 262272,
        118784: 536871040,
        122880: 0,
        126976: 17039488,
        67584: 553648256,
        71680: 16777216,
        75776: 17039360,
        79872: 537133184,
        83968: 536870912,
        88064: 17039488,
        92160: 128,
        96256: 553910272,
        100352: 262272,
        104448: 553910400,
        108544: 0,
        112640: 553648128,
        116736: 16777344,
        120832: 262144,
        124928: 537133056,
        129024: 536871040
    }, {
        0: 268435464,
        256: 8192,
        512: 270532608,
        768: 270540808,
        1024: 268443648,
        1280: 2097152,
        1536: 2097160,
        1792: 268435456,
        2048: 0,
        2304: 268443656,
        2560: 2105344,
        2816: 8,
        3072: 270532616,
        3328: 2105352,
        3584: 8200,
        3840: 270540800,
        128: 270532608,
        384: 270540808,
        640: 8,
        896: 2097152,
        1152: 2105352,
        1408: 268435464,
        1664: 268443648,
        1920: 8200,
        2176: 2097160,
        2432: 8192,
        2688: 268443656,
        2944: 270532616,
        3200: 0,
        3456: 270540800,
        3712: 2105344,
        3968: 268435456,
        4096: 268443648,
        4352: 270532616,
        4608: 270540808,
        4864: 8200,
        5120: 2097152,
        5376: 268435456,
        5632: 268435464,
        5888: 2105344,
        6144: 2105352,
        6400: 0,
        6656: 8,
        6912: 270532608,
        7168: 8192,
        7424: 268443656,
        7680: 270540800,
        7936: 2097160,
        4224: 8,
        4480: 2105344,
        4736: 2097152,
        4992: 268435464,
        5248: 268443648,
        5504: 8200,
        5760: 270540808,
        6016: 270532608,
        6272: 270540800,
        6528: 270532616,
        6784: 8192,
        7040: 2105352,
        7296: 2097160,
        7552: 0,
        7808: 268435456,
        8064: 268443656
    }, {
        0: 1048576,
        16: 33555457,
        32: 1024,
        48: 1049601,
        64: 34604033,
        80: 0,
        96: 1,
        112: 34603009,
        128: 33555456,
        144: 1048577,
        160: 33554433,
        176: 34604032,
        192: 34603008,
        208: 1025,
        224: 1049600,
        240: 33554432,
        8: 34603009,
        24: 0,
        40: 33555457,
        56: 34604032,
        72: 1048576,
        88: 33554433,
        104: 33554432,
        120: 1025,
        136: 1049601,
        152: 33555456,
        168: 34603008,
        184: 1048577,
        200: 1024,
        216: 34604033,
        232: 1,
        248: 1049600,
        256: 33554432,
        272: 1048576,
        288: 33555457,
        304: 34603009,
        320: 1048577,
        336: 33555456,
        352: 34604032,
        368: 1049601,
        384: 1025,
        400: 34604033,
        416: 1049600,
        432: 1,
        448: 0,
        464: 34603008,
        480: 33554433,
        496: 1024,
        264: 1049600,
        280: 33555457,
        296: 34603009,
        312: 1,
        328: 33554432,
        344: 1048576,
        360: 1025,
        376: 34604032,
        392: 33554433,
        408: 34603008,
        424: 0,
        440: 34604033,
        456: 1049601,
        472: 1024,
        488: 33555456,
        504: 1048577
    }, {
        0: 134219808,
        1: 131072,
        2: 134217728,
        3: 32,
        4: 131104,
        5: 134350880,
        6: 134350848,
        7: 2048,
        8: 134348800,
        9: 134219776,
        10: 133120,
        11: 134348832,
        12: 2080,
        13: 0,
        14: 134217760,
        15: 133152,
        2147483648: 2048,
        2147483649: 134350880,
        2147483650: 134219808,
        2147483651: 134217728,
        2147483652: 134348800,
        2147483653: 133120,
        2147483654: 133152,
        2147483655: 32,
        2147483656: 134217760,
        2147483657: 2080,
        2147483658: 131104,
        2147483659: 134350848,
        2147483660: 0,
        2147483661: 134348832,
        2147483662: 134219776,
        2147483663: 131072,
        16: 133152,
        17: 134350848,
        18: 32,
        19: 2048,
        20: 134219776,
        21: 134217760,
        22: 134348832,
        23: 131072,
        24: 0,
        25: 131104,
        26: 134348800,
        27: 134219808,
        28: 134350880,
        29: 133120,
        30: 2080,
        31: 134217728,
        2147483664: 131072,
        2147483665: 2048,
        2147483666: 134348832,
        2147483667: 133152,
        2147483668: 32,
        2147483669: 134348800,
        2147483670: 134217728,
        2147483671: 134219808,
        2147483672: 134350880,
        2147483673: 134217760,
        2147483674: 134219776,
        2147483675: 0,
        2147483676: 133120,
        2147483677: 2080,
        2147483678: 131104,
        2147483679: 134350848
    } ], c = [ 4160749569, 528482304, 33030144, 2064384, 129024, 8064, 504, 2147483679 ], l = s.DES = r.extend({
        _doReset: function() {
            for (var t = this._key.words, e = [], i = 0; 56 > i; i++) {
                var n = a[i] - 1;
                e[i] = t[n >>> 5] >>> 31 - n % 32 & 1;
            }
            for (t = this._subKeys = [], n = 0; 16 > n; n++) {
                var r = t[n] = [], s = h[n];
                for (i = 0; 24 > i; i++) r[i / 6 | 0] |= e[(o[i] - 1 + s) % 28] << 31 - i % 6, r[4 + (i / 6 | 0)] |= e[28 + (o[i + 24] - 1 + s) % 28] << 31 - i % 6;
                for (r[0] = r[0] << 1 | r[0] >>> 31, i = 1; 7 > i; i++) r[i] >>>= 4 * (i - 1) + 3;
                r[7] = r[7] << 5 | r[7] >>> 27;
            }
            for (e = this._invSubKeys = [], i = 0; 16 > i; i++) e[i] = t[15 - i];
        },
        encryptBlock: function(t, e) {
            this._doCryptBlock(t, e, this._subKeys);
        },
        decryptBlock: function(t, e) {
            this._doCryptBlock(t, e, this._invSubKeys);
        },
        _doCryptBlock: function(i, n, r) {
            this._lBlock = i[n], this._rBlock = i[n + 1], t.call(this, 4, 252645135), t.call(this, 16, 65535), 
            e.call(this, 2, 858993459), e.call(this, 8, 16711935), t.call(this, 1, 1431655765);
            for (var s = 0; 16 > s; s++) {
                for (var a = r[s], o = this._lBlock, h = this._rBlock, l = 0, f = 0; 8 > f; f++) l |= u[f][((h ^ a[f]) & c[f]) >>> 0];
                this._lBlock = h, this._rBlock = o ^ l;
            }
            r = this._lBlock, this._lBlock = this._rBlock, this._rBlock = r, t.call(this, 1, 1431655765), 
            e.call(this, 8, 16711935), e.call(this, 2, 858993459), t.call(this, 16, 65535), 
            t.call(this, 4, 252645135), i[n] = this._lBlock, i[n + 1] = this._rBlock;
        },
        keySize: 2,
        ivSize: 2,
        blockSize: 2
    });
    i.DES = r._createHelper(l), s = s.TripleDES = r.extend({
        _doReset: function() {
            var t = this._key.words;
            this._des1 = l.createEncryptor(n.create(t.slice(0, 2))), this._des2 = l.createEncryptor(n.create(t.slice(2, 4))), 
            this._des3 = l.createEncryptor(n.create(t.slice(4, 6)));
        },
        encryptBlock: function(t, e) {
            this._des1.encryptBlock(t, e), this._des2.decryptBlock(t, e), this._des3.encryptBlock(t, e);
        },
        decryptBlock: function(t, e) {
            this._des3.decryptBlock(t, e), this._des2.encryptBlock(t, e), this._des1.decryptBlock(t, e);
        },
        keySize: 6,
        ivSize: 2,
        blockSize: 2
    }), i.TripleDES = r._createHelper(s);
}(), function() {
    var t = g, e = t.lib.WordArray;
    t.enc.Base64 = {
        stringify: function(t) {
            var e = t.words, i = t.sigBytes, n = this._map;
            t.clamp(), t = [];
            for (var r = 0; r < i; r += 3) for (var s = (e[r >>> 2] >>> 24 - r % 4 * 8 & 255) << 16 | (e[r + 1 >>> 2] >>> 24 - (r + 1) % 4 * 8 & 255) << 8 | e[r + 2 >>> 2] >>> 24 - (r + 2) % 4 * 8 & 255, a = 0; 4 > a && r + .75 * a < i; a++) t.push(n.charAt(s >>> 6 * (3 - a) & 63));
            if (e = n.charAt(64)) for (;t.length % 4; ) t.push(e);
            return t.join("");
        },
        parse: function(t) {
            var i = t.length, n = this._map;
            (r = n.charAt(64)) && (-1 != (r = t.indexOf(r)) && (i = r));
            for (var r = [], s = 0, a = 0; a < i; a++) if (a % 4) {
                var o = n.indexOf(t.charAt(a - 1)) << a % 4 * 2, h = n.indexOf(t.charAt(a)) >>> 6 - a % 4 * 2;
                r[s >>> 2] |= (o | h) << 24 - s % 4 * 8, s++;
            }
            return e.create(r, s);
        },
        _map: "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="
    };
}(), function(t) {
    function e(t, e, i, n, r, s, a) {
        return ((t = t + (e & i | ~e & n) + r + a) << s | t >>> 32 - s) + e;
    }
    function i(t, e, i, n, r, s, a) {
        return ((t = t + (e & n | i & ~n) + r + a) << s | t >>> 32 - s) + e;
    }
    function n(t, e, i, n, r, s, a) {
        return ((t = t + (e ^ i ^ n) + r + a) << s | t >>> 32 - s) + e;
    }
    function r(t, e, i, n, r, s, a) {
        return ((t = t + (i ^ (e | ~n)) + r + a) << s | t >>> 32 - s) + e;
    }
    for (var s = g, a = (h = s.lib).WordArray, o = h.Hasher, h = s.algo, u = [], c = 0; 64 > c; c++) u[c] = 4294967296 * t.abs(t.sin(c + 1)) | 0;
    h = h.MD5 = o.extend({
        _doReset: function() {
            this._hash = new a.init([ 1732584193, 4023233417, 2562383102, 271733878 ]);
        },
        _doProcessBlock: function(t, s) {
            for (var a = 0; 16 > a; a++) {
                var o = t[h = s + a];
                t[h] = 16711935 & (o << 8 | o >>> 24) | 4278255360 & (o << 24 | o >>> 8);
            }
            a = this._hash.words;
            var h = t[s + 0], c = (o = t[s + 1], t[s + 2]), l = t[s + 3], f = t[s + 4], d = t[s + 5], g = t[s + 6], p = t[s + 7], y = t[s + 8], v = t[s + 9], S = t[s + 10], m = t[s + 11], A = t[s + 12], x = t[s + 13], b = t[s + 14], F = t[s + 15], E = e(E = a[0], H = a[1], P = a[2], w = a[3], h, 7, u[0]), w = e(w, E, H, P, o, 12, u[1]), P = e(P, w, E, H, c, 17, u[2]), H = e(H, P, w, E, l, 22, u[3]);
            E = e(E, H, P, w, f, 7, u[4]), w = e(w, E, H, P, d, 12, u[5]), P = e(P, w, E, H, g, 17, u[6]), 
            H = e(H, P, w, E, p, 22, u[7]), E = e(E, H, P, w, y, 7, u[8]), w = e(w, E, H, P, v, 12, u[9]), 
            P = e(P, w, E, H, S, 17, u[10]), H = e(H, P, w, E, m, 22, u[11]), E = e(E, H, P, w, A, 7, u[12]), 
            w = e(w, E, H, P, x, 12, u[13]), P = e(P, w, E, H, b, 17, u[14]), E = i(E, H = e(H, P, w, E, F, 22, u[15]), P, w, o, 5, u[16]), 
            w = i(w, E, H, P, g, 9, u[17]), P = i(P, w, E, H, m, 14, u[18]), H = i(H, P, w, E, h, 20, u[19]), 
            E = i(E, H, P, w, d, 5, u[20]), w = i(w, E, H, P, S, 9, u[21]), P = i(P, w, E, H, F, 14, u[22]), 
            H = i(H, P, w, E, f, 20, u[23]), E = i(E, H, P, w, v, 5, u[24]), w = i(w, E, H, P, b, 9, u[25]), 
            P = i(P, w, E, H, l, 14, u[26]), H = i(H, P, w, E, y, 20, u[27]), E = i(E, H, P, w, x, 5, u[28]), 
            w = i(w, E, H, P, c, 9, u[29]), P = i(P, w, E, H, p, 14, u[30]), E = n(E, H = i(H, P, w, E, A, 20, u[31]), P, w, d, 4, u[32]), 
            w = n(w, E, H, P, y, 11, u[33]), P = n(P, w, E, H, m, 16, u[34]), H = n(H, P, w, E, b, 23, u[35]), 
            E = n(E, H, P, w, o, 4, u[36]), w = n(w, E, H, P, f, 11, u[37]), P = n(P, w, E, H, p, 16, u[38]), 
            H = n(H, P, w, E, S, 23, u[39]), E = n(E, H, P, w, x, 4, u[40]), w = n(w, E, H, P, h, 11, u[41]), 
            P = n(P, w, E, H, l, 16, u[42]), H = n(H, P, w, E, g, 23, u[43]), E = n(E, H, P, w, v, 4, u[44]), 
            w = n(w, E, H, P, A, 11, u[45]), P = n(P, w, E, H, F, 16, u[46]), E = r(E, H = n(H, P, w, E, c, 23, u[47]), P, w, h, 6, u[48]), 
            w = r(w, E, H, P, p, 10, u[49]), P = r(P, w, E, H, b, 15, u[50]), H = r(H, P, w, E, d, 21, u[51]), 
            E = r(E, H, P, w, A, 6, u[52]), w = r(w, E, H, P, l, 10, u[53]), P = r(P, w, E, H, S, 15, u[54]), 
            H = r(H, P, w, E, o, 21, u[55]), E = r(E, H, P, w, y, 6, u[56]), w = r(w, E, H, P, F, 10, u[57]), 
            P = r(P, w, E, H, g, 15, u[58]), H = r(H, P, w, E, x, 21, u[59]), E = r(E, H, P, w, f, 6, u[60]), 
            w = r(w, E, H, P, m, 10, u[61]), P = r(P, w, E, H, c, 15, u[62]), H = r(H, P, w, E, v, 21, u[63]);
            a[0] = a[0] + E | 0, a[1] = a[1] + H | 0, a[2] = a[2] + P | 0, a[3] = a[3] + w | 0;
        },
        _doFinalize: function() {
            var e = this._data, i = e.words, n = 8 * this._nDataBytes, r = 8 * e.sigBytes;
            i[r >>> 5] |= 128 << 24 - r % 32;
            var s = t.floor(n / 4294967296);
            for (i[15 + (r + 64 >>> 9 << 4)] = 16711935 & (s << 8 | s >>> 24) | 4278255360 & (s << 24 | s >>> 8), 
            i[14 + (r + 64 >>> 9 << 4)] = 16711935 & (n << 8 | n >>> 24) | 4278255360 & (n << 24 | n >>> 8), 
            e.sigBytes = 4 * (i.length + 1), this._process(), i = (e = this._hash).words, n = 0; 4 > n; n++) r = i[n], 
            i[n] = 16711935 & (r << 8 | r >>> 24) | 4278255360 & (r << 24 | r >>> 8);
            return e;
        },
        clone: function() {
            var t = o.clone.call(this);
            return t._hash = this._hash.clone(), t;
        }
    }), s.MD5 = o._createHelper(h), s.HmacMD5 = o._createHmacHelper(h);
}(Math), function() {
    var t = g, e = (r = t.lib).WordArray, i = r.Hasher, n = [], r = t.algo.SHA1 = i.extend({
        _doReset: function() {
            this._hash = new e.init([ 1732584193, 4023233417, 2562383102, 271733878, 3285377520 ]);
        },
        _doProcessBlock: function(t, e) {
            for (var i = this._hash.words, r = i[0], s = i[1], a = i[2], o = i[3], h = i[4], u = 0; 80 > u; u++) {
                if (16 > u) n[u] = 0 | t[e + u]; else {
                    var c = n[u - 3] ^ n[u - 8] ^ n[u - 14] ^ n[u - 16];
                    n[u] = c << 1 | c >>> 31;
                }
                c = (r << 5 | r >>> 27) + h + n[u], c = 20 > u ? c + (1518500249 + (s & a | ~s & o)) : 40 > u ? c + (1859775393 + (s ^ a ^ o)) : 60 > u ? c + ((s & a | s & o | a & o) - 1894007588) : c + ((s ^ a ^ o) - 899497514), 
                h = o, o = a, a = s << 30 | s >>> 2, s = r, r = c;
            }
            i[0] = i[0] + r | 0, i[1] = i[1] + s | 0, i[2] = i[2] + a | 0, i[3] = i[3] + o | 0, 
            i[4] = i[4] + h | 0;
        },
        _doFinalize: function() {
            var t = this._data, e = t.words, i = 8 * this._nDataBytes, n = 8 * t.sigBytes;
            return e[n >>> 5] |= 128 << 24 - n % 32, e[14 + (n + 64 >>> 9 << 4)] = Math.floor(i / 4294967296), 
            e[15 + (n + 64 >>> 9 << 4)] = i, t.sigBytes = 4 * e.length, this._process(), this._hash;
        },
        clone: function() {
            var t = i.clone.call(this);
            return t._hash = this._hash.clone(), t;
        }
    });
    t.SHA1 = i._createHelper(r), t.HmacSHA1 = i._createHmacHelper(r);
}(), function(t) {
    for (var e = g, i = (r = e.lib).WordArray, n = r.Hasher, r = e.algo, s = [], a = [], o = function(t) {
        return 4294967296 * (t - (0 | t)) | 0;
    }, h = 2, u = 0; 64 > u; ) {
        var c;
        t: {
            c = h;
            for (var l = t.sqrt(c), f = 2; f <= l; f++) if (!(c % f)) {
                c = !1;
                break t;
            }
            c = !0;
        }
        c && (8 > u && (s[u] = o(t.pow(h, .5))), a[u] = o(t.pow(h, 1 / 3)), u++), h++;
    }
    var d = [];
    r = r.SHA256 = n.extend({
        _doReset: function() {
            this._hash = new i.init(s.slice(0));
        },
        _doProcessBlock: function(t, e) {
            for (var i = this._hash.words, n = i[0], r = i[1], s = i[2], o = i[3], h = i[4], u = i[5], c = i[6], l = i[7], f = 0; 64 > f; f++) {
                if (16 > f) d[f] = 0 | t[e + f]; else {
                    var g = d[f - 15], p = d[f - 2];
                    d[f] = ((g << 25 | g >>> 7) ^ (g << 14 | g >>> 18) ^ g >>> 3) + d[f - 7] + ((p << 15 | p >>> 17) ^ (p << 13 | p >>> 19) ^ p >>> 10) + d[f - 16];
                }
                g = l + ((h << 26 | h >>> 6) ^ (h << 21 | h >>> 11) ^ (h << 7 | h >>> 25)) + (h & u ^ ~h & c) + a[f] + d[f], 
                p = ((n << 30 | n >>> 2) ^ (n << 19 | n >>> 13) ^ (n << 10 | n >>> 22)) + (n & r ^ n & s ^ r & s), 
                l = c, c = u, u = h, h = o + g | 0, o = s, s = r, r = n, n = g + p | 0;
            }
            i[0] = i[0] + n | 0, i[1] = i[1] + r | 0, i[2] = i[2] + s | 0, i[3] = i[3] + o | 0, 
            i[4] = i[4] + h | 0, i[5] = i[5] + u | 0, i[6] = i[6] + c | 0, i[7] = i[7] + l | 0;
        },
        _doFinalize: function() {
            var e = this._data, i = e.words, n = 8 * this._nDataBytes, r = 8 * e.sigBytes;
            return i[r >>> 5] |= 128 << 24 - r % 32, i[14 + (r + 64 >>> 9 << 4)] = t.floor(n / 4294967296), 
            i[15 + (r + 64 >>> 9 << 4)] = n, e.sigBytes = 4 * i.length, this._process(), this._hash;
        },
        clone: function() {
            var t = n.clone.call(this);
            return t._hash = this._hash.clone(), t;
        }
    });
    e.SHA256 = n._createHelper(r), e.HmacSHA256 = n._createHmacHelper(r);
}(Math), function() {
    var t = g, e = t.lib.WordArray, i = (n = t.algo).SHA256, n = n.SHA224 = i.extend({
        _doReset: function() {
            this._hash = new e.init([ 3238371032, 914150663, 812702999, 4144912697, 4290775857, 1750603025, 1694076839, 3204075428 ]);
        },
        _doFinalize: function() {
            var t = i._doFinalize.call(this);
            return t.sigBytes -= 4, t;
        }
    });
    t.SHA224 = i._createHelper(n), t.HmacSHA224 = i._createHmacHelper(n);
}(), function() {
    function t() {
        return n.create.apply(n, arguments);
    }
    for (var e = g, i = e.lib.Hasher, n = (s = e.x64).Word, r = s.WordArray, s = e.algo, a = [ t(1116352408, 3609767458), t(1899447441, 602891725), t(3049323471, 3964484399), t(3921009573, 2173295548), t(961987163, 4081628472), t(1508970993, 3053834265), t(2453635748, 2937671579), t(2870763221, 3664609560), t(3624381080, 2734883394), t(310598401, 1164996542), t(607225278, 1323610764), t(1426881987, 3590304994), t(1925078388, 4068182383), t(2162078206, 991336113), t(2614888103, 633803317), t(3248222580, 3479774868), t(3835390401, 2666613458), t(4022224774, 944711139), t(264347078, 2341262773), t(604807628, 2007800933), t(770255983, 1495990901), t(1249150122, 1856431235), t(1555081692, 3175218132), t(1996064986, 2198950837), t(2554220882, 3999719339), t(2821834349, 766784016), t(2952996808, 2566594879), t(3210313671, 3203337956), t(3336571891, 1034457026), t(3584528711, 2466948901), t(113926993, 3758326383), t(338241895, 168717936), t(666307205, 1188179964), t(773529912, 1546045734), t(1294757372, 1522805485), t(1396182291, 2643833823), t(1695183700, 2343527390), t(1986661051, 1014477480), t(2177026350, 1206759142), t(2456956037, 344077627), t(2730485921, 1290863460), t(2820302411, 3158454273), t(3259730800, 3505952657), t(3345764771, 106217008), t(3516065817, 3606008344), t(3600352804, 1432725776), t(4094571909, 1467031594), t(275423344, 851169720), t(430227734, 3100823752), t(506948616, 1363258195), t(659060556, 3750685593), t(883997877, 3785050280), t(958139571, 3318307427), t(1322822218, 3812723403), t(1537002063, 2003034995), t(1747873779, 3602036899), t(1955562222, 1575990012), t(2024104815, 1125592928), t(2227730452, 2716904306), t(2361852424, 442776044), t(2428436474, 593698344), t(2756734187, 3733110249), t(3204031479, 2999351573), t(3329325298, 3815920427), t(3391569614, 3928383900), t(3515267271, 566280711), t(3940187606, 3454069534), t(4118630271, 4000239992), t(116418474, 1914138554), t(174292421, 2731055270), t(289380356, 3203993006), t(460393269, 320620315), t(685471733, 587496836), t(852142971, 1086792851), t(1017036298, 365543100), t(1126000580, 2618297676), t(1288033470, 3409855158), t(1501505948, 4234509866), t(1607167915, 987167468), t(1816402316, 1246189591) ], o = [], h = 0; 80 > h; h++) o[h] = t();
    s = s.SHA512 = i.extend({
        _doReset: function() {
            this._hash = new r.init([ new n.init(1779033703, 4089235720), new n.init(3144134277, 2227873595), new n.init(1013904242, 4271175723), new n.init(2773480762, 1595750129), new n.init(1359893119, 2917565137), new n.init(2600822924, 725511199), new n.init(528734635, 4215389547), new n.init(1541459225, 327033209) ]);
        },
        _doProcessBlock: function(t, e) {
            for (var i = (l = this._hash.words)[0], n = l[1], r = l[2], s = l[3], h = l[4], u = l[5], c = l[6], l = l[7], f = i.high, d = i.low, g = n.high, p = n.low, y = r.high, v = r.low, S = s.high, m = s.low, A = h.high, x = h.low, b = u.high, F = u.low, E = c.high, w = c.low, P = l.high, H = l.low, C = f, O = d, D = g, T = p, j = y, I = v, R = S, B = m, N = A, V = x, K = b, _ = F, L = E, k = w, M = P, q = H, U = 0; 80 > U; U++) {
                var z = o[U];
                if (16 > U) var W = z.high = 0 | t[e + 2 * U], J = z.low = 0 | t[e + 2 * U + 1]; else {
                    W = ((J = (W = o[U - 15]).high) >>> 1 | (G = W.low) << 31) ^ (J >>> 8 | G << 24) ^ J >>> 7;
                    var G = (G >>> 1 | J << 31) ^ (G >>> 8 | J << 24) ^ (G >>> 7 | J << 25), X = ((J = (X = o[U - 2]).high) >>> 19 | (Y = X.low) << 13) ^ (J << 3 | Y >>> 29) ^ J >>> 6, Y = (Y >>> 19 | J << 13) ^ (Y << 3 | J >>> 29) ^ (Y >>> 6 | J << 26), $ = (J = o[U - 7]).high, Z = (Q = o[U - 16]).high, Q = Q.low;
                    W = (W = (W = W + $ + ((J = G + J.low) >>> 0 < G >>> 0 ? 1 : 0)) + X + ((J = J + Y) >>> 0 < Y >>> 0 ? 1 : 0)) + Z + ((J = J + Q) >>> 0 < Q >>> 0 ? 1 : 0);
                    z.high = W, z.low = J;
                }
                $ = N & K ^ ~N & L, Q = V & _ ^ ~V & k, z = C & D ^ C & j ^ D & j;
                var tt = O & T ^ O & I ^ T & I, et = (G = (C >>> 28 | O << 4) ^ (C << 30 | O >>> 2) ^ (C << 25 | O >>> 7), 
                X = (O >>> 28 | C << 4) ^ (O << 30 | C >>> 2) ^ (O << 25 | C >>> 7), (Y = a[U]).high), it = Y.low;
                Z = M + ((N >>> 14 | V << 18) ^ (N >>> 18 | V << 14) ^ (N << 23 | V >>> 9)) + ((Y = q + ((V >>> 14 | N << 18) ^ (V >>> 18 | N << 14) ^ (V << 23 | N >>> 9))) >>> 0 < q >>> 0 ? 1 : 0), 
                M = L, q = k, L = K, k = _, K = N, _ = V, N = R + (Z = (Z = (Z = Z + $ + ((Y = Y + Q) >>> 0 < Q >>> 0 ? 1 : 0)) + et + ((Y = Y + it) >>> 0 < it >>> 0 ? 1 : 0)) + W + ((Y = Y + J) >>> 0 < J >>> 0 ? 1 : 0)) + ((V = B + Y | 0) >>> 0 < B >>> 0 ? 1 : 0) | 0, 
                R = j, B = I, j = D, I = T, D = C, T = O, C = Z + (z = G + z + ((J = X + tt) >>> 0 < X >>> 0 ? 1 : 0)) + ((O = Y + J | 0) >>> 0 < Y >>> 0 ? 1 : 0) | 0;
            }
            d = i.low = d + O, i.high = f + C + (d >>> 0 < O >>> 0 ? 1 : 0), p = n.low = p + T, 
            n.high = g + D + (p >>> 0 < T >>> 0 ? 1 : 0), v = r.low = v + I, r.high = y + j + (v >>> 0 < I >>> 0 ? 1 : 0), 
            m = s.low = m + B, s.high = S + R + (m >>> 0 < B >>> 0 ? 1 : 0), x = h.low = x + V, 
            h.high = A + N + (x >>> 0 < V >>> 0 ? 1 : 0), F = u.low = F + _, u.high = b + K + (F >>> 0 < _ >>> 0 ? 1 : 0), 
            w = c.low = w + k, c.high = E + L + (w >>> 0 < k >>> 0 ? 1 : 0), H = l.low = H + q, 
            l.high = P + M + (H >>> 0 < q >>> 0 ? 1 : 0);
        },
        _doFinalize: function() {
            var t = this._data, e = t.words, i = 8 * this._nDataBytes, n = 8 * t.sigBytes;
            return e[n >>> 5] |= 128 << 24 - n % 32, e[30 + (n + 128 >>> 10 << 5)] = Math.floor(i / 4294967296), 
            e[31 + (n + 128 >>> 10 << 5)] = i, t.sigBytes = 4 * e.length, this._process(), this._hash.toX32();
        },
        clone: function() {
            var t = i.clone.call(this);
            return t._hash = this._hash.clone(), t;
        },
        blockSize: 32
    }), e.SHA512 = i._createHelper(s), e.HmacSHA512 = i._createHmacHelper(s);
}(), function() {
    var t = g, e = (r = t.x64).Word, i = r.WordArray, n = (r = t.algo).SHA512, r = r.SHA384 = n.extend({
        _doReset: function() {
            this._hash = new i.init([ new e.init(3418070365, 3238371032), new e.init(1654270250, 914150663), new e.init(2438529370, 812702999), new e.init(355462360, 4144912697), new e.init(1731405415, 4290775857), new e.init(2394180231, 1750603025), new e.init(3675008525, 1694076839), new e.init(1203062813, 3204075428) ]);
        },
        _doFinalize: function() {
            var t = n._doFinalize.call(this);
            return t.sigBytes -= 16, t;
        }
    });
    t.SHA384 = n._createHelper(r), t.HmacSHA384 = n._createHmacHelper(r);
}(), function() {
    var t = g, e = (n = t.lib).WordArray, i = n.Hasher, n = t.algo, r = e.create([ 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 7, 4, 13, 1, 10, 6, 15, 3, 12, 0, 9, 5, 2, 14, 11, 8, 3, 10, 14, 4, 9, 15, 8, 1, 2, 7, 0, 6, 13, 11, 5, 12, 1, 9, 11, 10, 0, 8, 12, 4, 13, 3, 7, 15, 14, 5, 6, 2, 4, 0, 5, 9, 7, 12, 2, 10, 14, 1, 3, 8, 11, 6, 15, 13 ]), s = e.create([ 5, 14, 7, 0, 9, 2, 11, 4, 13, 6, 15, 8, 1, 10, 3, 12, 6, 11, 3, 7, 0, 13, 5, 10, 14, 15, 8, 12, 4, 9, 1, 2, 15, 5, 1, 3, 7, 14, 6, 9, 11, 8, 12, 2, 10, 0, 4, 13, 8, 6, 4, 1, 3, 11, 15, 0, 5, 12, 2, 13, 9, 7, 10, 14, 12, 15, 10, 4, 1, 5, 8, 7, 6, 2, 13, 14, 0, 3, 9, 11 ]), a = e.create([ 11, 14, 15, 12, 5, 8, 7, 9, 11, 13, 14, 15, 6, 7, 9, 8, 7, 6, 8, 13, 11, 9, 7, 15, 7, 12, 15, 9, 11, 7, 13, 12, 11, 13, 6, 7, 14, 9, 13, 15, 14, 8, 13, 6, 5, 12, 7, 5, 11, 12, 14, 15, 14, 15, 9, 8, 9, 14, 5, 6, 8, 6, 5, 12, 9, 15, 5, 11, 6, 8, 13, 12, 5, 12, 13, 14, 11, 8, 5, 6 ]), o = e.create([ 8, 9, 9, 11, 13, 15, 15, 5, 7, 7, 8, 11, 14, 14, 12, 6, 9, 13, 15, 7, 12, 8, 9, 11, 7, 7, 12, 7, 6, 15, 13, 11, 9, 7, 15, 11, 8, 6, 6, 14, 12, 13, 5, 14, 13, 13, 7, 5, 15, 5, 8, 11, 14, 14, 6, 14, 6, 9, 12, 9, 12, 5, 15, 8, 8, 5, 12, 9, 12, 5, 14, 6, 8, 13, 6, 5, 15, 13, 11, 11 ]), h = e.create([ 0, 1518500249, 1859775393, 2400959708, 2840853838 ]), u = e.create([ 1352829926, 1548603684, 1836072691, 2053994217, 0 ]);
    n = n.RIPEMD160 = i.extend({
        _doReset: function() {
            this._hash = e.create([ 1732584193, 4023233417, 2562383102, 271733878, 3285377520 ]);
        },
        _doProcessBlock: function(t, e) {
            for (var i = 0; 16 > i; i++) {
                var n = t[A = e + i];
                t[A] = 16711935 & (n << 8 | n >>> 24) | 4278255360 & (n << 24 | n >>> 8);
            }
            var c, l, f, d, g, p, y, v, S, m, A = this._hash.words, x = (n = h.words, u.words), b = r.words, F = s.words, E = a.words, w = o.words;
            p = c = A[0], y = l = A[1], v = f = A[2], S = d = A[3], m = g = A[4];
            var P;
            for (i = 0; 80 > i; i += 1) P = c + t[e + b[i]] | 0, P = 16 > i ? P + ((l ^ f ^ d) + n[0]) : 32 > i ? P + ((l & f | ~l & d) + n[1]) : 48 > i ? P + (((l | ~f) ^ d) + n[2]) : 64 > i ? P + ((l & d | f & ~d) + n[3]) : P + ((l ^ (f | ~d)) + n[4]), 
            P = (P = (P |= 0) << E[i] | P >>> 32 - E[i]) + g | 0, c = g, g = d, d = f << 10 | f >>> 22, 
            f = l, l = P, P = p + t[e + F[i]] | 0, P = 16 > i ? P + ((y ^ (v | ~S)) + x[0]) : 32 > i ? P + ((y & S | v & ~S) + x[1]) : 48 > i ? P + (((y | ~v) ^ S) + x[2]) : 64 > i ? P + ((y & v | ~y & S) + x[3]) : P + ((y ^ v ^ S) + x[4]), 
            P = (P = (P |= 0) << w[i] | P >>> 32 - w[i]) + m | 0, p = m, m = S, S = v << 10 | v >>> 22, 
            v = y, y = P;
            P = A[1] + f + S | 0, A[1] = A[2] + d + m | 0, A[2] = A[3] + g + p | 0, A[3] = A[4] + c + y | 0, 
            A[4] = A[0] + l + v | 0, A[0] = P;
        },
        _doFinalize: function() {
            var t = this._data, e = t.words, i = 8 * this._nDataBytes, n = 8 * t.sigBytes;
            for (e[n >>> 5] |= 128 << 24 - n % 32, e[14 + (n + 64 >>> 9 << 4)] = 16711935 & (i << 8 | i >>> 24) | 4278255360 & (i << 24 | i >>> 8), 
            t.sigBytes = 4 * (e.length + 1), this._process(), e = (t = this._hash).words, i = 0; 5 > i; i++) n = e[i], 
            e[i] = 16711935 & (n << 8 | n >>> 24) | 4278255360 & (n << 24 | n >>> 8);
            return t;
        },
        clone: function() {
            var t = i.clone.call(this);
            return t._hash = this._hash.clone(), t;
        }
    });
    t.RIPEMD160 = i._createHelper(n), t.HmacRIPEMD160 = i._createHmacHelper(n);
}(Math), d = (f = g).enc.Utf8, f.algo.HMAC = f.lib.Base.extend({
    init: function(t, e) {
        t = this._hasher = new t.init(), "string" == typeof e && (e = d.parse(e));
        var i = t.blockSize, n = 4 * i;
        e.sigBytes > n && (e = t.finalize(e)), e.clamp();
        for (var r = this._oKey = e.clone(), s = this._iKey = e.clone(), a = r.words, o = s.words, h = 0; h < i; h++) a[h] ^= 1549556828, 
        o[h] ^= 909522486;
        r.sigBytes = s.sigBytes = n, this.reset();
    },
    reset: function() {
        var t = this._hasher;
        t.reset(), t.update(this._iKey);
    },
    update: function(t) {
        return this._hasher.update(t), this;
    },
    finalize: function(t) {
        var e = this._hasher;
        return t = e.finalize(t), e.reset(), e.finalize(this._oKey.clone().concat(t));
    }
}), function() {
    var t, e = g, i = (t = e.lib).Base, n = t.WordArray, r = (t = e.algo).HMAC, s = t.PBKDF2 = i.extend({
        cfg: i.extend({
            keySize: 4,
            hasher: t.SHA1,
            iterations: 1
        }),
        init: function(t) {
            this.cfg = this.cfg.extend(t);
        },
        compute: function(t, e) {
            var i = this.cfg, s = r.create(i.hasher, t), a = n.create(), o = n.create([ 1 ]), h = a.words, u = o.words, c = i.keySize;
            for (i = i.iterations; h.length < c; ) {
                var l = s.update(e).finalize(o);
                s.reset();
                for (var f = l.words, d = f.length, g = l, p = 1; p < i; p++) {
                    g = s.finalize(g), s.reset();
                    for (var y = g.words, v = 0; v < d; v++) f[v] ^= y[v];
                }
                a.concat(l), u[0]++;
            }
            return a.sigBytes = 4 * c, a;
        }
    });
    e.PBKDF2 = function(t, e, i) {
        return s.create(i).compute(t, e);
    };
}();

/*! (c) Tom Wu | http://www-cs-students.stanford.edu/~tjw/jsbn/
 */ var p, y = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";

function v(t) {
    var e, i, n = "";
    for (e = 0; e + 3 <= t.length; e += 3) i = parseInt(t.substring(e, e + 3), 16), 
    n += y.charAt(i >> 6) + y.charAt(63 & i);
    for (e + 1 == t.length ? (i = parseInt(t.substring(e, e + 1), 16), n += y.charAt(i << 2)) : e + 2 == t.length && (i = parseInt(t.substring(e, e + 2), 16), 
    n += y.charAt(i >> 2) + y.charAt((3 & i) << 4)), "="; (3 & n.length) > 0; ) n += "=";
    return n;
}

function S(t) {
    var e, i, n, r = "", s = 0;
    for (e = 0; e < t.length && "=" != t.charAt(e); ++e) (n = y.indexOf(t.charAt(e))) < 0 || (0 == s ? (r += E(n >> 2), 
    i = 3 & n, s = 1) : 1 == s ? (r += E(i << 2 | n >> 4), i = 15 & n, s = 2) : 2 == s ? (r += E(i), 
    r += E(n >> 2), i = 3 & n, s = 3) : (r += E(i << 2 | n >> 4), r += E(15 & n), s = 0));
    return 1 == s && (r += E(i << 2)), r;
}

function m(t, e, i) {
    null != t && ("number" == typeof t ? this.fromNumber(t, e, i) : null == e && "string" != typeof t ? this.fromString(t, 256) : this.fromString(t, e));
}

function A() {
    return new m(null);
}

"Microsoft Internet Explorer" == o.appName ? (m.prototype.am = function(t, e, i, n, r, s) {
    for (var a = 32767 & e, o = e >> 15; --s >= 0; ) {
        var h = 32767 & this[t], u = this[t++] >> 15, c = o * h + u * a;
        r = ((h = a * h + ((32767 & c) << 15) + i[n] + (1073741823 & r)) >>> 30) + (c >>> 15) + o * u + (r >>> 30), 
        i[n++] = 1073741823 & h;
    }
    return r;
}, p = 30) : "Netscape" != o.appName ? (m.prototype.am = function(t, e, i, n, r, s) {
    for (;--s >= 0; ) {
        var a = e * this[t++] + i[n] + r;
        r = Math.floor(a / 67108864), i[n++] = 67108863 & a;
    }
    return r;
}, p = 26) : (m.prototype.am = function(t, e, i, n, r, s) {
    for (var a = 16383 & e, o = e >> 14; --s >= 0; ) {
        var h = 16383 & this[t], u = this[t++] >> 14, c = o * h + u * a;
        r = ((h = a * h + ((16383 & c) << 14) + i[n] + r) >> 28) + (c >> 14) + o * u, i[n++] = 268435455 & h;
    }
    return r;
}, p = 28), m.prototype.DB = p, m.prototype.DM = (1 << p) - 1, m.prototype.DV = 1 << p;

m.prototype.FV = Math.pow(2, 52), m.prototype.F1 = 52 - p, m.prototype.F2 = 2 * p - 52;

var x, b, F = new Array();

for (x = "0".charCodeAt(0), b = 0; b <= 9; ++b) F[x++] = b;

for (x = "a".charCodeAt(0), b = 10; b < 36; ++b) F[x++] = b;

for (x = "A".charCodeAt(0), b = 10; b < 36; ++b) F[x++] = b;

function E(t) {
    return "0123456789abcdefghijklmnopqrstuvwxyz".charAt(t);
}

function w(t, e) {
    var i = F[t.charCodeAt(e)];
    return null == i ? -1 : i;
}

function P(t) {
    var e = A();
    return e.fromInt(t), e;
}

function H(t) {
    var e, i = 1;
    return 0 != (e = t >>> 16) && (t = e, i += 16), 0 != (e = t >> 8) && (t = e, i += 8), 
    0 != (e = t >> 4) && (t = e, i += 4), 0 != (e = t >> 2) && (t = e, i += 2), 0 != (e = t >> 1) && (t = e, 
    i += 1), i;
}

function C(t) {
    this.m = t;
}

function O(t) {
    this.m = t, this.mp = t.invDigit(), this.mpl = 32767 & this.mp, this.mph = this.mp >> 15, 
    this.um = (1 << t.DB - 15) - 1, this.mt2 = 2 * t.t;
}

function D(t, e) {
    return t & e;
}

function T(t, e) {
    return t | e;
}

function j(t, e) {
    return t ^ e;
}

function I(t, e) {
    return t & ~e;
}

function R(t) {
    if (0 == t) return -1;
    var e = 0;
    return 0 == (65535 & t) && (t >>= 16, e += 16), 0 == (255 & t) && (t >>= 8, e += 8), 
    0 == (15 & t) && (t >>= 4, e += 4), 0 == (3 & t) && (t >>= 2, e += 2), 0 == (1 & t) && ++e, 
    e;
}

function B(t) {
    for (var e = 0; 0 != t; ) t &= t - 1, ++e;
    return e;
}

function N() {}

function V(t) {
    return t;
}

function K(t) {
    this.r2 = A(), this.q3 = A(), m.ONE.dlShiftTo(2 * t.t, this.r2), this.mu = this.r2.divide(t), 
    this.m = t;
}

C.prototype.convert = function(t) {
    return t.s < 0 || t.compareTo(this.m) >= 0 ? t.mod(this.m) : t;
}, C.prototype.revert = function(t) {
    return t;
}, C.prototype.reduce = function(t) {
    t.divRemTo(this.m, null, t);
}, C.prototype.mulTo = function(t, e, i) {
    t.multiplyTo(e, i), this.reduce(i);
}, C.prototype.sqrTo = function(t, e) {
    t.squareTo(e), this.reduce(e);
}, O.prototype.convert = function(t) {
    var e = A();
    return t.abs().dlShiftTo(this.m.t, e), e.divRemTo(this.m, null, e), t.s < 0 && e.compareTo(m.ZERO) > 0 && this.m.subTo(e, e), 
    e;
}, O.prototype.revert = function(t) {
    var e = A();
    return t.copyTo(e), this.reduce(e), e;
}, O.prototype.reduce = function(t) {
    for (;t.t <= this.mt2; ) t[t.t++] = 0;
    for (var e = 0; e < this.m.t; ++e) {
        var i = 32767 & t[e], n = i * this.mpl + ((i * this.mph + (t[e] >> 15) * this.mpl & this.um) << 15) & t.DM;
        for (t[i = e + this.m.t] += this.m.am(0, n, t, e, 0, this.m.t); t[i] >= t.DV; ) t[i] -= t.DV, 
        t[++i]++;
    }
    t.clamp(), t.drShiftTo(this.m.t, t), t.compareTo(this.m) >= 0 && t.subTo(this.m, t);
}, O.prototype.mulTo = function(t, e, i) {
    t.multiplyTo(e, i), this.reduce(i);
}, O.prototype.sqrTo = function(t, e) {
    t.squareTo(e), this.reduce(e);
}, m.prototype.copyTo = function(t) {
    for (var e = this.t - 1; e >= 0; --e) t[e] = this[e];
    t.t = this.t, t.s = this.s;
}, m.prototype.fromInt = function(t) {
    this.t = 1, this.s = t < 0 ? -1 : 0, t > 0 ? this[0] = t : t < -1 ? this[0] = t + this.DV : this.t = 0;
}, m.prototype.fromString = function(t, e) {
    var i;
    if (16 == e) i = 4; else if (8 == e) i = 3; else if (256 == e) i = 8; else if (2 == e) i = 1; else if (32 == e) i = 5; else {
        if (4 != e) return void this.fromRadix(t, e);
        i = 2;
    }
    this.t = 0, this.s = 0;
    for (var n = t.length, r = !1, s = 0; --n >= 0; ) {
        var a = 8 == i ? 255 & t[n] : w(t, n);
        a < 0 ? "-" == t.charAt(n) && (r = !0) : (r = !1, 0 == s ? this[this.t++] = a : s + i > this.DB ? (this[this.t - 1] |= (a & (1 << this.DB - s) - 1) << s, 
        this[this.t++] = a >> this.DB - s) : this[this.t - 1] |= a << s, (s += i) >= this.DB && (s -= this.DB));
    }
    8 == i && 0 != (128 & t[0]) && (this.s = -1, s > 0 && (this[this.t - 1] |= (1 << this.DB - s) - 1 << s)), 
    this.clamp(), r && m.ZERO.subTo(this, this);
}, m.prototype.clamp = function() {
    for (var t = this.s & this.DM; this.t > 0 && this[this.t - 1] == t; ) --this.t;
}, m.prototype.dlShiftTo = function(t, e) {
    var i;
    for (i = this.t - 1; i >= 0; --i) e[i + t] = this[i];
    for (i = t - 1; i >= 0; --i) e[i] = 0;
    e.t = this.t + t, e.s = this.s;
}, m.prototype.drShiftTo = function(t, e) {
    for (var i = t; i < this.t; ++i) e[i - t] = this[i];
    e.t = Math.max(this.t - t, 0), e.s = this.s;
}, m.prototype.lShiftTo = function(t, e) {
    var i, n = t % this.DB, r = this.DB - n, s = (1 << r) - 1, a = Math.floor(t / this.DB), o = this.s << n & this.DM;
    for (i = this.t - 1; i >= 0; --i) e[i + a + 1] = this[i] >> r | o, o = (this[i] & s) << n;
    for (i = a - 1; i >= 0; --i) e[i] = 0;
    e[a] = o, e.t = this.t + a + 1, e.s = this.s, e.clamp();
}, m.prototype.rShiftTo = function(t, e) {
    e.s = this.s;
    var i = Math.floor(t / this.DB);
    if (i >= this.t) e.t = 0; else {
        var n = t % this.DB, r = this.DB - n, s = (1 << n) - 1;
        e[0] = this[i] >> n;
        for (var a = i + 1; a < this.t; ++a) e[a - i - 1] |= (this[a] & s) << r, e[a - i] = this[a] >> n;
        n > 0 && (e[this.t - i - 1] |= (this.s & s) << r), e.t = this.t - i, e.clamp();
    }
}, m.prototype.subTo = function(t, e) {
    for (var i = 0, n = 0, r = Math.min(t.t, this.t); i < r; ) n += this[i] - t[i], 
    e[i++] = n & this.DM, n >>= this.DB;
    if (t.t < this.t) {
        for (n -= t.s; i < this.t; ) n += this[i], e[i++] = n & this.DM, n >>= this.DB;
        n += this.s;
    } else {
        for (n += this.s; i < t.t; ) n -= t[i], e[i++] = n & this.DM, n >>= this.DB;
        n -= t.s;
    }
    e.s = n < 0 ? -1 : 0, n < -1 ? e[i++] = this.DV + n : n > 0 && (e[i++] = n), e.t = i, 
    e.clamp();
}, m.prototype.multiplyTo = function(t, e) {
    var i = this.abs(), n = t.abs(), r = i.t;
    for (e.t = r + n.t; --r >= 0; ) e[r] = 0;
    for (r = 0; r < n.t; ++r) e[r + i.t] = i.am(0, n[r], e, r, 0, i.t);
    e.s = 0, e.clamp(), this.s != t.s && m.ZERO.subTo(e, e);
}, m.prototype.squareTo = function(t) {
    for (var e = this.abs(), i = t.t = 2 * e.t; --i >= 0; ) t[i] = 0;
    for (i = 0; i < e.t - 1; ++i) {
        var n = e.am(i, e[i], t, 2 * i, 0, 1);
        (t[i + e.t] += e.am(i + 1, 2 * e[i], t, 2 * i + 1, n, e.t - i - 1)) >= e.DV && (t[i + e.t] -= e.DV, 
        t[i + e.t + 1] = 1);
    }
    t.t > 0 && (t[t.t - 1] += e.am(i, e[i], t, 2 * i, 0, 1)), t.s = 0, t.clamp();
}, m.prototype.divRemTo = function(t, e, i) {
    var n = t.abs();
    if (!(n.t <= 0)) {
        var r = this.abs();
        if (r.t < n.t) return null != e && e.fromInt(0), void (null != i && this.copyTo(i));
        null == i && (i = A());
        var s = A(), a = this.s, o = t.s, h = this.DB - H(n[n.t - 1]);
        h > 0 ? (n.lShiftTo(h, s), r.lShiftTo(h, i)) : (n.copyTo(s), r.copyTo(i));
        var u = s.t, c = s[u - 1];
        if (0 != c) {
            var l = c * (1 << this.F1) + (u > 1 ? s[u - 2] >> this.F2 : 0), f = this.FV / l, d = (1 << this.F1) / l, g = 1 << this.F2, p = i.t, y = p - u, v = null == e ? A() : e;
            for (s.dlShiftTo(y, v), i.compareTo(v) >= 0 && (i[i.t++] = 1, i.subTo(v, i)), m.ONE.dlShiftTo(u, v), 
            v.subTo(s, s); s.t < u; ) s[s.t++] = 0;
            for (;--y >= 0; ) {
                var S = i[--p] == c ? this.DM : Math.floor(i[p] * f + (i[p - 1] + g) * d);
                if ((i[p] += s.am(0, S, i, y, 0, u)) < S) for (s.dlShiftTo(y, v), i.subTo(v, i); i[p] < --S; ) i.subTo(v, i);
            }
            null != e && (i.drShiftTo(u, e), a != o && m.ZERO.subTo(e, e)), i.t = u, i.clamp(), 
            h > 0 && i.rShiftTo(h, i), a < 0 && m.ZERO.subTo(i, i);
        }
    }
}, m.prototype.invDigit = function() {
    if (this.t < 1) return 0;
    var t = this[0];
    if (0 == (1 & t)) return 0;
    var e = 3 & t;
    return (e = (e = (e = (e = e * (2 - (15 & t) * e) & 15) * (2 - (255 & t) * e) & 255) * (2 - ((65535 & t) * e & 65535)) & 65535) * (2 - t * e % this.DV) % this.DV) > 0 ? this.DV - e : -e;
}, m.prototype.isEven = function() {
    return 0 == (this.t > 0 ? 1 & this[0] : this.s);
}, m.prototype.exp = function(t, e) {
    if (t > 4294967295 || t < 1) return m.ONE;
    var i = A(), n = A(), r = e.convert(this), s = H(t) - 1;
    for (r.copyTo(i); --s >= 0; ) if (e.sqrTo(i, n), (t & 1 << s) > 0) e.mulTo(n, r, i); else {
        var a = i;
        i = n, n = a;
    }
    return e.revert(i);
}, m.prototype.toString = function(t) {
    if (this.s < 0) return "-" + this.negate().toString(t);
    var e;
    if (16 == t) e = 4; else if (8 == t) e = 3; else if (2 == t) e = 1; else if (32 == t) e = 5; else {
        if (4 != t) return this.toRadix(t);
        e = 2;
    }
    var i, n = (1 << e) - 1, r = !1, s = "", a = this.t, o = this.DB - a * this.DB % e;
    if (a-- > 0) for (o < this.DB && (i = this[a] >> o) > 0 && (r = !0, s = E(i)); a >= 0; ) o < e ? (i = (this[a] & (1 << o) - 1) << e - o, 
    i |= this[--a] >> (o += this.DB - e)) : (i = this[a] >> (o -= e) & n, o <= 0 && (o += this.DB, 
    --a)), i > 0 && (r = !0), r && (s += E(i));
    return r ? s : "0";
}, m.prototype.negate = function() {
    var t = A();
    return m.ZERO.subTo(this, t), t;
}, m.prototype.abs = function() {
    return this.s < 0 ? this.negate() : this;
}, m.prototype.compareTo = function(t) {
    var e = this.s - t.s;
    if (0 != e) return e;
    var i = this.t;
    if (0 != (e = i - t.t)) return this.s < 0 ? -e : e;
    for (;--i >= 0; ) if (0 != (e = this[i] - t[i])) return e;
    return 0;
}, m.prototype.bitLength = function() {
    return this.t <= 0 ? 0 : this.DB * (this.t - 1) + H(this[this.t - 1] ^ this.s & this.DM);
}, m.prototype.mod = function(t) {
    var e = A();
    return this.abs().divRemTo(t, null, e), this.s < 0 && e.compareTo(m.ZERO) > 0 && t.subTo(e, e), 
    e;
}, m.prototype.modPowInt = function(t, e) {
    var i;
    return i = t < 256 || e.isEven() ? new C(e) : new O(e), this.exp(t, i);
}, m.ZERO = P(0), m.ONE = P(1), N.prototype.convert = V, N.prototype.revert = V, 
N.prototype.mulTo = function(t, e, i) {
    t.multiplyTo(e, i);
}, N.prototype.sqrTo = function(t, e) {
    t.squareTo(e);
}, K.prototype.convert = function(t) {
    if (t.s < 0 || t.t > 2 * this.m.t) return t.mod(this.m);
    if (t.compareTo(this.m) < 0) return t;
    var e = A();
    return t.copyTo(e), this.reduce(e), e;
}, K.prototype.revert = function(t) {
    return t;
}, K.prototype.reduce = function(t) {
    for (t.drShiftTo(this.m.t - 1, this.r2), t.t > this.m.t + 1 && (t.t = this.m.t + 1, 
    t.clamp()), this.mu.multiplyUpperTo(this.r2, this.m.t + 1, this.q3), this.m.multiplyLowerTo(this.q3, this.m.t + 1, this.r2); t.compareTo(this.r2) < 0; ) t.dAddOffset(1, this.m.t + 1);
    for (t.subTo(this.r2, t); t.compareTo(this.m) >= 0; ) t.subTo(this.m, t);
}, K.prototype.mulTo = function(t, e, i) {
    t.multiplyTo(e, i), this.reduce(i);
}, K.prototype.sqrTo = function(t, e) {
    t.squareTo(e), this.reduce(e);
};

var _ = [ 2, 3, 5, 7, 11, 13, 17, 19, 23, 29, 31, 37, 41, 43, 47, 53, 59, 61, 67, 71, 73, 79, 83, 89, 97, 101, 103, 107, 109, 113, 127, 131, 137, 139, 149, 151, 157, 163, 167, 173, 179, 181, 191, 193, 197, 199, 211, 223, 227, 229, 233, 239, 241, 251, 257, 263, 269, 271, 277, 281, 283, 293, 307, 311, 313, 317, 331, 337, 347, 349, 353, 359, 367, 373, 379, 383, 389, 397, 401, 409, 419, 421, 431, 433, 439, 443, 449, 457, 461, 463, 467, 479, 487, 491, 499, 503, 509, 521, 523, 541, 547, 557, 563, 569, 571, 577, 587, 593, 599, 601, 607, 613, 617, 619, 631, 641, 643, 647, 653, 659, 661, 673, 677, 683, 691, 701, 709, 719, 727, 733, 739, 743, 751, 757, 761, 769, 773, 787, 797, 809, 811, 821, 823, 827, 829, 839, 853, 857, 859, 863, 877, 881, 883, 887, 907, 911, 919, 929, 937, 941, 947, 953, 967, 971, 977, 983, 991, 997 ], L = (1 << 26) / _[_.length - 1];

/*! (c) Tom Wu | http://www-cs-students.stanford.edu/~tjw/jsbn/
 */ function k() {
    this.i = 0, this.j = 0, this.S = new Array();
}

m.prototype.chunkSize = function(t) {
    return Math.floor(Math.LN2 * this.DB / Math.log(t));
}, m.prototype.toRadix = function(t) {
    if (null == t && (t = 10), 0 == this.signum() || t < 2 || t > 36) return "0";
    var e = this.chunkSize(t), i = Math.pow(t, e), n = P(i), r = A(), s = A(), a = "";
    for (this.divRemTo(n, r, s); r.signum() > 0; ) a = (i + s.intValue()).toString(t).substr(1) + a, 
    r.divRemTo(n, r, s);
    return s.intValue().toString(t) + a;
}, m.prototype.fromRadix = function(t, e) {
    this.fromInt(0), null == e && (e = 10);
    for (var i = this.chunkSize(e), n = Math.pow(e, i), r = !1, s = 0, a = 0, o = 0; o < t.length; ++o) {
        var h = w(t, o);
        h < 0 ? "-" == t.charAt(o) && 0 == this.signum() && (r = !0) : (a = e * a + h, ++s >= i && (this.dMultiply(n), 
        this.dAddOffset(a, 0), s = 0, a = 0));
    }
    s > 0 && (this.dMultiply(Math.pow(e, s)), this.dAddOffset(a, 0)), r && m.ZERO.subTo(this, this);
}, m.prototype.fromNumber = function(t, e, i) {
    if ("number" == typeof e) if (t < 2) this.fromInt(1); else for (this.fromNumber(t, i), 
    this.testBit(t - 1) || this.bitwiseTo(m.ONE.shiftLeft(t - 1), T, this), this.isEven() && this.dAddOffset(1, 0); !this.isProbablePrime(e); ) this.dAddOffset(2, 0), 
    this.bitLength() > t && this.subTo(m.ONE.shiftLeft(t - 1), this); else {
        var n = new Array(), r = 7 & t;
        n.length = 1 + (t >> 3), e.nextBytes(n), r > 0 ? n[0] &= (1 << r) - 1 : n[0] = 0, 
        this.fromString(n, 256);
    }
}, m.prototype.bitwiseTo = function(t, e, i) {
    var n, r, s = Math.min(t.t, this.t);
    for (n = 0; n < s; ++n) i[n] = e(this[n], t[n]);
    if (t.t < this.t) {
        for (r = t.s & this.DM, n = s; n < this.t; ++n) i[n] = e(this[n], r);
        i.t = this.t;
    } else {
        for (r = this.s & this.DM, n = s; n < t.t; ++n) i[n] = e(r, t[n]);
        i.t = t.t;
    }
    i.s = e(this.s, t.s), i.clamp();
}, m.prototype.changeBit = function(t, e) {
    var i = m.ONE.shiftLeft(t);
    return this.bitwiseTo(i, e, i), i;
}, m.prototype.addTo = function(t, e) {
    for (var i = 0, n = 0, r = Math.min(t.t, this.t); i < r; ) n += this[i] + t[i], 
    e[i++] = n & this.DM, n >>= this.DB;
    if (t.t < this.t) {
        for (n += t.s; i < this.t; ) n += this[i], e[i++] = n & this.DM, n >>= this.DB;
        n += this.s;
    } else {
        for (n += this.s; i < t.t; ) n += t[i], e[i++] = n & this.DM, n >>= this.DB;
        n += t.s;
    }
    e.s = n < 0 ? -1 : 0, n > 0 ? e[i++] = n : n < -1 && (e[i++] = this.DV + n), e.t = i, 
    e.clamp();
}, m.prototype.dMultiply = function(t) {
    this[this.t] = this.am(0, t - 1, this, 0, 0, this.t), ++this.t, this.clamp();
}, m.prototype.dAddOffset = function(t, e) {
    if (0 != t) {
        for (;this.t <= e; ) this[this.t++] = 0;
        for (this[e] += t; this[e] >= this.DV; ) this[e] -= this.DV, ++e >= this.t && (this[this.t++] = 0), 
        ++this[e];
    }
}, m.prototype.multiplyLowerTo = function(t, e, i) {
    var n, r = Math.min(this.t + t.t, e);
    for (i.s = 0, i.t = r; r > 0; ) i[--r] = 0;
    for (n = i.t - this.t; r < n; ++r) i[r + this.t] = this.am(0, t[r], i, r, 0, this.t);
    for (n = Math.min(t.t, e); r < n; ++r) this.am(0, t[r], i, r, 0, e - r);
    i.clamp();
}, m.prototype.multiplyUpperTo = function(t, e, i) {
    --e;
    var n = i.t = this.t + t.t - e;
    for (i.s = 0; --n >= 0; ) i[n] = 0;
    for (n = Math.max(e - this.t, 0); n < t.t; ++n) i[this.t + n - e] = this.am(e - n, t[n], i, 0, 0, this.t + n - e);
    i.clamp(), i.drShiftTo(1, i);
}, m.prototype.modInt = function(t) {
    if (t <= 0) return 0;
    var e = this.DV % t, i = this.s < 0 ? t - 1 : 0;
    if (this.t > 0) if (0 == e) i = this[0] % t; else for (var n = this.t - 1; n >= 0; --n) i = (e * i + this[n]) % t;
    return i;
}, m.prototype.millerRabin = function(t) {
    var e = this.subtract(m.ONE), i = e.getLowestSetBit();
    if (i <= 0) return !1;
    var n = e.shiftRight(i);
    (t = t + 1 >> 1) > _.length && (t = _.length);
    for (var r = A(), s = 0; s < t; ++s) {
        r.fromInt(_[Math.floor(Math.random() * _.length)]);
        var a = r.modPow(n, this);
        if (0 != a.compareTo(m.ONE) && 0 != a.compareTo(e)) {
            for (var o = 1; o++ < i && 0 != a.compareTo(e); ) if (0 == (a = a.modPowInt(2, this)).compareTo(m.ONE)) return !1;
            if (0 != a.compareTo(e)) return !1;
        }
    }
    return !0;
}, m.prototype.clone = 
/*! (c) Tom Wu | http://www-cs-students.stanford.edu/~tjw/jsbn/
 */
function() {
    var t = A();
    return this.copyTo(t), t;
}, m.prototype.intValue = function() {
    if (this.s < 0) {
        if (1 == this.t) return this[0] - this.DV;
        if (0 == this.t) return -1;
    } else {
        if (1 == this.t) return this[0];
        if (0 == this.t) return 0;
    }
    return (this[1] & (1 << 32 - this.DB) - 1) << this.DB | this[0];
}, m.prototype.byteValue = function() {
    return 0 == this.t ? this.s : this[0] << 24 >> 24;
}, m.prototype.shortValue = function() {
    return 0 == this.t ? this.s : this[0] << 16 >> 16;
}, m.prototype.signum = function() {
    return this.s < 0 ? -1 : this.t <= 0 || 1 == this.t && this[0] <= 0 ? 0 : 1;
}, m.prototype.toByteArray = function() {
    var t = this.t, e = new Array();
    e[0] = this.s;
    var i, n = this.DB - t * this.DB % 8, r = 0;
    if (t-- > 0) for (n < this.DB && (i = this[t] >> n) != (this.s & this.DM) >> n && (e[r++] = i | this.s << this.DB - n); t >= 0; ) n < 8 ? (i = (this[t] & (1 << n) - 1) << 8 - n, 
    i |= this[--t] >> (n += this.DB - 8)) : (i = this[t] >> (n -= 8) & 255, n <= 0 && (n += this.DB, 
    --t)), 0 != (128 & i) && (i |= -256), 0 == r && (128 & this.s) != (128 & i) && ++r, 
    (r > 0 || i != this.s) && (e[r++] = i);
    return e;
}, m.prototype.equals = function(t) {
    return 0 == this.compareTo(t);
}, m.prototype.min = function(t) {
    return this.compareTo(t) < 0 ? this : t;
}, m.prototype.max = function(t) {
    return this.compareTo(t) > 0 ? this : t;
}, m.prototype.and = function(t) {
    var e = A();
    return this.bitwiseTo(t, D, e), e;
}, m.prototype.or = function(t) {
    var e = A();
    return this.bitwiseTo(t, T, e), e;
}, m.prototype.xor = function(t) {
    var e = A();
    return this.bitwiseTo(t, j, e), e;
}, m.prototype.andNot = function(t) {
    var e = A();
    return this.bitwiseTo(t, I, e), e;
}, m.prototype.not = function() {
    for (var t = A(), e = 0; e < this.t; ++e) t[e] = this.DM & ~this[e];
    return t.t = this.t, t.s = ~this.s, t;
}, m.prototype.shiftLeft = function(t) {
    var e = A();
    return t < 0 ? this.rShiftTo(-t, e) : this.lShiftTo(t, e), e;
}, m.prototype.shiftRight = function(t) {
    var e = A();
    return t < 0 ? this.lShiftTo(-t, e) : this.rShiftTo(t, e), e;
}, m.prototype.getLowestSetBit = function() {
    for (var t = 0; t < this.t; ++t) if (0 != this[t]) return t * this.DB + R(this[t]);
    return this.s < 0 ? this.t * this.DB : -1;
}, m.prototype.bitCount = function() {
    for (var t = 0, e = this.s & this.DM, i = 0; i < this.t; ++i) t += B(this[i] ^ e);
    return t;
}, m.prototype.testBit = function(t) {
    var e = Math.floor(t / this.DB);
    return e >= this.t ? 0 != this.s : 0 != (this[e] & 1 << t % this.DB);
}, m.prototype.setBit = function(t) {
    return this.changeBit(t, T);
}, m.prototype.clearBit = function(t) {
    return this.changeBit(t, I);
}, m.prototype.flipBit = function(t) {
    return this.changeBit(t, j);
}, m.prototype.add = function(t) {
    var e = A();
    return this.addTo(t, e), e;
}, m.prototype.subtract = function(t) {
    var e = A();
    return this.subTo(t, e), e;
}, m.prototype.multiply = function(t) {
    var e = A();
    return this.multiplyTo(t, e), e;
}, m.prototype.divide = function(t) {
    var e = A();
    return this.divRemTo(t, e, null), e;
}, m.prototype.remainder = function(t) {
    var e = A();
    return this.divRemTo(t, null, e), e;
}, m.prototype.divideAndRemainder = function(t) {
    var e = A(), i = A();
    return this.divRemTo(t, e, i), new Array(e, i);
}, m.prototype.modPow = function(t, e) {
    var i, n, r = t.bitLength(), s = P(1);
    if (r <= 0) return s;
    i = r < 18 ? 1 : r < 48 ? 3 : r < 144 ? 4 : r < 768 ? 5 : 6, n = r < 8 ? new C(e) : e.isEven() ? new K(e) : new O(e);
    var a = new Array(), o = 3, h = i - 1, u = (1 << i) - 1;
    if (a[1] = n.convert(this), i > 1) {
        var c = A();
        for (n.sqrTo(a[1], c); o <= u; ) a[o] = A(), n.mulTo(c, a[o - 2], a[o]), o += 2;
    }
    var l, f, d = t.t - 1, g = !0, p = A();
    for (r = H(t[d]) - 1; d >= 0; ) {
        for (r >= h ? l = t[d] >> r - h & u : (l = (t[d] & (1 << r + 1) - 1) << h - r, d > 0 && (l |= t[d - 1] >> this.DB + r - h)), 
        o = i; 0 == (1 & l); ) l >>= 1, --o;
        if ((r -= o) < 0 && (r += this.DB, --d), g) a[l].copyTo(s), g = !1; else {
            for (;o > 1; ) n.sqrTo(s, p), n.sqrTo(p, s), o -= 2;
            o > 0 ? n.sqrTo(s, p) : (f = s, s = p, p = f), n.mulTo(p, a[l], s);
        }
        for (;d >= 0 && 0 == (t[d] & 1 << r); ) n.sqrTo(s, p), f = s, s = p, p = f, --r < 0 && (r = this.DB - 1, 
        --d);
    }
    return n.revert(s);
}, m.prototype.modInverse = function(t) {
    var e = t.isEven();
    if (this.isEven() && e || 0 == t.signum()) return m.ZERO;
    for (var i = t.clone(), n = this.clone(), r = P(1), s = P(0), a = P(0), o = P(1); 0 != i.signum(); ) {
        for (;i.isEven(); ) i.rShiftTo(1, i), e ? (r.isEven() && s.isEven() || (r.addTo(this, r), 
        s.subTo(t, s)), r.rShiftTo(1, r)) : s.isEven() || s.subTo(t, s), s.rShiftTo(1, s);
        for (;n.isEven(); ) n.rShiftTo(1, n), e ? (a.isEven() && o.isEven() || (a.addTo(this, a), 
        o.subTo(t, o)), a.rShiftTo(1, a)) : o.isEven() || o.subTo(t, o), o.rShiftTo(1, o);
        i.compareTo(n) >= 0 ? (i.subTo(n, i), e && r.subTo(a, r), s.subTo(o, s)) : (n.subTo(i, n), 
        e && a.subTo(r, a), o.subTo(s, o));
    }
    return 0 != n.compareTo(m.ONE) ? m.ZERO : o.compareTo(t) >= 0 ? o.subtract(t) : o.signum() < 0 ? (o.addTo(t, o), 
    o.signum() < 0 ? o.add(t) : o) : o;
}, m.prototype.pow = function(t) {
    return this.exp(t, new N());
}, m.prototype.gcd = function(t) {
    var e = this.s < 0 ? this.negate() : this.clone(), i = t.s < 0 ? t.negate() : t.clone();
    if (e.compareTo(i) < 0) {
        var n = e;
        e = i, i = n;
    }
    var r = e.getLowestSetBit(), s = i.getLowestSetBit();
    if (s < 0) return e;
    for (r < s && (s = r), s > 0 && (e.rShiftTo(s, e), i.rShiftTo(s, i)); e.signum() > 0; ) (r = e.getLowestSetBit()) > 0 && e.rShiftTo(r, e), 
    (r = i.getLowestSetBit()) > 0 && i.rShiftTo(r, i), e.compareTo(i) >= 0 ? (e.subTo(i, e), 
    e.rShiftTo(1, e)) : (i.subTo(e, i), i.rShiftTo(1, i));
    return s > 0 && i.lShiftTo(s, i), i;
}, m.prototype.isProbablePrime = function(t) {
    var e, i = this.abs();
    if (1 == i.t && i[0] <= _[_.length - 1]) {
        for (e = 0; e < _.length; ++e) if (i[0] == _[e]) return !0;
        return !1;
    }
    if (i.isEven()) return !1;
    for (e = 1; e < _.length; ) {
        for (var n = _[e], r = e + 1; r < _.length && n < L; ) n *= _[r++];
        for (n = i.modInt(n); e < r; ) if (n % _[e++] == 0) return !1;
    }
    return i.millerRabin(t);
}, m.prototype.square = function() {
    var t = A();
    return this.squareTo(t), t;
}, k.prototype.init = function(t) {
    var e, i, n;
    for (e = 0; e < 256; ++e) this.S[e] = e;
    for (i = 0, e = 0; e < 256; ++e) i = i + this.S[e] + t[e % t.length] & 255, n = this.S[e], 
    this.S[e] = this.S[i], this.S[i] = n;
    this.i = 0, this.j = 0;
}, k.prototype.next = function() {
    var t;
    return this.i = this.i + 1 & 255, this.j = this.j + this.S[this.i] & 255, t = this.S[this.i], 
    this.S[this.i] = this.S[this.j], this.S[this.j] = t, this.S[t + this.S[this.i] & 255];
};

var M, q, U;

/*! (c) Tom Wu | http://www-cs-students.stanford.edu/~tjw/jsbn/
 */ function z() {
    !function(t) {
        q[U++] ^= 255 & t, q[U++] ^= t >> 8 & 255, q[U++] ^= t >> 16 & 255, q[U++] ^= t >> 24 & 255, 
        U >= 256 && (U -= 256);
    }(new Date().getTime());
}

if (null == q) {
    var W;
    if (q = new Array(), U = 0, u.crypto && u.crypto.getRandomValues) {
        var J = new Uint8Array(32);
        for (u.crypto.getRandomValues(J), W = 0; W < 32; ++W) q[U++] = J[W];
    }
    if ("Netscape" == o.appName && o.appVersion < "5" && u.crypto && u.crypto.random) {
        var G = u.crypto.random(32);
        for (W = 0; W < G.length; ++W) q[U++] = 255 & G.charCodeAt(W);
    }
    for (;U < 256; ) W = Math.floor(65536 * Math.random()), q[U++] = W >>> 8, q[U++] = 255 & W;
    U = 0, z();
}

function X() {
    if (null == M) {
        for (z(), (M = new k()).init(q), U = 0; U < q.length; ++U) q[U] = 0;
        U = 0;
    }
    return M.next();
}

function Y() {}

/*! (c) Tom Wu | http://www-cs-students.stanford.edu/~tjw/jsbn/
 */ function $(t, e) {
    return new m(t, e);
}

function Z(t, e, i) {
    for (var n = "", r = 0; n.length < e; ) n += i(String.fromCharCode.apply(String, t.concat([ (********** & r) >> 24, (16711680 & r) >> 16, (65280 & r) >> 8, 255 & r ]))), 
    r += 1;
    return n;
}

function Q() {
    this.n = null, this.e = 0, this.d = null, this.p = null, this.q = null, this.dmp1 = null, 
    this.dmq1 = null, this.coeff = null;
}

function tt(t, e, i) {
    for (var n = "", r = 0; n.length < e; ) n += i(t + String.fromCharCode.apply(String, [ (********** & r) >> 24, (16711680 & r) >> 16, (65280 & r) >> 8, 255 & r ])), 
    r += 1;
    return n;
}

/*! (c) Tom Wu | http://www-cs-students.stanford.edu/~tjw/jsbn/
 */ function et(t, e) {
    this.x = e, this.q = t;
}

function it(t, e, i, n) {
    this.curve = t, this.x = e, this.y = i, this.z = null == n ? m.ONE : n, this.zinv = null;
}

function nt(t, e, i) {
    this.q = t, this.a = this.fromBigInteger(e), this.b = this.fromBigInteger(i), this.infinity = new it(this, null, null);
}

Y.prototype.nextBytes = function(t) {
    var e;
    for (e = 0; e < t.length; ++e) t[e] = X();
}, Q.prototype.doPublic = function(t) {
    return t.modPowInt(this.e, this.n);
}, Q.prototype.setPublic = function(t, e) {
    if (this.isPublic = !0, this.isPrivate = !1, "string" != typeof t) this.n = t, this.e = e; else {
        if (!(null != t && null != e && t.length > 0 && e.length > 0)) throw "Invalid RSA public key";
        this.n = $(t, 16), this.e = parseInt(e, 16);
    }
}, Q.prototype.encrypt = function(t) {
    var e = function(t, e) {
        if (e < t.length + 11) return console.log("Message too long for RSA"), null;
        for (var i = new Array(), n = t.length - 1; n >= 0 && e > 0; ) {
            var r = t.charCodeAt(n--);
            r < 128 ? i[--e] = r : r > 127 && r < 2048 ? (i[--e] = 63 & r | 128, i[--e] = r >> 6 | 192) : (i[--e] = 63 & r | 128, 
            i[--e] = r >> 6 & 63 | 128, i[--e] = r >> 12 | 224);
        }
        i[--e] = 0;
        for (var s = new Y(), a = new Array(); e > 2; ) {
            for (a[0] = 0; 0 == a[0]; ) s.nextBytes(a);
            i[--e] = a[0];
        }
        return i[--e] = 2, i[--e] = 0, new m(i);
    }(t, this.n.bitLength() + 7 >> 3);
    if (null == e) return null;
    var i = this.doPublic(e);
    if (null == i) return null;
    var n = i.toString(16);
    return 0 == (1 & n.length) ? n : "0" + n;
}, Q.prototype.encryptOAEP = function(t, e, i) {
    var n = function(t, e, i, n) {
        var r = c.crypto.MessageDigest, s = c.crypto.Util, a = null;
        if (i || (i = "sha1"), "string" == typeof i && (a = r.getCanonicalAlgName(i), n = r.getHashLength(a), 
        i = function(t) {
            return yt(s.hashString(t, a));
        }), t.length + 2 * n + 2 > e) throw "Message too long for RSA";
        var o, h = "";
        for (o = 0; o < e - t.length - 2 * n - 2; o += 1) h += "\0";
        var u = i("") + h + "" + t, l = new Array(n);
        new Y().nextBytes(l);
        var f = Z(l, u.length, i), d = [];
        for (o = 0; o < u.length; o += 1) d[o] = u.charCodeAt(o) ^ f.charCodeAt(o);
        var g = Z(d, l.length, i), p = [ 0 ];
        for (o = 0; o < l.length; o += 1) p[o + 1] = l[o] ^ g.charCodeAt(o);
        return new m(p.concat(d));
    }(t, this.n.bitLength() + 7 >> 3, e, i);
    if (null == n) return null;
    var r = this.doPublic(n);
    if (null == r) return null;
    var s = r.toString(16);
    return 0 == (1 & s.length) ? s : "0" + s;
}, Q.prototype.type = "RSA", Q.prototype.doPrivate = function(t) {
    if (null == this.p || null == this.q) return t.modPow(this.d, this.n);
    for (var e = t.mod(this.p).modPow(this.dmp1, this.p), i = t.mod(this.q).modPow(this.dmq1, this.q); e.compareTo(i) < 0; ) e = e.add(this.p);
    return e.subtract(i).multiply(this.coeff).mod(this.p).multiply(this.q).add(i);
}, Q.prototype.setPrivate = function(t, e, i) {
    this.isPrivate = !0, "string" != typeof t ? (this.n = t, this.e = e, this.d = i) : null != t && null != e && t.length > 0 && e.length > 0 ? (this.n = $(t, 16), 
    this.e = parseInt(e, 16), this.d = $(i, 16)) : alert("Invalid RSA private key");
}, Q.prototype.setPrivateEx = function(t, e, i, n, r, s, a, o) {
    if (this.isPrivate = !0, this.isPublic = !1, null == t) throw "RSASetPrivateEx N == null";
    if (null == e) throw "RSASetPrivateEx E == null";
    if (0 == t.length) throw "RSASetPrivateEx N.length == 0";
    if (0 == e.length) throw "RSASetPrivateEx E.length == 0";
    null != t && null != e && t.length > 0 && e.length > 0 ? (this.n = $(t, 16), this.e = parseInt(e, 16), 
    this.d = $(i, 16), this.p = $(n, 16), this.q = $(r, 16), this.dmp1 = $(s, 16), this.dmq1 = $(a, 16), 
    this.coeff = $(o, 16)) : alert("Invalid RSA private key in RSASetPrivateEx");
}, Q.prototype.generate = function(t, e) {
    var i = new Y(), n = t >> 1;
    this.e = parseInt(e, 16);
    for (var r = new m(e, 16); ;) {
        for (;this.p = new m(t - n, 1, i), 0 != this.p.subtract(m.ONE).gcd(r).compareTo(m.ONE) || !this.p.isProbablePrime(10); ) ;
        for (;this.q = new m(n, 1, i), 0 != this.q.subtract(m.ONE).gcd(r).compareTo(m.ONE) || !this.q.isProbablePrime(10); ) ;
        if (this.p.compareTo(this.q) <= 0) {
            var s = this.p;
            this.p = this.q, this.q = s;
        }
        var a = this.p.subtract(m.ONE), o = this.q.subtract(m.ONE), h = a.multiply(o);
        if (0 == h.gcd(r).compareTo(m.ONE)) {
            this.n = this.p.multiply(this.q), this.d = r.modInverse(h), this.dmp1 = this.d.mod(a), 
            this.dmq1 = this.d.mod(o), this.coeff = this.q.modInverse(this.p);
            break;
        }
    }
    this.isPrivate = !0;
}, Q.prototype.decrypt = function(t) {
    var e = $(t, 16), i = this.doPrivate(e);
    return null == i ? null : 
    /*! (c) Tom Wu | http://www-cs-students.stanford.edu/~tjw/jsbn/
 */
    function(t, e) {
        for (var i = t.toByteArray(), n = 0; n < i.length && 0 == i[n]; ) ++n;
        if (i.length - n != e - 1 || 2 != i[n]) return null;
        for (++n; 0 != i[n]; ) if (++n >= i.length) return null;
        for (var r = ""; ++n < i.length; ) {
            var s = 255 & i[n];
            s < 128 ? r += String.fromCharCode(s) : s > 191 && s < 224 ? (r += String.fromCharCode((31 & s) << 6 | 63 & i[n + 1]), 
            ++n) : (r += String.fromCharCode((15 & s) << 12 | (63 & i[n + 1]) << 6 | 63 & i[n + 2]), 
            n += 2);
        }
        return r;
    }(i, this.n.bitLength() + 7 >> 3);
}, Q.prototype.decryptOAEP = function(t, e, i) {
    var n = $(t, 16), r = this.doPrivate(n);
    return null == r ? null : function(t, e, i, n) {
        var r = c.crypto.MessageDigest, s = c.crypto.Util, a = null;
        for (i || (i = "sha1"), "string" == typeof i && (a = r.getCanonicalAlgName(i), n = r.getHashLength(a), 
        i = function(t) {
            return yt(s.hashString(t, a));
        }), t = t.toByteArray(), o = 0; o < t.length; o += 1) t[o] &= 255;
        for (;t.length < e; ) t.unshift(0);
        if ((t = String.fromCharCode.apply(String, t)).length < 2 * n + 2) throw "Cipher too short";
        var o, h = t.substr(1, n), u = t.substr(n + 1), l = tt(u, n, i), f = [];
        for (o = 0; o < h.length; o += 1) f[o] = h.charCodeAt(o) ^ l.charCodeAt(o);
        var d = tt(String.fromCharCode.apply(String, f), t.length - n, i), g = [];
        for (o = 0; o < u.length; o += 1) g[o] = u.charCodeAt(o) ^ d.charCodeAt(o);
        if ((g = String.fromCharCode.apply(String, g)).substr(0, n) !== i("")) throw "Hash mismatch";
        var p = (g = g.substr(n)).indexOf("");
        if ((-1 != p ? g.substr(0, p).lastIndexOf("\0") : -1) + 1 != p) throw "Malformed data";
        return g.substr(p + 1);
    }(r, this.n.bitLength() + 7 >> 3, e, i);
}, et.prototype.equals = function(t) {
    return t == this || this.q.equals(t.q) && this.x.equals(t.x);
}, et.prototype.toBigInteger = function() {
    return this.x;
}, et.prototype.negate = function() {
    return new et(this.q, this.x.negate().mod(this.q));
}, et.prototype.add = function(t) {
    return new et(this.q, this.x.add(t.toBigInteger()).mod(this.q));
}, et.prototype.subtract = function(t) {
    return new et(this.q, this.x.subtract(t.toBigInteger()).mod(this.q));
}, et.prototype.multiply = function(t) {
    return new et(this.q, this.x.multiply(t.toBigInteger()).mod(this.q));
}, et.prototype.square = function() {
    return new et(this.q, this.x.square().mod(this.q));
}, et.prototype.divide = function(t) {
    return new et(this.q, this.x.multiply(t.toBigInteger().modInverse(this.q)).mod(this.q));
}, it.prototype.getX = function() {
    return null == this.zinv && (this.zinv = this.z.modInverse(this.curve.q)), this.curve.fromBigInteger(this.x.toBigInteger().multiply(this.zinv).mod(this.curve.q));
}, it.prototype.getY = function() {
    return null == this.zinv && (this.zinv = this.z.modInverse(this.curve.q)), this.curve.fromBigInteger(this.y.toBigInteger().multiply(this.zinv).mod(this.curve.q));
}, it.prototype.equals = function(t) {
    return t == this || (this.isInfinity() ? t.isInfinity() : t.isInfinity() ? this.isInfinity() : !!t.y.toBigInteger().multiply(this.z).subtract(this.y.toBigInteger().multiply(t.z)).mod(this.curve.q).equals(m.ZERO) && t.x.toBigInteger().multiply(this.z).subtract(this.x.toBigInteger().multiply(t.z)).mod(this.curve.q).equals(m.ZERO));
}, it.prototype.isInfinity = function() {
    return null == this.x && null == this.y || this.z.equals(m.ZERO) && !this.y.toBigInteger().equals(m.ZERO);
}, it.prototype.negate = function() {
    return new it(this.curve, this.x, this.y.negate(), this.z);
}, it.prototype.add = function(t) {
    if (this.isInfinity()) return t;
    if (t.isInfinity()) return this;
    var e = t.y.toBigInteger().multiply(this.z).subtract(this.y.toBigInteger().multiply(t.z)).mod(this.curve.q), i = t.x.toBigInteger().multiply(this.z).subtract(this.x.toBigInteger().multiply(t.z)).mod(this.curve.q);
    if (m.ZERO.equals(i)) return m.ZERO.equals(e) ? this.twice() : this.curve.getInfinity();
    var n = new m("3"), r = this.x.toBigInteger(), s = this.y.toBigInteger(), a = (t.x.toBigInteger(), 
    t.y.toBigInteger(), i.square()), o = a.multiply(i), h = r.multiply(a), u = e.square().multiply(this.z), c = u.subtract(h.shiftLeft(1)).multiply(t.z).subtract(o).multiply(i).mod(this.curve.q), l = h.multiply(n).multiply(e).subtract(s.multiply(o)).subtract(u.multiply(e)).multiply(t.z).add(e.multiply(o)).mod(this.curve.q), f = o.multiply(this.z).multiply(t.z).mod(this.curve.q);
    return new it(this.curve, this.curve.fromBigInteger(c), this.curve.fromBigInteger(l), f);
}, it.prototype.twice = function() {
    if (this.isInfinity()) return this;
    if (0 == this.y.toBigInteger().signum()) return this.curve.getInfinity();
    var t = new m("3"), e = this.x.toBigInteger(), i = this.y.toBigInteger(), n = i.multiply(this.z), r = n.multiply(i).mod(this.curve.q), s = this.curve.a.toBigInteger(), a = e.square().multiply(t);
    m.ZERO.equals(s) || (a = a.add(this.z.square().multiply(s)));
    var o = (a = a.mod(this.curve.q)).square().subtract(e.shiftLeft(3).multiply(r)).shiftLeft(1).multiply(n).mod(this.curve.q), h = a.multiply(t).multiply(e).subtract(r.shiftLeft(1)).shiftLeft(2).multiply(r).subtract(a.square().multiply(a)).mod(this.curve.q), u = n.square().multiply(n).shiftLeft(3).mod(this.curve.q);
    return new it(this.curve, this.curve.fromBigInteger(o), this.curve.fromBigInteger(h), u);
}, it.prototype.multiply = function(t) {
    if (this.isInfinity()) return this;
    if (0 == t.signum()) return this.curve.getInfinity();
    var e, i = t, n = i.multiply(new m("3")), r = this.negate(), s = this;
    for (e = n.bitLength() - 2; e > 0; --e) {
        s = s.twice();
        var a = n.testBit(e);
        a != i.testBit(e) && (s = s.add(a ? this : r));
    }
    return s;
}, it.prototype.multiplyTwo = function(t, e, i) {
    var n;
    n = t.bitLength() > i.bitLength() ? t.bitLength() - 1 : i.bitLength() - 1;
    for (var r = this.curve.getInfinity(), s = this.add(e); n >= 0; ) r = r.twice(), 
    t.testBit(n) ? r = i.testBit(n) ? r.add(s) : r.add(this) : i.testBit(n) && (r = r.add(e)), 
    --n;
    return r;
}, nt.prototype.getQ = function() {
    return this.q;
}, nt.prototype.getA = function() {
    return this.a;
}, nt.prototype.getB = function() {
    return this.b;
}, nt.prototype.equals = function(t) {
    return t == this || this.q.equals(t.q) && this.a.equals(t.a) && this.b.equals(t.b);
}, nt.prototype.getInfinity = function() {
    return this.infinity;
}, nt.prototype.fromBigInteger = function(t) {
    return new et(this.q, t);
}, nt.prototype.decodePointHex = function(t) {
    switch (parseInt(t.substr(0, 2), 16)) {
      case 0:
        return this.infinity;

      case 2:
      case 3:
        return null;

      case 4:
      case 6:
      case 7:
        var e = (t.length - 2) / 2, i = t.substr(2, e), n = t.substr(e + 2, e);
        return new it(this, this.fromBigInteger(new m(i, 16)), this.fromBigInteger(new m(n, 16)));

      default:
        return null;
    }
}, 
/*! (c) Stefan Thomas | https://github.com/bitcoinjs/bitcoinjs-lib
 */
et.prototype.getByteLength = function() {
    return Math.floor((this.toBigInteger().bitLength() + 7) / 8);
}, it.prototype.getEncoded = function(t) {
    var e = function(t, e) {
        var i = t.toByteArrayUnsigned();
        if (e < i.length) i = i.slice(i.length - e); else for (;e > i.length; ) i.unshift(0);
        return i;
    }, i = this.getX().toBigInteger(), n = this.getY().toBigInteger(), r = e(i, 32);
    return t ? n.isEven() ? r.unshift(2) : r.unshift(3) : (r.unshift(4), r = r.concat(e(n, 32))), 
    r;
}, it.decodeFrom = function(t, e) {
    e[0];
    var i = e.length - 1, n = e.slice(1, 1 + i / 2), r = e.slice(1 + i / 2, 1 + i);
    n.unshift(0), r.unshift(0);
    var s = new m(n), a = new m(r);
    return new it(t, t.fromBigInteger(s), t.fromBigInteger(a));
}, it.decodeFromHex = function(t, e) {
    e.substr(0, 2);
    var i = e.length - 2, n = e.substr(2, i / 2), r = e.substr(2 + i / 2, i / 2), s = new m(n, 16), a = new m(r, 16);
    return new it(t, t.fromBigInteger(s), t.fromBigInteger(a));
}, it.prototype.add2D = function(t) {
    if (this.isInfinity()) return t;
    if (t.isInfinity()) return this;
    if (this.x.equals(t.x)) return this.y.equals(t.y) ? this.twice() : this.curve.getInfinity();
    var e = t.x.subtract(this.x), i = t.y.subtract(this.y).divide(e), n = i.square().subtract(this.x).subtract(t.x), r = i.multiply(this.x.subtract(n)).subtract(this.y);
    return new it(this.curve, n, r);
}, it.prototype.twice2D = function() {
    if (this.isInfinity()) return this;
    if (0 == this.y.toBigInteger().signum()) return this.curve.getInfinity();
    var t = this.curve.fromBigInteger(m.valueOf(2)), e = this.curve.fromBigInteger(m.valueOf(3)), i = this.x.square().multiply(e).add(this.curve.a).divide(this.y.multiply(t)), n = i.square().subtract(this.x.multiply(t)), r = i.multiply(this.x.subtract(n)).subtract(this.y);
    return new it(this.curve, n, r);
}, it.prototype.multiply2D = function(t) {
    if (this.isInfinity()) return this;
    if (0 == t.signum()) return this.curve.getInfinity();
    var e, i = t, n = i.multiply(new m("3")), r = this.negate(), s = this;
    for (e = n.bitLength() - 2; e > 0; --e) {
        s = s.twice();
        var a = n.testBit(e);
        a != i.testBit(e) && (s = s.add2D(a ? this : r));
    }
    return s;
}, it.prototype.isOnCurve = function() {
    var t = this.getX().toBigInteger(), e = this.getY().toBigInteger(), i = this.curve.getA().toBigInteger(), n = this.curve.getB().toBigInteger(), r = this.curve.getQ(), s = e.multiply(e).mod(r), a = t.multiply(t).multiply(t).add(i.multiply(t)).add(n).mod(r);
    return s.equals(a);
}, it.prototype.toString = function() {
    return "(" + this.getX().toBigInteger().toString() + "," + this.getY().toBigInteger().toString() + ")";
}, it.prototype.validate = function() {
    var t = this.curve.getQ();
    if (this.isInfinity()) throw new Error("Point is at infinity.");
    var e = this.getX().toBigInteger(), i = this.getY().toBigInteger();
    if (e.compareTo(m.ONE) < 0 || e.compareTo(t.subtract(m.ONE)) > 0) throw new Error("x coordinate out of bounds");
    if (i.compareTo(m.ONE) < 0 || i.compareTo(t.subtract(m.ONE)) > 0) throw new Error("y coordinate out of bounds");
    if (!this.isOnCurve()) throw new Error("Point is not on the curve.");
    if (this.multiply(t).isInfinity()) throw new Error("Point is not a scalar multiple of G.");
    return !0;
};

/*! Mike Samuel (c) 2009 | code.google.com/p/json-sans-eval
 */ var rt = function() {
    var t = new RegExp('(?:false|true|null|[\\{\\}\\[\\]]|(?:-?\\b(?:0|[1-9][0-9]*)(?:\\.[0-9]+)?(?:[eE][+-]?[0-9]+)?\\b)|(?:"(?:[^\\0-\\x08\\x0a-\\x1f"\\\\]|\\\\(?:["/\\\\bfnrt]|u[0-9A-Fa-f]{4}))*"))', "g"), e = new RegExp("\\\\(?:([^u])|u(.{4}))", "g"), i = {
        '"': '"',
        "/": "/",
        "\\": "\\",
        b: "\b",
        f: "\f",
        n: "\n",
        r: "\r",
        t: "\t"
    };
    function n(t, e, n) {
        return e ? i[e] : String.fromCharCode(parseInt(n, 16));
    }
    var r = new String(""), s = Object.hasOwnProperty;
    return function(i, o) {
        var h, u, c = i.match(t), l = c[0], f = !1;
        "{" === l ? h = {} : "[" === l ? h = [] : (h = [], f = !0);
        for (var d = [ h ], g = 1 - f, p = c.length; g < p; ++g) {
            var y;
            switch ((l = c[g]).charCodeAt(0)) {
              default:
                (y = d[0])[u || y.length] = +l, u = void 0;
                break;

              case 34:
                if (-1 !== (l = l.substring(1, l.length - 1)).indexOf("\\") && (l = l.replace(e, n)), 
                y = d[0], !u) {
                    if (!(y instanceof Array)) {
                        u = l || r;
                        break;
                    }
                    u = y.length;
                }
                y[u] = l, u = void 0;
                break;

              case 91:
                y = d[0], d.unshift(y[u || y.length] = []), u = void 0;
                break;

              case 93:
                d.shift();
                break;

              case 102:
                (y = d[0])[u || y.length] = !1, u = void 0;
                break;

              case 110:
                (y = d[0])[u || y.length] = null, u = void 0;
                break;

              case 116:
                (y = d[0])[u || y.length] = !0, u = void 0;
                break;

              case 123:
                y = d[0], d.unshift(y[u || y.length] = {}), u = void 0;
                break;

              case 125:
                d.shift();
            }
        }
        if (f) {
            if (1 !== d.length) throw new Error();
            h = h[0];
        } else if (d.length) throw new Error();
        if (o) {
            var v = function(t, e) {
                var i = t[e];
                if (i && "object" === a(i)) {
                    var n = null;
                    for (var r in i) if (s.call(i, r) && i !== t) {
                        var h = v(i, r);
                        void 0 !== h ? i[r] = h : (n || (n = []), n.push(r));
                    }
                    if (n) for (var u = n.length; --u >= 0; ) delete i[n[u]];
                }
                return o.call(t, e, i);
            };
            h = v({
                "": h
            }, "");
        }
        return h;
    };
}();

/*! asn1-1.0.12.js (c) 2013-2016 Kenji Urushima | kjur.github.com/jsrsasign/license
 */ void 0 !== c && c || (c = {}), void 0 !== c.asn1 && c.asn1 || (c.asn1 = {}), 
c.asn1.ASN1Util = new function() {
    this.integerToByteHex = function(t) {
        var e = t.toString(16);
        return e.length % 2 == 1 && (e = "0" + e), e;
    }, this.bigIntToMinTwosComplementsHex = function(t) {
        var e = t.toString(16);
        if ("-" != e.substr(0, 1)) e.length % 2 == 1 ? e = "0" + e : e.match(/^[0-7]/) || (e = "00" + e); else {
            var i = e.substr(1).length;
            i % 2 == 1 ? i += 1 : e.match(/^[0-7]/) || (i += 2);
            for (var n = "", r = 0; r < i; r++) n += "f";
            e = new m(n, 16).xor(t).add(m.ONE).toString(16).replace(/^-/, "");
        }
        return e;
    }, this.getPEMStringFromHex = function(t, e) {
        var i = St(t).replace(/(.{64})/g, "$1\r\n");
        return "-----BEGIN " + e + "-----\r\n" + (i = i.replace(/\r\n$/, "")) + "\r\n-----END " + e + "-----\r\n";
    }, this.newObject = function(t) {
        var e = c.asn1, i = Object.keys(t);
        if (1 != i.length) throw "key of param shall be only one.";
        var n = i[0];
        if (-1 == ":bool:int:bitstr:octstr:null:oid:enum:utf8str:numstr:prnstr:telstr:ia5str:utctime:gentime:seq:set:tag:".indexOf(":" + n + ":")) throw "undefined key: " + n;
        if ("bool" == n) return new e.DERBoolean(t[n]);
        if ("int" == n) return new e.DERInteger(t[n]);
        if ("bitstr" == n) return new e.DERBitString(t[n]);
        if ("octstr" == n) return new e.DEROctetString(t[n]);
        if ("null" == n) return new e.DERNull(t[n]);
        if ("oid" == n) return new e.DERObjectIdentifier(t[n]);
        if ("enum" == n) return new e.DEREnumerated(t[n]);
        if ("utf8str" == n) return new e.DERUTF8String(t[n]);
        if ("numstr" == n) return new e.DERNumericString(t[n]);
        if ("prnstr" == n) return new e.DERPrintableString(t[n]);
        if ("telstr" == n) return new e.DERTeletexString(t[n]);
        if ("ia5str" == n) return new e.DERIA5String(t[n]);
        if ("utctime" == n) return new e.DERUTCTime(t[n]);
        if ("gentime" == n) return new e.DERGeneralizedTime(t[n]);
        if ("seq" == n) {
            for (var r = t[n], s = [], a = 0; a < r.length; a++) {
                var o = e.ASN1Util.newObject(r[a]);
                s.push(o);
            }
            return new e.DERSequence({
                array: s
            });
        }
        if ("set" == n) {
            for (r = t[n], s = [], a = 0; a < r.length; a++) {
                o = e.ASN1Util.newObject(r[a]);
                s.push(o);
            }
            return new e.DERSet({
                array: s
            });
        }
        if ("tag" == n) {
            var h = t[n];
            if ("[object Array]" === Object.prototype.toString.call(h) && 3 == h.length) {
                var u = e.ASN1Util.newObject(h[2]);
                return new e.DERTaggedObject({
                    tag: h[0],
                    explicit: h[1],
                    obj: u
                });
            }
            var l = {};
            if (void 0 !== h.explicit && (l.explicit = h.explicit), void 0 !== h.tag && (l.tag = h.tag), 
            void 0 === h.obj) throw "obj shall be specified for 'tag'.";
            return l.obj = e.ASN1Util.newObject(h.obj), new e.DERTaggedObject(l);
        }
    }, this.jsonToASN1HEX = function(t) {
        return this.newObject(t).getEncodedHex();
    };
}(), c.asn1.ASN1Util.oidHexToInt = function(t) {
    for (var e = "", i = parseInt(t.substr(0, 2), 16), n = (e = Math.floor(i / 40) + "." + i % 40, 
    ""), r = 2; r < t.length; r += 2) {
        var s = ("00000000" + parseInt(t.substr(r, 2), 16).toString(2)).slice(-8);
        if (n += s.substr(1, 7), "0" == s.substr(0, 1)) e = e + "." + new m(n, 2).toString(10), 
        n = "";
    }
    return e;
}, c.asn1.ASN1Util.oidIntToHex = function(t) {
    var e = function(t) {
        var e = t.toString(16);
        return 1 == e.length && (e = "0" + e), e;
    }, i = function(t) {
        var i = "", n = new m(t, 10).toString(2), r = 7 - n.length % 7;
        7 == r && (r = 0);
        for (var s = "", a = 0; a < r; a++) s += "0";
        n = s + n;
        for (a = 0; a < n.length - 1; a += 7) {
            var o = n.substr(a, 7);
            a != n.length - 7 && (o = "1" + o), i += e(parseInt(o, 2));
        }
        return i;
    };
    if (!t.match(/^[0-9.]+$/)) throw "malformed oid string: " + t;
    var n = "", r = t.split("."), s = 40 * parseInt(r[0]) + parseInt(r[1]);
    n += e(s), r.splice(0, 2);
    for (var a = 0; a < r.length; a++) n += i(r[a]);
    return n;
}, c.asn1.ASN1Object = function() {
    this.getLengthHexFromValue = function() {
        if (void 0 === this.hV || null == this.hV) throw "this.hV is null or undefined.";
        if (this.hV.length % 2 == 1) throw "value hex must be even length: n=" + "".length + ",v=" + this.hV;
        var t = this.hV.length / 2, e = t.toString(16);
        if (e.length % 2 == 1 && (e = "0" + e), t < 128) return e;
        var i = e.length / 2;
        if (i > 15) throw "ASN.1 length too long to represent by 8x: n = " + t.toString(16);
        return (128 + i).toString(16) + e;
    }, this.getEncodedHex = function() {
        return (null == this.hTLV || this.isModified) && (this.hV = this.getFreshValueHex(), 
        this.hL = this.getLengthHexFromValue(), this.hTLV = this.hT + this.hL + this.hV, 
        this.isModified = !1), this.hTLV;
    }, this.getValueHex = function() {
        return this.getEncodedHex(), this.hV;
    }, this.getFreshValueHex = function() {
        return "";
    };
}, c.asn1.DERAbstractString = function(t) {
    c.asn1.DERAbstractString.superclass.constructor.call(this);
    this.getString = function() {
        return this.s;
    }, this.setString = function(t) {
        this.hTLV = null, this.isModified = !0, this.s = t, this.hV = ut(this.s);
    }, this.setStringHex = function(t) {
        this.hTLV = null, this.isModified = !0, this.s = null, this.hV = t;
    }, this.getFreshValueHex = function() {
        return this.hV;
    }, void 0 !== t && ("string" == typeof t ? this.setString(t) : void 0 !== t.str ? this.setString(t.str) : void 0 !== t.hex && this.setStringHex(t.hex));
}, l.lang.extend(c.asn1.DERAbstractString, c.asn1.ASN1Object), c.asn1.DERAbstractTime = function(t) {
    c.asn1.DERAbstractTime.superclass.constructor.call(this);
    this.localDateToUTC = function(t) {
        return utc = t.getTime() + 6e4 * t.getTimezoneOffset(), new Date(utc);
    }, this.formatDate = function(t, e, i) {
        var n = this.zeroPadding, r = this.localDateToUTC(t), s = String(r.getFullYear());
        "utc" == e && (s = s.substr(2, 2));
        var a = s + n(String(r.getMonth() + 1), 2) + n(String(r.getDate()), 2) + n(String(r.getHours()), 2) + n(String(r.getMinutes()), 2) + n(String(r.getSeconds()), 2);
        if (!0 === i) {
            var o = r.getMilliseconds();
            if (0 != o) {
                var h = n(String(o), 3);
                a = a + "." + (h = h.replace(/[0]+$/, ""));
            }
        }
        return a + "Z";
    }, this.zeroPadding = function(t, e) {
        return t.length >= e ? t : new Array(e - t.length + 1).join("0") + t;
    }, this.getString = function() {
        return this.s;
    }, this.setString = function(t) {
        this.hTLV = null, this.isModified = !0, this.s = t, this.hV = ut(t);
    }, this.setByDateValue = function(t, e, i, n, r, s) {
        var a = new Date(Date.UTC(t, e - 1, i, n, r, s, 0));
        this.setByDate(a);
    }, this.getFreshValueHex = function() {
        return this.hV;
    };
}, l.lang.extend(c.asn1.DERAbstractTime, c.asn1.ASN1Object), c.asn1.DERAbstractStructured = function(t) {
    c.asn1.DERAbstractString.superclass.constructor.call(this);
    this.setByASN1ObjectArray = function(t) {
        this.hTLV = null, this.isModified = !0, this.asn1Array = t;
    }, this.appendASN1Object = function(t) {
        this.hTLV = null, this.isModified = !0, this.asn1Array.push(t);
    }, this.asn1Array = new Array(), void 0 !== t && void 0 !== t.array && (this.asn1Array = t.array);
}, l.lang.extend(c.asn1.DERAbstractStructured, c.asn1.ASN1Object), c.asn1.DERBoolean = function() {
    c.asn1.DERBoolean.superclass.constructor.call(this), this.hT = "01", this.hTLV = "0101ff";
}, l.lang.extend(c.asn1.DERBoolean, c.asn1.ASN1Object), c.asn1.DERInteger = function(t) {
    c.asn1.DERInteger.superclass.constructor.call(this), this.hT = "02", this.setByBigInteger = function(t) {
        this.hTLV = null, this.isModified = !0, this.hV = c.asn1.ASN1Util.bigIntToMinTwosComplementsHex(t);
    }, this.setByInteger = function(t) {
        var e = new m(String(t), 10);
        this.setByBigInteger(e);
    }, this.setValueHex = function(t) {
        this.hV = t;
    }, this.getFreshValueHex = function() {
        return this.hV;
    }, void 0 !== t && (void 0 !== t.bigint ? this.setByBigInteger(t.bigint) : void 0 !== t.int ? this.setByInteger(t.int) : "number" == typeof t ? this.setByInteger(t) : void 0 !== t.hex && this.setValueHex(t.hex));
}, l.lang.extend(c.asn1.DERInteger, c.asn1.ASN1Object), c.asn1.DERBitString = function(t) {
    if (void 0 !== t && void 0 !== t.obj) {
        var e = c.asn1.ASN1Util.newObject(t.obj);
        t.hex = "00" + e.getEncodedHex();
    }
    c.asn1.DERBitString.superclass.constructor.call(this), this.hT = "03", this.setHexValueIncludingUnusedBits = function(t) {
        this.hTLV = null, this.isModified = !0, this.hV = t;
    }, this.setUnusedBitsAndHexValue = function(t, e) {
        if (t < 0 || 7 < t) throw "unused bits shall be from 0 to 7: u = " + t;
        var i = "0" + t;
        this.hTLV = null, this.isModified = !0, this.hV = i + e;
    }, this.setByBinaryString = function(t) {
        var e = 8 - (t = t.replace(/0+$/, "")).length % 8;
        8 == e && (e = 0);
        for (var i = 0; i <= e; i++) t += "0";
        var n = "";
        for (i = 0; i < t.length - 1; i += 8) {
            var r = t.substr(i, 8), s = parseInt(r, 2).toString(16);
            1 == s.length && (s = "0" + s), n += s;
        }
        this.hTLV = null, this.isModified = !0, this.hV = "0" + e + n;
    }, this.setByBooleanArray = function(t) {
        for (var e = "", i = 0; i < t.length; i++) 1 == t[i] ? e += "1" : e += "0";
        this.setByBinaryString(e);
    }, this.newFalseArray = function(t) {
        for (var e = new Array(t), i = 0; i < t; i++) e[i] = !1;
        return e;
    }, this.getFreshValueHex = function() {
        return this.hV;
    }, void 0 !== t && ("string" == typeof t && t.toLowerCase().match(/^[0-9a-f]+$/) ? this.setHexValueIncludingUnusedBits(t) : void 0 !== t.hex ? this.setHexValueIncludingUnusedBits(t.hex) : void 0 !== t.bin ? this.setByBinaryString(t.bin) : void 0 !== t.array && this.setByBooleanArray(t.array));
}, l.lang.extend(c.asn1.DERBitString, c.asn1.ASN1Object), c.asn1.DEROctetString = function(t) {
    if (void 0 !== t && void 0 !== t.obj) {
        var e = c.asn1.ASN1Util.newObject(t.obj);
        t.hex = e.getEncodedHex();
    }
    c.asn1.DEROctetString.superclass.constructor.call(this, t), this.hT = "04";
}, l.lang.extend(c.asn1.DEROctetString, c.asn1.DERAbstractString), c.asn1.DERNull = function() {
    c.asn1.DERNull.superclass.constructor.call(this), this.hT = "05", this.hTLV = "0500";
}, l.lang.extend(c.asn1.DERNull, c.asn1.ASN1Object), c.asn1.DERObjectIdentifier = function(t) {
    var e = function(t) {
        var e = t.toString(16);
        return 1 == e.length && (e = "0" + e), e;
    }, i = function(t) {
        var i = "", n = new m(t, 10).toString(2), r = 7 - n.length % 7;
        7 == r && (r = 0);
        for (var s = "", a = 0; a < r; a++) s += "0";
        n = s + n;
        for (a = 0; a < n.length - 1; a += 7) {
            var o = n.substr(a, 7);
            a != n.length - 7 && (o = "1" + o), i += e(parseInt(o, 2));
        }
        return i;
    };
    c.asn1.DERObjectIdentifier.superclass.constructor.call(this), this.hT = "06", this.setValueHex = function(t) {
        this.hTLV = null, this.isModified = !0, this.s = null, this.hV = t;
    }, this.setValueOidString = function(t) {
        if (!t.match(/^[0-9.]+$/)) throw "malformed oid string: " + t;
        var n = "", r = t.split("."), s = 40 * parseInt(r[0]) + parseInt(r[1]);
        n += e(s), r.splice(0, 2);
        for (var a = 0; a < r.length; a++) n += i(r[a]);
        this.hTLV = null, this.isModified = !0, this.s = null, this.hV = n;
    }, this.setValueName = function(t) {
        var e = c.asn1.x509.OID.name2oid(t);
        if ("" === e) throw "DERObjectIdentifier oidName undefined: " + t;
        this.setValueOidString(e);
    }, this.getFreshValueHex = function() {
        return this.hV;
    }, void 0 !== t && ("string" == typeof t ? t.match(/^[0-2].[0-9.]+$/) ? this.setValueOidString(t) : this.setValueName(t) : void 0 !== t.oid ? this.setValueOidString(t.oid) : void 0 !== t.hex ? this.setValueHex(t.hex) : void 0 !== t.name && this.setValueName(t.name));
}, l.lang.extend(c.asn1.DERObjectIdentifier, c.asn1.ASN1Object), c.asn1.DEREnumerated = function(t) {
    c.asn1.DEREnumerated.superclass.constructor.call(this), this.hT = "0a", this.setByBigInteger = function(t) {
        this.hTLV = null, this.isModified = !0, this.hV = c.asn1.ASN1Util.bigIntToMinTwosComplementsHex(t);
    }, this.setByInteger = function(t) {
        var e = new m(String(t), 10);
        this.setByBigInteger(e);
    }, this.setValueHex = function(t) {
        this.hV = t;
    }, this.getFreshValueHex = function() {
        return this.hV;
    }, void 0 !== t && (void 0 !== t.int ? this.setByInteger(t.int) : "number" == typeof t ? this.setByInteger(t) : void 0 !== t.hex && this.setValueHex(t.hex));
}, l.lang.extend(c.asn1.DEREnumerated, c.asn1.ASN1Object), c.asn1.DERUTF8String = function(t) {
    c.asn1.DERUTF8String.superclass.constructor.call(this, t), this.hT = "0c";
}, l.lang.extend(c.asn1.DERUTF8String, c.asn1.DERAbstractString), c.asn1.DERNumericString = function(t) {
    c.asn1.DERNumericString.superclass.constructor.call(this, t), this.hT = "12";
}, l.lang.extend(c.asn1.DERNumericString, c.asn1.DERAbstractString), c.asn1.DERPrintableString = function(t) {
    c.asn1.DERPrintableString.superclass.constructor.call(this, t), this.hT = "13";
}, l.lang.extend(c.asn1.DERPrintableString, c.asn1.DERAbstractString), c.asn1.DERTeletexString = function(t) {
    c.asn1.DERTeletexString.superclass.constructor.call(this, t), this.hT = "14";
}, l.lang.extend(c.asn1.DERTeletexString, c.asn1.DERAbstractString), c.asn1.DERIA5String = function(t) {
    c.asn1.DERIA5String.superclass.constructor.call(this, t), this.hT = "16";
}, l.lang.extend(c.asn1.DERIA5String, c.asn1.DERAbstractString), c.asn1.DERUTCTime = function(t) {
    c.asn1.DERUTCTime.superclass.constructor.call(this, t), this.hT = "17", this.setByDate = function(t) {
        this.hTLV = null, this.isModified = !0, this.date = t, this.s = this.formatDate(this.date, "utc"), 
        this.hV = ut(this.s);
    }, this.getFreshValueHex = function() {
        return void 0 === this.date && void 0 === this.s && (this.date = new Date(), this.s = this.formatDate(this.date, "utc"), 
        this.hV = ut(this.s)), this.hV;
    }, void 0 !== t && (void 0 !== t.str ? this.setString(t.str) : "string" == typeof t && t.match(/^[0-9]{12}Z$/) ? this.setString(t) : void 0 !== t.hex ? this.setStringHex(t.hex) : void 0 !== t.date && this.setByDate(t.date));
}, l.lang.extend(c.asn1.DERUTCTime, c.asn1.DERAbstractTime), c.asn1.DERGeneralizedTime = function(t) {
    c.asn1.DERGeneralizedTime.superclass.constructor.call(this, t), this.hT = "18", 
    this.withMillis = !1, this.setByDate = function(t) {
        this.hTLV = null, this.isModified = !0, this.date = t, this.s = this.formatDate(this.date, "gen", this.withMillis), 
        this.hV = ut(this.s);
    }, this.getFreshValueHex = function() {
        return void 0 === this.date && void 0 === this.s && (this.date = new Date(), this.s = this.formatDate(this.date, "gen", this.withMillis), 
        this.hV = ut(this.s)), this.hV;
    }, void 0 !== t && (void 0 !== t.str ? this.setString(t.str) : "string" == typeof t && t.match(/^[0-9]{14}Z$/) ? this.setString(t) : void 0 !== t.hex ? this.setStringHex(t.hex) : void 0 !== t.date && this.setByDate(t.date), 
    !0 === t.millis && (this.withMillis = !0));
}, l.lang.extend(c.asn1.DERGeneralizedTime, c.asn1.DERAbstractTime), c.asn1.DERSequence = function(t) {
    c.asn1.DERSequence.superclass.constructor.call(this, t), this.hT = "30", this.getFreshValueHex = function() {
        for (var t = "", e = 0; e < this.asn1Array.length; e++) {
            t += this.asn1Array[e].getEncodedHex();
        }
        return this.hV = t, this.hV;
    };
}, l.lang.extend(c.asn1.DERSequence, c.asn1.DERAbstractStructured), c.asn1.DERSet = function(t) {
    c.asn1.DERSet.superclass.constructor.call(this, t), this.hT = "31", this.sortFlag = !0, 
    this.getFreshValueHex = function() {
        for (var t = new Array(), e = 0; e < this.asn1Array.length; e++) {
            var i = this.asn1Array[e];
            t.push(i.getEncodedHex());
        }
        return 1 == this.sortFlag && t.sort(), this.hV = t.join(""), this.hV;
    }, void 0 !== t && void 0 !== t.sortflag && 0 == t.sortflag && (this.sortFlag = !1);
}, l.lang.extend(c.asn1.DERSet, c.asn1.DERAbstractStructured), c.asn1.DERTaggedObject = function(t) {
    c.asn1.DERTaggedObject.superclass.constructor.call(this), this.hT = "a0", this.hV = "", 
    this.isExplicit = !0, this.asn1Object = null, this.setASN1Object = function(t, e, i) {
        this.hT = e, this.isExplicit = t, this.asn1Object = i, this.isExplicit ? (this.hV = this.asn1Object.getEncodedHex(), 
        this.hTLV = null, this.isModified = !0) : (this.hV = null, this.hTLV = i.getEncodedHex(), 
        this.hTLV = this.hTLV.replace(/^../, e), this.isModified = !1);
    }, this.getFreshValueHex = function() {
        return this.hV;
    }, void 0 !== t && (void 0 !== t.tag && (this.hT = t.tag), void 0 !== t.explicit && (this.isExplicit = t.explicit), 
    void 0 !== t.obj && (this.asn1Object = t.obj, this.setASN1Object(this.isExplicit, this.hT, this.asn1Object)));
}, l.lang.extend(c.asn1.DERTaggedObject, c.asn1.ASN1Object);

/*! asn1hex-1.1.9.js (c) 2012-2017 Kenji Urushima | kjur.github.com/jsrsasign/license
 */ var st, at, ot = new function() {}();

function ht(t) {
    for (var e = "", i = 0; i < t.length; i++) {
        var n = t[i].toString(16);
        1 == n.length && (n = "0" + n), e += n;
    }
    return e;
}

function ut(t) {
    return ht(function(t) {
        for (var e = new Array(), i = 0; i < t.length; i++) e[i] = t.charCodeAt(i);
        return e;
    }(t));
}

function ct(t) {
    return t = (t = (t = t.replace(/\=/g, "")).replace(/\+/g, "-")).replace(/\//g, "_");
}

function lt(t) {
    return t.length % 4 == 2 ? t += "==" : t.length % 4 == 3 && (t += "="), t = (t = t.replace(/-/g, "+")).replace(/_/g, "/");
}

function ft(t) {
    return t.length % 2 == 1 && (t = "0" + t), ct(v(t));
}

function dt(t) {
    return S(lt(t));
}

function gt(t) {
    return mt(xt(t));
}

function pt(t) {
    return decodeURIComponent(At(t));
}

function yt(t) {
    for (var e = "", i = 0; i < t.length - 1; i += 2) e += String.fromCharCode(parseInt(t.substr(i, 2), 16));
    return e;
}

function vt(t) {
    for (var e = "", i = 0; i < t.length; i++) e += ("0" + t.charCodeAt(i).toString(16)).slice(-2);
    return e;
}

function St(t) {
    return v(t);
}

function mt(t) {
    return t.replace(/%/g, "");
}

function At(t) {
    return t.replace(/(..)/g, "%$1");
}

function xt(t) {
    for (var e = encodeURIComponent(t), i = "", n = 0; n < e.length; n++) "%" == e[n] ? (i += e.substr(n, 3), 
    n += 2) : i = i + "%" + ut(e[n]);
    return i;
}

ot.getByteLengthOfL_AtObj = function(t, e) {
    if ("8" != t.substring(e + 2, e + 3)) return 1;
    var i = parseInt(t.substring(e + 3, e + 4));
    return 0 == i ? -1 : 0 < i && i < 10 ? i + 1 : -2;
}, ot.getHexOfL_AtObj = function(t, e) {
    var i = ot.getByteLengthOfL_AtObj(t, e);
    return i < 1 ? "" : t.substring(e + 2, e + 2 + 2 * i);
}, ot.getIntOfL_AtObj = function(t, e) {
    var i = ot.getHexOfL_AtObj(t, e);
    return "" == i ? -1 : (parseInt(i.substring(0, 1)) < 8 ? new m(i, 16) : new m(i.substring(2), 16)).intValue();
}, ot.getStartPosOfV_AtObj = function(t, e) {
    var i = ot.getByteLengthOfL_AtObj(t, e);
    return i < 0 ? i : e + 2 * (i + 1);
}, ot.getHexOfV_AtObj = function(t, e) {
    var i = ot.getStartPosOfV_AtObj(t, e), n = ot.getIntOfL_AtObj(t, e);
    return t.substring(i, i + 2 * n);
}, ot.getHexOfTLV_AtObj = function(t, e) {
    return t.substr(e, 2) + ot.getHexOfL_AtObj(t, e) + ot.getHexOfV_AtObj(t, e);
}, ot.getPosOfNextSibling_AtObj = function(t, e) {
    return ot.getStartPosOfV_AtObj(t, e) + 2 * ot.getIntOfL_AtObj(t, e);
}, ot.getPosArrayOfChildren_AtObj = function(t, e) {
    var i = new Array(), n = ot.getStartPosOfV_AtObj(t, e);
    "03" == t.substr(e, 2) ? i.push(n + 2) : i.push(n);
    for (var r = ot.getIntOfL_AtObj(t, e), s = n, a = 0; ;) {
        var o = ot.getPosOfNextSibling_AtObj(t, s);
        if (null == o || o - n >= 2 * r) break;
        if (a >= 200) break;
        i.push(o), s = o, a++;
    }
    return i;
}, ot.getNthChildIndex_AtObj = function(t, e, i) {
    return ot.getPosArrayOfChildren_AtObj(t, e)[i];
}, ot.getDecendantIndexByNthList = function(t, e, i) {
    if (0 == i.length) return e;
    var n = i.shift(), r = ot.getPosArrayOfChildren_AtObj(t, e);
    return ot.getDecendantIndexByNthList(t, r[n], i);
}, ot.getDecendantHexTLVByNthList = function(t, e, i) {
    var n = ot.getDecendantIndexByNthList(t, e, i);
    return ot.getHexOfTLV_AtObj(t, n);
}, ot.getDecendantHexVByNthList = function(t, e, i) {
    var n = ot.getDecendantIndexByNthList(t, e, i);
    return ot.getHexOfV_AtObj(t, n);
}, ot.getVbyList = function(t, e, i, n) {
    var r = ot.getDecendantIndexByNthList(t, e, i);
    if (void 0 === r) throw "can't find nthList object";
    if (void 0 !== n && t.substr(r, 2) != n) throw "checking tag doesn't match: " + t.substr(r, 2) + "!=" + n;
    return ot.getHexOfV_AtObj(t, r);
}, ot.hextooidstr = function(t) {
    var e = function(t, e) {
        return t.length >= e ? t : new Array(e - t.length + 1).join("0") + t;
    }, i = [], n = t.substr(0, 2), r = parseInt(n, 16);
    i[0] = new String(Math.floor(r / 40)), i[1] = new String(r % 40);
    for (var s = t.substr(2), a = [], o = 0; o < s.length / 2; o++) a.push(parseInt(s.substr(2 * o, 2), 16));
    var h = [], u = "";
    for (o = 0; o < a.length; o++) 128 & a[o] ? u += e((127 & a[o]).toString(2), 7) : (u += e((127 & a[o]).toString(2), 7), 
    h.push(new String(parseInt(u, 2))), u = "");
    var c = i.join(".");
    return h.length > 0 && (c = c + "." + h.join(".")), c;
}, ot.dump = function(t, e, i, n) {
    var r = t;
    t instanceof c.asn1.ASN1Object && (r = t.getEncodedHex());
    var s = function(t, e) {
        return t.length <= 2 * e ? t : t.substr(0, e) + "..(total " + t.length / 2 + "bytes).." + t.substr(t.length - e, e);
    };
    void 0 === e && (e = {
        ommit_long_octet: 32
    }), void 0 === i && (i = 0), void 0 === n && (n = "");
    var a = e.ommit_long_octet;
    if ("01" == r.substr(i, 2)) return "00" == (o = ot.getHexOfV_AtObj(r, i)) ? n + "BOOLEAN FALSE\n" : n + "BOOLEAN TRUE\n";
    if ("02" == r.substr(i, 2)) return n + "INTEGER " + s(o = ot.getHexOfV_AtObj(r, i), a) + "\n";
    if ("03" == r.substr(i, 2)) return n + "BITSTRING " + s(o = ot.getHexOfV_AtObj(r, i), a) + "\n";
    if ("04" == r.substr(i, 2)) {
        var o = ot.getHexOfV_AtObj(r, i);
        if (ot.isASN1HEX(o)) {
            var h = n + "OCTETSTRING, encapsulates\n";
            return h += ot.dump(o, e, 0, n + "  ");
        }
        return n + "OCTETSTRING " + s(o, a) + "\n";
    }
    if ("05" == r.substr(i, 2)) return n + "NULL\n";
    if ("06" == r.substr(i, 2)) {
        var u = ot.getHexOfV_AtObj(r, i), l = c.asn1.ASN1Util.oidHexToInt(u), f = c.asn1.x509.OID.oid2name(l), d = l.replace(/\./g, " ");
        return "" != f ? n + "ObjectIdentifier " + f + " (" + d + ")\n" : n + "ObjectIdentifier (" + d + ")\n";
    }
    if ("0c" == r.substr(i, 2)) return n + "UTF8String '" + pt(ot.getHexOfV_AtObj(r, i)) + "'\n";
    if ("13" == r.substr(i, 2)) return n + "PrintableString '" + pt(ot.getHexOfV_AtObj(r, i)) + "'\n";
    if ("14" == r.substr(i, 2)) return n + "TeletexString '" + pt(ot.getHexOfV_AtObj(r, i)) + "'\n";
    if ("16" == r.substr(i, 2)) return n + "IA5String '" + pt(ot.getHexOfV_AtObj(r, i)) + "'\n";
    if ("17" == r.substr(i, 2)) return n + "UTCTime " + pt(ot.getHexOfV_AtObj(r, i)) + "\n";
    if ("18" == r.substr(i, 2)) return n + "GeneralizedTime " + pt(ot.getHexOfV_AtObj(r, i)) + "\n";
    if ("30" == r.substr(i, 2)) {
        if ("3000" == r.substr(i, 4)) return n + "SEQUENCE {}\n";
        h = n + "SEQUENCE\n";
        var g = e;
        if ((2 == (S = ot.getPosArrayOfChildren_AtObj(r, i)).length || 3 == S.length) && "06" == r.substr(S[0], 2) && "04" == r.substr(S[S.length - 1], 2)) {
            var p = ot.getHexOfV_AtObj(r, S[0]), y = (l = c.asn1.ASN1Util.oidHexToInt(p), f = c.asn1.x509.OID.oid2name(l), 
            JSON.parse(JSON.stringify(e)));
            y.x509ExtName = f, g = y;
        }
        for (var v = 0; v < S.length; v++) h += ot.dump(r, g, S[v], n + "  ");
        return h;
    }
    if ("31" == r.substr(i, 2)) {
        h = n + "SET\n";
        var S = ot.getPosArrayOfChildren_AtObj(r, i);
        for (v = 0; v < S.length; v++) h += ot.dump(r, e, S[v], n + "  ");
        return h;
    }
    var m = parseInt(r.substr(i, 2), 16);
    if (0 != (128 & m)) {
        var A = 31 & m;
        if (0 != (32 & m)) {
            var h = n + "[" + A + "]\n";
            for (S = ot.getPosArrayOfChildren_AtObj(r, i), v = 0; v < S.length; v++) h += ot.dump(r, e, S[v], n + "  ");
            return h;
        }
        return "68747470" == (o = ot.getHexOfV_AtObj(r, i)).substr(0, 8) && (o = pt(o)), 
        "subjectAltName" === e.x509ExtName && 2 == A && (o = pt(o)), h = n + "[" + A + "] " + o + "\n";
    }
    return n + "UNKNOWN(" + r.substr(i, 2) + ") " + ot.getHexOfV_AtObj(r, i) + "\n";
}, ot.isASN1HEX = function(t) {
    if (t.length % 2 == 1) return !1;
    var e = ot.getIntOfL_AtObj(t, 0), i = t.substr(0, 2), n = ot.getHexOfL_AtObj(t, 0);
    return t.length - i.length - n.length == 2 * e;
}, ot.pemToHex = function(t, e) {
    if (-1 == t.indexOf("-----BEGIN ")) throw "can't find PEM header: " + e;
    return S((t = void 0 !== e ? (t = t.replace("-----BEGIN " + e + "-----", "")).replace("-----END " + e + "-----", "") : (t = t.replace(/-----BEGIN [^-]+-----/, "")).replace(/-----END [^-]+-----/, "")).replace(/\s+/g, ""));
}, 
/*! asn1x509-1.0.22.js (c) 2013-2017 Kenji Urushima | kjur.github.com/jsrsasign/license
 */
void 0 !== c && c || (c = {}), void 0 !== c.asn1 && c.asn1 || (c.asn1 = {}), void 0 !== c.asn1.x509 && c.asn1.x509 || (c.asn1.x509 = {}), 
c.asn1.x509.Certificate = function(t) {
    c.asn1.x509.Certificate.superclass.constructor.call(this);
    this.setRsaPrvKeyByPEMandPass = function(t, e) {
        var i = bt.getDecryptedKeyHex(t, e), n = new Q();
        n.readPrivateKeyFromASN1HexString(i), this.prvKey = n;
    }, this.sign = function() {
        this.asn1SignatureAlg = this.asn1TBSCert.asn1SignatureAlg;
        var t = new c.crypto.Signature({
            alg: this.asn1SignatureAlg.nameAlg
        });
        t.init(this.prvKey), t.updateHex(this.asn1TBSCert.getEncodedHex()), this.hexSig = t.sign(), 
        this.asn1Sig = new c.asn1.DERBitString({
            hex: "00" + this.hexSig
        });
        var e = new c.asn1.DERSequence({
            array: [ this.asn1TBSCert, this.asn1SignatureAlg, this.asn1Sig ]
        });
        this.hTLV = e.getEncodedHex(), this.isModified = !1;
    }, this.setSignatureHex = function(t) {
        this.asn1SignatureAlg = this.asn1TBSCert.asn1SignatureAlg, this.hexSig = t, this.asn1Sig = new c.asn1.DERBitString({
            hex: "00" + this.hexSig
        });
        var e = new c.asn1.DERSequence({
            array: [ this.asn1TBSCert, this.asn1SignatureAlg, this.asn1Sig ]
        });
        this.hTLV = e.getEncodedHex(), this.isModified = !1;
    }, this.getEncodedHex = function() {
        if (0 == this.isModified && null != this.hTLV) return this.hTLV;
        throw "not signed yet";
    }, this.getPEMString = function() {
        var t = this.getEncodedHex(), e = g.enc.Hex.parse(t);
        return "-----BEGIN CERTIFICATE-----\r\n" + g.enc.Base64.stringify(e).replace(/(.{64})/g, "$1\r\n") + "\r\n-----END CERTIFICATE-----\r\n";
    }, void 0 !== t && (void 0 !== t.tbscertobj && (this.asn1TBSCert = t.tbscertobj), 
    void 0 !== t.prvkeyobj ? this.prvKey = t.prvkeyobj : void 0 !== t.rsaprvkey ? this.prvKey = t.rsaprvkey : void 0 !== t.rsaprvpem && void 0 !== t.rsaprvpas && this.setRsaPrvKeyByPEMandPass(t.rsaprvpem, t.rsaprvpas));
}, l.lang.extend(c.asn1.x509.Certificate, c.asn1.ASN1Object), c.asn1.x509.TBSCertificate = function(t) {
    c.asn1.x509.TBSCertificate.superclass.constructor.call(this), this._initialize = function() {
        this.asn1Array = new Array(), this.asn1Version = new c.asn1.DERTaggedObject({
            obj: new c.asn1.DERInteger({
                int: 2
            })
        }), this.asn1SerialNumber = null, this.asn1SignatureAlg = null, this.asn1Issuer = null, 
        this.asn1NotBefore = null, this.asn1NotAfter = null, this.asn1Subject = null, this.asn1SubjPKey = null, 
        this.extensionsArray = new Array();
    }, this.setSerialNumberByParam = function(t) {
        this.asn1SerialNumber = new c.asn1.DERInteger(t);
    }, this.setSignatureAlgByParam = function(t) {
        this.asn1SignatureAlg = new c.asn1.x509.AlgorithmIdentifier(t);
    }, this.setIssuerByParam = function(t) {
        this.asn1Issuer = new c.asn1.x509.X500Name(t);
    }, this.setNotBeforeByParam = function(t) {
        this.asn1NotBefore = new c.asn1.x509.Time(t);
    }, this.setNotAfterByParam = function(t) {
        this.asn1NotAfter = new c.asn1.x509.Time(t);
    }, this.setSubjectByParam = function(t) {
        this.asn1Subject = new c.asn1.x509.X500Name(t);
    }, this.setSubjectPublicKeyByParam = function(t) {
        this.asn1SubjPKey = new c.asn1.x509.SubjectPublicKeyInfo(t);
    }, this.setSubjectPublicKeyByGetKey = function(t) {
        var e = Ft.getKey(t);
        this.asn1SubjPKey = new c.asn1.x509.SubjectPublicKeyInfo(e);
    }, this.appendExtension = function(t) {
        this.extensionsArray.push(t);
    }, this.appendExtensionByName = function(t, e) {
        c.asn1.x509.Extension.appendByNameToArray(t, e, this.extensionsArray);
    }, this.getEncodedHex = function() {
        if (null == this.asn1NotBefore || null == this.asn1NotAfter) throw "notBefore and/or notAfter not set";
        var t = new c.asn1.DERSequence({
            array: [ this.asn1NotBefore, this.asn1NotAfter ]
        });
        if (this.asn1Array = new Array(), this.asn1Array.push(this.asn1Version), this.asn1Array.push(this.asn1SerialNumber), 
        this.asn1Array.push(this.asn1SignatureAlg), this.asn1Array.push(this.asn1Issuer), 
        this.asn1Array.push(t), this.asn1Array.push(this.asn1Subject), this.asn1Array.push(this.asn1SubjPKey), 
        this.extensionsArray.length > 0) {
            var e = new c.asn1.DERSequence({
                array: this.extensionsArray
            }), i = new c.asn1.DERTaggedObject({
                explicit: !0,
                tag: "a3",
                obj: e
            });
            this.asn1Array.push(i);
        }
        var n = new c.asn1.DERSequence({
            array: this.asn1Array
        });
        return this.hTLV = n.getEncodedHex(), this.isModified = !1, this.hTLV;
    }, this._initialize();
}, l.lang.extend(c.asn1.x509.TBSCertificate, c.asn1.ASN1Object), c.asn1.x509.Extension = function(t) {
    c.asn1.x509.Extension.superclass.constructor.call(this);
    this.getEncodedHex = function() {
        var t = new c.asn1.DERObjectIdentifier({
            oid: this.oid
        }), e = new c.asn1.DEROctetString({
            hex: this.getExtnValueHex()
        }), i = new Array();
        return i.push(t), this.critical && i.push(new c.asn1.DERBoolean()), i.push(e), new c.asn1.DERSequence({
            array: i
        }).getEncodedHex();
    }, this.critical = !1, void 0 !== t && void 0 !== t.critical && (this.critical = t.critical);
}, l.lang.extend(c.asn1.x509.Extension, c.asn1.ASN1Object), c.asn1.x509.Extension.appendByNameToArray = function(t, e, i) {
    if ("basicconstraints" == t.toLowerCase()) {
        var n = new c.asn1.x509.BasicConstraints(e);
        i.push(n);
    } else if ("keyusage" == t.toLowerCase()) {
        n = new c.asn1.x509.KeyUsage(e);
        i.push(n);
    } else if ("crldistributionpoints" == t.toLowerCase()) {
        n = new c.asn1.x509.CRLDistributionPoints(e);
        i.push(n);
    } else if ("extkeyusage" == t.toLowerCase()) {
        n = new c.asn1.x509.ExtKeyUsage(e);
        i.push(n);
    } else if ("authoritykeyidentifier" == t.toLowerCase()) {
        n = new c.asn1.x509.AuthorityKeyIdentifier(e);
        i.push(n);
    } else if ("authorityinfoaccess" == t.toLowerCase()) {
        n = new c.asn1.x509.AuthorityInfoAccess(e);
        i.push(n);
    } else if ("subjectaltname" == t.toLowerCase()) {
        n = new c.asn1.x509.SubjectAltName(e);
        i.push(n);
    } else {
        if ("issueraltname" != t.toLowerCase()) throw "unsupported extension name: " + t;
        n = new c.asn1.x509.IssuerAltName(e);
        i.push(n);
    }
}, c.asn1.x509.KeyUsage = function(t) {
    c.asn1.x509.KeyUsage.superclass.constructor.call(this, t), this.getExtnValueHex = function() {
        return this.asn1ExtnValue.getEncodedHex();
    }, this.oid = "*********", void 0 !== t && void 0 !== t.bin && (this.asn1ExtnValue = new c.asn1.DERBitString(t));
}, l.lang.extend(c.asn1.x509.KeyUsage, c.asn1.x509.Extension), c.asn1.x509.BasicConstraints = function(t) {
    c.asn1.x509.BasicConstraints.superclass.constructor.call(this, t);
    this.getExtnValueHex = function() {
        var t = new Array();
        this.cA && t.push(new c.asn1.DERBoolean()), this.pathLen > -1 && t.push(new c.asn1.DERInteger({
            int: this.pathLen
        }));
        var e = new c.asn1.DERSequence({
            array: t
        });
        return this.asn1ExtnValue = e, this.asn1ExtnValue.getEncodedHex();
    }, this.oid = "*********", this.cA = !1, this.pathLen = -1, void 0 !== t && (void 0 !== t.cA && (this.cA = t.cA), 
    void 0 !== t.pathLen && (this.pathLen = t.pathLen));
}, l.lang.extend(c.asn1.x509.BasicConstraints, c.asn1.x509.Extension), c.asn1.x509.CRLDistributionPoints = function(t) {
    c.asn1.x509.CRLDistributionPoints.superclass.constructor.call(this, t), this.getExtnValueHex = function() {
        return this.asn1ExtnValue.getEncodedHex();
    }, this.setByDPArray = function(t) {
        this.asn1ExtnValue = new c.asn1.DERSequence({
            array: t
        });
    }, this.setByOneURI = function(t) {
        var e = new c.asn1.x509.GeneralNames([ {
            uri: t
        } ]), i = new c.asn1.x509.DistributionPointName(e), n = new c.asn1.x509.DistributionPoint({
            dpobj: i
        });
        this.setByDPArray([ n ]);
    }, this.oid = "*********", void 0 !== t && (void 0 !== t.array ? this.setByDPArray(t.array) : void 0 !== t.uri && this.setByOneURI(t.uri));
}, l.lang.extend(c.asn1.x509.CRLDistributionPoints, c.asn1.x509.Extension), c.asn1.x509.ExtKeyUsage = function(t) {
    c.asn1.x509.ExtKeyUsage.superclass.constructor.call(this, t), this.setPurposeArray = function(t) {
        this.asn1ExtnValue = new c.asn1.DERSequence();
        for (var e = 0; e < t.length; e++) {
            var i = new c.asn1.DERObjectIdentifier(t[e]);
            this.asn1ExtnValue.appendASN1Object(i);
        }
    }, this.getExtnValueHex = function() {
        return this.asn1ExtnValue.getEncodedHex();
    }, this.oid = "*********", void 0 !== t && void 0 !== t.array && this.setPurposeArray(t.array);
}, l.lang.extend(c.asn1.x509.ExtKeyUsage, c.asn1.x509.Extension), c.asn1.x509.AuthorityKeyIdentifier = function(t) {
    c.asn1.x509.AuthorityKeyIdentifier.superclass.constructor.call(this, t), this.asn1KID = null, 
    this.asn1CertIssuer = null, this.asn1CertSN = null, this.getExtnValueHex = function() {
        var t = new Array();
        this.asn1KID && t.push(new c.asn1.DERTaggedObject({
            explicit: !1,
            tag: "80",
            obj: this.asn1KID
        })), this.asn1CertIssuer && t.push(new c.asn1.DERTaggedObject({
            explicit: !1,
            tag: "a1",
            obj: this.asn1CertIssuer
        })), this.asn1CertSN && t.push(new c.asn1.DERTaggedObject({
            explicit: !1,
            tag: "82",
            obj: this.asn1CertSN
        }));
        var e = new c.asn1.DERSequence({
            array: t
        });
        return this.asn1ExtnValue = e, this.asn1ExtnValue.getEncodedHex();
    }, this.setKIDByParam = function(t) {
        this.asn1KID = new c.asn1.DEROctetString(t);
    }, this.setCertIssuerByParam = function(t) {
        this.asn1CertIssuer = new c.asn1.x509.X500Name(t);
    }, this.setCertSNByParam = function(t) {
        this.asn1CertSN = new c.asn1.DERInteger(t);
    }, this.oid = "*********", void 0 !== t && (void 0 !== t.kid && this.setKIDByParam(t.kid), 
    void 0 !== t.issuer && this.setCertIssuerByParam(t.issuer), void 0 !== t.sn && this.setCertSNByParam(t.sn));
}, l.lang.extend(c.asn1.x509.AuthorityKeyIdentifier, c.asn1.x509.Extension), c.asn1.x509.AuthorityInfoAccess = function(t) {
    c.asn1.x509.AuthorityInfoAccess.superclass.constructor.call(this, t), this.setAccessDescriptionArray = function(t) {
        for (var e = new Array(), i = 0; i < t.length; i++) {
            var n = new c.asn1.DERObjectIdentifier(t[i].accessMethod), r = new c.asn1.x509.GeneralName(t[i].accessLocation), s = new c.asn1.DERSequence({
                array: [ n, r ]
            });
            e.push(s);
        }
        this.asn1ExtnValue = new c.asn1.DERSequence({
            array: e
        });
    }, this.getExtnValueHex = function() {
        return this.asn1ExtnValue.getEncodedHex();
    }, this.oid = "*******.*******.1", void 0 !== t && void 0 !== t.array && this.setAccessDescriptionArray(t.array);
}, l.lang.extend(c.asn1.x509.AuthorityInfoAccess, c.asn1.x509.Extension), c.asn1.x509.SubjectAltName = function(t) {
    c.asn1.x509.SubjectAltName.superclass.constructor.call(this, t), this.setNameArray = function(t) {
        this.asn1ExtnValue = new c.asn1.x509.GeneralNames(t);
    }, this.getExtnValueHex = function() {
        return this.asn1ExtnValue.getEncodedHex();
    }, this.oid = "*********", void 0 !== t && void 0 !== t.array && this.setNameArray(t.array);
}, l.lang.extend(c.asn1.x509.SubjectAltName, c.asn1.x509.Extension), c.asn1.x509.IssuerAltName = function(t) {
    c.asn1.x509.IssuerAltName.superclass.constructor.call(this, t), this.setNameArray = function(t) {
        this.asn1ExtnValue = new c.asn1.x509.GeneralNames(t);
    }, this.getExtnValueHex = function() {
        return this.asn1ExtnValue.getEncodedHex();
    }, this.oid = "*********", void 0 !== t && void 0 !== t.array && this.setNameArray(t.array);
}, l.lang.extend(c.asn1.x509.IssuerAltName, c.asn1.x509.Extension), c.asn1.x509.CRL = function(t) {
    c.asn1.x509.CRL.superclass.constructor.call(this);
    this.setRsaPrvKeyByPEMandPass = function(t, e) {
        var i = bt.getDecryptedKeyHex(t, e), n = new Q();
        n.readPrivateKeyFromASN1HexString(i), this.rsaPrvKey = n;
    }, this.sign = function() {
        this.asn1SignatureAlg = this.asn1TBSCertList.asn1SignatureAlg, sig = new c.crypto.Signature({
            alg: "SHA1withRSA",
            prov: "cryptojs/jsrsa"
        }), sig.initSign(this.rsaPrvKey), sig.updateHex(this.asn1TBSCertList.getEncodedHex()), 
        this.hexSig = sig.sign(), this.asn1Sig = new c.asn1.DERBitString({
            hex: "00" + this.hexSig
        });
        var t = new c.asn1.DERSequence({
            array: [ this.asn1TBSCertList, this.asn1SignatureAlg, this.asn1Sig ]
        });
        this.hTLV = t.getEncodedHex(), this.isModified = !1;
    }, this.getEncodedHex = function() {
        if (0 == this.isModified && null != this.hTLV) return this.hTLV;
        throw "not signed yet";
    }, this.getPEMString = function() {
        var t = this.getEncodedHex(), e = g.enc.Hex.parse(t);
        return "-----BEGIN X509 CRL-----\r\n" + g.enc.Base64.stringify(e).replace(/(.{64})/g, "$1\r\n") + "\r\n-----END X509 CRL-----\r\n";
    }, void 0 !== t && (void 0 !== t.tbsobj && (this.asn1TBSCertList = t.tbsobj), void 0 !== t.rsaprvkey && (this.rsaPrvKey = t.rsaprvkey), 
    void 0 !== t.rsaprvpem && void 0 !== t.rsaprvpas && this.setRsaPrvKeyByPEMandPass(t.rsaprvpem, t.rsaprvpas));
}, l.lang.extend(c.asn1.x509.CRL, c.asn1.ASN1Object), c.asn1.x509.TBSCertList = function(t) {
    c.asn1.x509.TBSCertList.superclass.constructor.call(this);
    this.setSignatureAlgByParam = function(t) {
        this.asn1SignatureAlg = new c.asn1.x509.AlgorithmIdentifier(t);
    }, this.setIssuerByParam = function(t) {
        this.asn1Issuer = new c.asn1.x509.X500Name(t);
    }, this.setThisUpdateByParam = function(t) {
        this.asn1ThisUpdate = new c.asn1.x509.Time(t);
    }, this.setNextUpdateByParam = function(t) {
        this.asn1NextUpdate = new c.asn1.x509.Time(t);
    }, this.addRevokedCert = function(t, e) {
        var i = {};
        null != t && null != t && (i.sn = t), null != e && null != e && (i.time = e);
        var n = new c.asn1.x509.CRLEntry(i);
        this.aRevokedCert.push(n);
    }, this.getEncodedHex = function() {
        if (this.asn1Array = new Array(), null != this.asn1Version && this.asn1Array.push(this.asn1Version), 
        this.asn1Array.push(this.asn1SignatureAlg), this.asn1Array.push(this.asn1Issuer), 
        this.asn1Array.push(this.asn1ThisUpdate), null != this.asn1NextUpdate && this.asn1Array.push(this.asn1NextUpdate), 
        this.aRevokedCert.length > 0) {
            var t = new c.asn1.DERSequence({
                array: this.aRevokedCert
            });
            this.asn1Array.push(t);
        }
        var e = new c.asn1.DERSequence({
            array: this.asn1Array
        });
        return this.hTLV = e.getEncodedHex(), this.isModified = !1, this.hTLV;
    }, this._initialize = function() {
        this.asn1Version = null, this.asn1SignatureAlg = null, this.asn1Issuer = null, this.asn1ThisUpdate = null, 
        this.asn1NextUpdate = null, this.aRevokedCert = new Array();
    }, this._initialize();
}, l.lang.extend(c.asn1.x509.TBSCertList, c.asn1.ASN1Object), c.asn1.x509.CRLEntry = function(t) {
    c.asn1.x509.CRLEntry.superclass.constructor.call(this);
    this.setCertSerial = function(t) {
        this.sn = new c.asn1.DERInteger(t);
    }, this.setRevocationDate = function(t) {
        this.time = new c.asn1.x509.Time(t);
    }, this.getEncodedHex = function() {
        var t = new c.asn1.DERSequence({
            array: [ this.sn, this.time ]
        });
        return this.TLV = t.getEncodedHex(), this.TLV;
    }, void 0 !== t && (void 0 !== t.time && this.setRevocationDate(t.time), void 0 !== t.sn && this.setCertSerial(t.sn));
}, l.lang.extend(c.asn1.x509.CRLEntry, c.asn1.ASN1Object), c.asn1.x509.X500Name = function(t) {
    if (c.asn1.x509.X500Name.superclass.constructor.call(this), this.asn1Array = new Array(), 
    this.setByString = function(t) {
        var e = t.split("/");
        e.shift();
        for (var i = 0; i < e.length; i++) this.asn1Array.push(new c.asn1.x509.RDN({
            str: e[i]
        }));
    }, this.setByLdapString = function(t) {
        var e = c.asn1.x509.X500Name.ldapToOneline(t);
        this.setByString(e);
    }, this.setByObject = function(t) {
        for (var e in t) if (t.hasOwnProperty(e)) {
            var i = new c.asn1.x509.RDN({
                str: e + "=" + t[e]
            });
            this.asn1Array ? this.asn1Array.push(i) : this.asn1Array = [ i ];
        }
    }, this.getEncodedHex = function() {
        if ("string" == typeof this.hTLV) return this.hTLV;
        var t = new c.asn1.DERSequence({
            array: this.asn1Array
        });
        return this.hTLV = t.getEncodedHex(), this.hTLV;
    }, void 0 !== t) {
        var e;
        if (void 0 !== t.str ? this.setByString(t.str) : void 0 !== t.ldapstr ? this.setByLdapString(t.ldapstr) : "object" === a(t) && this.setByObject(t), 
        void 0 !== t.certissuer) (e = new Nt()).hex = ot.pemToHex(t.certissuer), this.hTLV = e.getIssuerHex();
        if (void 0 !== t.certsubject) (e = new Nt()).hex = ot.pemToHex(t.certsubject), this.hTLV = e.getSubjectHex();
    }
}, l.lang.extend(c.asn1.x509.X500Name, c.asn1.ASN1Object), c.asn1.x509.X500Name.onelineToLDAP = function(t) {
    if ("/" !== t.substr(0, 1)) throw "malformed input";
    var e = (t = t.substr(1)).split("/");
    return e.reverse(), (e = e.map(function(t) {
        return t.replace(/,/, "\\,");
    })).join(",");
}, c.asn1.x509.X500Name.ldapToOneline = function(t) {
    for (var e = t.split(","), i = !1, n = [], r = 0; e.length > 0; r++) {
        var s = e.shift();
        if (!0 === i) {
            var a = (n.pop() + "," + s).replace(/\\,/g, ",");
            n.push(a), i = !1;
        } else n.push(s);
        "\\" === s.substr(-1, 1) && (i = !0);
    }
    return (n = n.map(function(t) {
        return t.replace("/", "\\/");
    })).reverse(), "/" + n.join("/");
}, c.asn1.x509.RDN = function(t) {
    c.asn1.x509.RDN.superclass.constructor.call(this), this.asn1Array = new Array(), 
    this.addByString = function(t) {
        this.asn1Array.push(new c.asn1.x509.AttributeTypeAndValue({
            str: t
        }));
    }, this.addByMultiValuedString = function(t) {
        for (var e = c.asn1.x509.RDN.parseString(t), i = 0; i < e.length; i++) this.addByString(e[i]);
    }, this.getEncodedHex = function() {
        var t = new c.asn1.DERSet({
            array: this.asn1Array
        });
        return this.TLV = t.getEncodedHex(), this.TLV;
    }, void 0 !== t && void 0 !== t.str && this.addByMultiValuedString(t.str);
}, l.lang.extend(c.asn1.x509.RDN, c.asn1.ASN1Object), c.asn1.x509.RDN.parseString = function(t) {
    for (var e = t.split(/\+/), i = !1, n = [], r = 0; e.length > 0; r++) {
        var s = e.shift();
        if (!0 === i) {
            var a = (n.pop() + "+" + s).replace(/\\\+/g, "+");
            n.push(a), i = !1;
        } else n.push(s);
        "\\" === s.substr(-1, 1) && (i = !0);
    }
    var o = !1, h = [];
    for (r = 0; n.length > 0; r++) {
        s = n.shift();
        if (!0 === o) {
            var u = h.pop();
            if (s.match(/"$/)) {
                a = (u + "+" + s).replace(/^([^=]+)="(.*)"$/, "$1=$2");
                h.push(a), o = !1;
            } else h.push(u + "+" + s);
        } else h.push(s);
        s.match(/^[^=]+="/) && (o = !0);
    }
    return h;
}, c.asn1.x509.AttributeTypeAndValue = function(t) {
    c.asn1.x509.AttributeTypeAndValue.superclass.constructor.call(this);
    this.setByString = function(t) {
        var e = t.match(/^([^=]+)=(.+)$/);
        if (!e) throw "malformed attrTypeAndValueStr: " + t;
        this.setByAttrTypeAndValueStr(e[1], e[2]);
    }, this.setByAttrTypeAndValueStr = function(t, e) {
        this.typeObj = c.asn1.x509.OID.atype2obj(t);
        var i = "utf8";
        "C" == t && (i = "prn"), this.valueObj = this.getValueObj(i, e);
    }, this.getValueObj = function(t, e) {
        if ("utf8" == t) return new c.asn1.DERUTF8String({
            str: e
        });
        if ("prn" == t) return new c.asn1.DERPrintableString({
            str: e
        });
        if ("tel" == t) return new c.asn1.DERTeletexString({
            str: e
        });
        if ("ia5" == t) return new c.asn1.DERIA5String({
            str: e
        });
        throw "unsupported directory string type: type=" + t + " value=" + e;
    }, this.getEncodedHex = function() {
        var t = new c.asn1.DERSequence({
            array: [ this.typeObj, this.valueObj ]
        });
        return this.TLV = t.getEncodedHex(), this.TLV;
    }, void 0 !== t && void 0 !== t.str && this.setByString(t.str);
}, l.lang.extend(c.asn1.x509.AttributeTypeAndValue, c.asn1.ASN1Object), c.asn1.x509.SubjectPublicKeyInfo = function(t) {
    c.asn1.x509.SubjectPublicKeyInfo.superclass.constructor.call(this);
    this.setRSAKey = function(t) {
        if (!Q.prototype.isPrototypeOf(t)) throw "argument is not RSAKey instance";
        this.rsaKey = t;
        var e = new c.asn1.DERInteger({
            bigint: t.n
        }), i = new c.asn1.DERInteger({
            int: t.e
        }), n = new c.asn1.DERSequence({
            array: [ e, i ]
        }).getEncodedHex();
        this.asn1AlgId = new c.asn1.x509.AlgorithmIdentifier({
            name: "rsaEncryption"
        }), this.asn1SubjPKey = new c.asn1.DERBitString({
            hex: "00" + n
        });
    }, this.setRSAPEM = function(t) {
        if (!t.match(/-----BEGIN PUBLIC KEY-----/)) throw "key not supported";
        var e = t, i = (e = (e = e.replace(/^-----[^-]+-----/, "")).replace(/-----[^-]+-----\s*$/, "")).replace(/\s+/g, ""), n = g.enc.Base64.parse(i), r = g.enc.Hex.stringify(n), s = Q.getHexValueArrayOfChildrenFromHex(r)[1].substr(2), a = Q.getHexValueArrayOfChildrenFromHex(s), o = new Q();
        o.setPublic(a[0], a[1]), this.setRSAKey(o);
    }, this.getASN1Object = function() {
        if (null == this.asn1AlgId || null == this.asn1SubjPKey) throw "algId and/or subjPubKey not set";
        return new c.asn1.DERSequence({
            array: [ this.asn1AlgId, this.asn1SubjPKey ]
        });
    }, this.getEncodedHex = function() {
        var t = this.getASN1Object();
        return this.hTLV = t.getEncodedHex(), this.hTLV;
    }, this._setRSAKey = function(t) {
        var e = c.asn1.ASN1Util.newObject({
            seq: [ {
                int: {
                    bigint: t.n
                }
            }, {
                int: {
                    int: t.e
                }
            } ]
        }).getEncodedHex();
        this.asn1AlgId = new c.asn1.x509.AlgorithmIdentifier({
            name: "rsaEncryption"
        }), this.asn1SubjPKey = new c.asn1.DERBitString({
            hex: "00" + e
        });
    }, this._setEC = function(t) {
        var e = new c.asn1.DERObjectIdentifier({
            name: t.curveName
        });
        this.asn1AlgId = new c.asn1.x509.AlgorithmIdentifier({
            name: "ecPublicKey",
            asn1params: e
        }), this.asn1SubjPKey = new c.asn1.DERBitString({
            hex: "00" + t.pubKeyHex
        });
    }, this._setDSA = function(t) {
        var e = new c.asn1.ASN1Util.newObject({
            seq: [ {
                int: {
                    bigint: t.p
                }
            }, {
                int: {
                    bigint: t.q
                }
            }, {
                int: {
                    bigint: t.g
                }
            } ]
        });
        this.asn1AlgId = new c.asn1.x509.AlgorithmIdentifier({
            name: "dsa",
            asn1params: e
        });
        var i = new c.asn1.DERInteger({
            bigint: t.y
        });
        this.asn1SubjPKey = new c.asn1.DERBitString({
            hex: "00" + i.getEncodedHex()
        });
    }, void 0 !== t && (t instanceof Q ? this._setRSAKey(t) : void 0 !== c.crypto.ECDSA && t instanceof c.crypto.ECDSA ? this._setEC(t) : void 0 !== c.crypto.DSA && t instanceof c.crypto.DSA ? this._setDSA(t) : void 0 !== t.rsakey ? this.setRSAKey(t.rsakey) : void 0 !== t.rsapem && this.setRSAPEM(t.rsapem));
}, l.lang.extend(c.asn1.x509.SubjectPublicKeyInfo, c.asn1.ASN1Object), c.asn1.x509.Time = function(t) {
    c.asn1.x509.Time.superclass.constructor.call(this);
    this.setTimeParams = function(t) {
        this.timeParams = t;
    }, this.getEncodedHex = function() {
        var t = null;
        return t = null != this.timeParams ? "utc" == this.type ? new c.asn1.DERUTCTime(this.timeParams) : new c.asn1.DERGeneralizedTime(this.timeParams) : "utc" == this.type ? new c.asn1.DERUTCTime() : new c.asn1.DERGeneralizedTime(), 
        this.TLV = t.getEncodedHex(), this.TLV;
    }, this.type = "utc", void 0 !== t && (void 0 !== t.type ? this.type = t.type : void 0 !== t.str && (t.str.match(/^[0-9]{12}Z$/) && (this.type = "utc"), 
    t.str.match(/^[0-9]{14}Z$/) && (this.type = "gen")), this.timeParams = t);
}, l.lang.extend(c.asn1.x509.Time, c.asn1.ASN1Object), c.asn1.x509.AlgorithmIdentifier = function(t) {
    if (c.asn1.x509.AlgorithmIdentifier.superclass.constructor.call(this), this.nameAlg = null, 
    this.asn1Alg = null, this.asn1Params = null, this.paramEmpty = !1, this.getEncodedHex = function() {
        if (null === this.nameAlg && null === this.asn1Alg) throw "algorithm not specified";
        null !== this.nameAlg && null === this.asn1Alg && (this.asn1Alg = c.asn1.x509.OID.name2obj(this.nameAlg));
        var t = [ this.asn1Alg ];
        null !== this.asn1Params && t.push(this.asn1Params);
        var e = new c.asn1.DERSequence({
            array: t
        });
        return this.hTLV = e.getEncodedHex(), this.hTLV;
    }, void 0 !== t && (void 0 !== t.name && (this.nameAlg = t.name), void 0 !== t.asn1params && (this.asn1Params = t.asn1params), 
    void 0 !== t.paramempty && (this.paramEmpty = t.paramempty)), null === this.asn1Params && !1 === this.paramEmpty && null !== this.nameAlg) {
        var e = this.nameAlg.toLowerCase();
        "withdsa" !== e.substr(-7, 7) && "withecdsa" !== e.substr(-9, 9) && (this.asn1Params = new c.asn1.DERNull());
    }
}, l.lang.extend(c.asn1.x509.AlgorithmIdentifier, c.asn1.ASN1Object), c.asn1.x509.GeneralName = function(t) {
    c.asn1.x509.GeneralName.superclass.constructor.call(this);
    var e = {
        rfc822: "81",
        dns: "82",
        dn: "a4",
        uri: "86"
    };
    this.explicit = !1, this.setByParam = function(t) {
        var i = null;
        if (void 0 !== t) {
            if (void 0 !== t.rfc822 && (this.type = "rfc822", i = new c.asn1.DERIA5String({
                str: t[this.type]
            })), void 0 !== t.dns && (this.type = "dns", i = new c.asn1.DERIA5String({
                str: t[this.type]
            })), void 0 !== t.uri && (this.type = "uri", i = new c.asn1.DERIA5String({
                str: t[this.type]
            })), void 0 !== t.dn && (this.type = "dn", i = new c.asn1.x509.X500Name({
                str: t.dn
            })), void 0 !== t.ldapdn && (this.type = "dn", i = new c.asn1.x509.X500Name({
                ldapstr: t.ldapdn
            })), void 0 !== t.certissuer) {
                this.type = "dn", this.explicit = !0;
                var n = null;
                if ((s = t.certissuer).match(/^[0-9A-Fa-f]+$/), -1 != s.indexOf("-----BEGIN ") && (n = ot.pemToHex(s)), 
                null == n) throw "certissuer param not cert";
                (a = new Nt()).hex = n;
                var r = a.getIssuerHex();
                (i = new c.asn1.ASN1Object()).hTLV = r;
            }
            if (void 0 !== t.certsubj) {
                this.type = "dn", this.explicit = !0;
                var s, a;
                n = null;
                if ((s = t.certsubj).match(/^[0-9A-Fa-f]+$/), -1 != s.indexOf("-----BEGIN ") && (n = ot.pemToHex(s)), 
                null == n) throw "certsubj param not cert";
                (a = new Nt()).hex = n;
                r = a.getSubjectHex();
                (i = new c.asn1.ASN1Object()).hTLV = r;
            }
            if (null == this.type) throw "unsupported type in params=" + t;
            this.asn1Obj = new c.asn1.DERTaggedObject({
                explicit: this.explicit,
                tag: e[this.type],
                obj: i
            });
        }
    }, this.getEncodedHex = function() {
        return this.asn1Obj.getEncodedHex();
    }, void 0 !== t && this.setByParam(t);
}, l.lang.extend(c.asn1.x509.GeneralName, c.asn1.ASN1Object), c.asn1.x509.GeneralNames = function(t) {
    c.asn1.x509.GeneralNames.superclass.constructor.call(this);
    this.setByParamArray = function(t) {
        for (var e = 0; e < t.length; e++) {
            var i = new c.asn1.x509.GeneralName(t[e]);
            this.asn1Array.push(i);
        }
    }, this.getEncodedHex = function() {
        return new c.asn1.DERSequence({
            array: this.asn1Array
        }).getEncodedHex();
    }, this.asn1Array = new Array(), void 0 !== t && this.setByParamArray(t);
}, l.lang.extend(c.asn1.x509.GeneralNames, c.asn1.ASN1Object), c.asn1.x509.DistributionPointName = function(t) {
    c.asn1.x509.DistributionPointName.superclass.constructor.call(this);
    if (this.getEncodedHex = function() {
        if ("full" != this.type) throw "currently type shall be 'full': " + this.type;
        return this.asn1Obj = new c.asn1.DERTaggedObject({
            explicit: !1,
            tag: this.tag,
            obj: this.asn1V
        }), this.hTLV = this.asn1Obj.getEncodedHex(), this.hTLV;
    }, void 0 !== t) {
        if (!c.asn1.x509.GeneralNames.prototype.isPrototypeOf(t)) throw "This class supports GeneralNames only as argument";
        this.type = "full", this.tag = "a0", this.asn1V = t;
    }
}, l.lang.extend(c.asn1.x509.DistributionPointName, c.asn1.ASN1Object), c.asn1.x509.DistributionPoint = function(t) {
    c.asn1.x509.DistributionPoint.superclass.constructor.call(this);
    this.getEncodedHex = function() {
        var t = new c.asn1.DERSequence();
        if (null != this.asn1DP) {
            var e = new c.asn1.DERTaggedObject({
                explicit: !0,
                tag: "a0",
                obj: this.asn1DP
            });
            t.appendASN1Object(e);
        }
        return this.hTLV = t.getEncodedHex(), this.hTLV;
    }, void 0 !== t && void 0 !== t.dpobj && (this.asn1DP = t.dpobj);
}, l.lang.extend(c.asn1.x509.DistributionPoint, c.asn1.ASN1Object), c.asn1.x509.OID = new function(t) {
    this.atype2oidList = {
        CN: "*******",
        L: "*******",
        ST: "*******",
        O: "********",
        OU: "********",
        C: "*******",
        STREET: "*******",
        DC: "0.9.2342.19200300.100.1.25",
        UID: "0.9.2342.19200300.100.1.1",
        SN: "*******",
        DN: "*******9",
        E: "1.2.840.113549.1.9.1",
        businessCategory: "********",
        postalCode: "********",
        jurisdictionOfIncorporationL: "*******.4.1.311.********",
        jurisdictionOfIncorporationSP: "*******.4.1.311.********",
        jurisdictionOfIncorporationC: "*******.4.1.311.********"
    }, this.name2oidList = {
        sha1: "********.2.26",
        sha256: "2.16.840.*********.2.1",
        sha384: "2.16.840.*********.2.2",
        sha512: "2.16.840.*********.2.3",
        sha224: "2.16.840.*********.2.4",
        md5: "1.2.840.113549.2.5",
        md2: "********.2.2.1",
        ripemd160: "********.2.1",
        MD2withRSA: "1.2.840.113549.1.1.2",
        MD4withRSA: "1.2.840.113549.1.1.3",
        MD5withRSA: "1.2.840.113549.1.1.4",
        SHA1withRSA: "1.2.840.113549.1.1.5",
        SHA224withRSA: "1.2.840.113549.1.1.14",
        SHA256withRSA: "1.2.840.113549.1.1.11",
        SHA384withRSA: "1.2.840.113549.1.1.12",
        SHA512withRSA: "1.2.840.113549.1.1.13",
        SHA1withECDSA: "1.2.840.10045.4.1",
        SHA224withECDSA: "1.2.840.10045.4.3.1",
        SHA256withECDSA: "1.2.840.10045.4.3.2",
        SHA384withECDSA: "1.2.840.10045.4.3.3",
        SHA512withECDSA: "1.2.840.10045.4.3.4",
        dsa: "1.2.840.10040.4.1",
        SHA1withDSA: "1.2.840.10040.4.3",
        SHA224withDSA: "2.16.840.*********.3.1",
        SHA256withDSA: "2.16.840.*********.3.2",
        rsaEncryption: "1.2.840.113549.1.1.1",
        commonName: "*******",
        localityName: "*******",
        stateOrProvinceName: "*******",
        organizationName: "********",
        organizationalUnitName: "********",
        countryName: "*******",
        streetAddress: "*******",
        domainComponent: "0.9.2342.19200300.100.1.25",
        userId: "0.9.2342.19200300.100.1.1",
        surname: "*******",
        distinguishedName: "*******9",
        emailAddress: "1.2.840.113549.1.9.1",
        businessCategory: "********",
        postalCode: "********",
        jurisdictionOfIncorporationL: "*******.4.1.311.********",
        jurisdictionOfIncorporationSP: "*******.4.1.311.********",
        jurisdictionOfIncorporationC: "*******.4.1.311.********",
        subjectKeyIdentifier: "*********",
        keyUsage: "*********",
        subjectAltName: "*********",
        issuerAltName: "*********",
        basicConstraints: "*********",
        nameConstraints: "*********",
        cRLDistributionPoints: "*********",
        certificatePolicies: "*********",
        authorityKeyIdentifier: "*********",
        policyConstraints: "*********",
        extKeyUsage: "*********",
        authorityInfoAccess: "*******.*******.1",
        ocsp: "*******.********.1",
        caIssuers: "*******.********.2",
        anyExtendedKeyUsage: "*********.0",
        serverAuth: "*******.*******.1",
        clientAuth: "*******.*******.2",
        codeSigning: "*******.*******.3",
        emailProtection: "*******.*******.4",
        timeStamping: "*******.*******.8",
        ocspSigning: "*******.*******.9",
        ecPublicKey: "1.2.840.10045.2.1",
        secp256r1: "1.2.840.10045.3.1.7",
        secp256k1: "1.3.132.0.10",
        secp384r1: "1.3.132.0.34",
        pkcs5PBES2: "1.2.840.113549.1.5.13",
        pkcs5PBKDF2: "1.2.840.113549.1.5.12",
        "des-EDE3-CBC": "1.2.840.113549.3.7",
        data: "1.2.840.113549.1.7.1",
        "signed-data": "1.2.840.113549.1.7.2",
        "enveloped-data": "1.2.840.113549.1.7.3",
        "digested-data": "1.2.840.113549.1.7.5",
        "encrypted-data": "1.2.840.113549.1.7.6",
        "authenticated-data": "1.2.840.113549.1.9.16.1.2",
        tstinfo: "1.2.840.113549.1.9.16.1.4",
        extensionRequest: "1.2.840.113549.1.9.14"
    }, this.objCache = {}, this.name2obj = function(t) {
        if (void 0 !== this.objCache[t]) return this.objCache[t];
        if (void 0 === this.name2oidList[t]) throw "Name of ObjectIdentifier not defined: " + t;
        var e = this.name2oidList[t], i = new c.asn1.DERObjectIdentifier({
            oid: e
        });
        return this.objCache[t] = i, i;
    }, this.atype2obj = function(t) {
        if (void 0 !== this.objCache[t]) return this.objCache[t];
        if (void 0 === this.atype2oidList[t]) throw "AttributeType name undefined: " + t;
        var e = this.atype2oidList[t], i = new c.asn1.DERObjectIdentifier({
            oid: e
        });
        return this.objCache[t] = i, i;
    };
}(), c.asn1.x509.OID.oid2name = function(t) {
    var e = c.asn1.x509.OID.name2oidList;
    for (var i in e) if (e[i] == t) return i;
    return "";
}, c.asn1.x509.OID.oid2atype = function(t) {
    var e = c.asn1.x509.OID.atype2oidList;
    for (var i in e) if (e[i] == t) return i;
    return t;
}, c.asn1.x509.OID.name2oid = function(t) {
    var e = c.asn1.x509.OID.name2oidList;
    return void 0 === e[t] ? "" : e[t];
}, c.asn1.x509.X509Util = new function() {
    this.getPKCS8PubKeyPEMfromRSAKey = function(t) {
        var e = c.asn1.ASN1Util.bigIntToMinTwosComplementsHex(t.n), i = c.asn1.ASN1Util.integerToByteHex(t.e), n = new c.asn1.DERInteger({
            hex: e
        }), r = new c.asn1.DERInteger({
            hex: i
        }), s = new c.asn1.DERSequence({
            array: [ n, r ]
        }).getEncodedHex(), a = new c.asn1.x509.AlgorithmIdentifier({
            name: "rsaEncryption"
        }), o = new c.asn1.DERBitString({
            hex: "00" + s
        }), h = new c.asn1.DERSequence({
            array: [ a, o ]
        }).getEncodedHex();
        return c.asn1.ASN1Util.getPEMStringFromHex(h, "PUBLIC KEY");
    };
}(), c.asn1.x509.X509Util.newCertPEM = function(t) {
    var e = c.asn1.x509, i = new e.TBSCertificate();
    if (void 0 === t.serial) throw "serial number undefined.";
    if (i.setSerialNumberByParam(t.serial), "string" != typeof t.sigalg.name) throw "unproper signature algorithm name";
    if (i.setSignatureAlgByParam(t.sigalg), void 0 === t.issuer) throw "issuer name undefined.";
    if (i.setIssuerByParam(t.issuer), void 0 === t.notbefore) throw "notbefore undefined.";
    if (i.setNotBeforeByParam(t.notbefore), void 0 === t.notafter) throw "notafter undefined.";
    if (i.setNotAfterByParam(t.notafter), void 0 === t.subject) throw "subject name undefined.";
    if (i.setSubjectByParam(t.subject), void 0 === t.sbjpubkey) throw "subject public key undefined.";
    if (i.setSubjectPublicKeyByGetKey(t.sbjpubkey), void 0 !== t.ext && void 0 !== t.ext.length) for (var n = 0; n < t.ext.length; n++) for (key in t.ext[n]) i.appendExtensionByName(key, t.ext[n][key]);
    if (void 0 === t.cakey && void 0 === t.sighex) throw "param cakey and sighex undefined.";
    var r = null, s = null;
    return t.cakey && (r = !0 === t.cakey.isPrivate ? t.cakey : Ft.getKey.apply(null, t.cakey), 
    (s = new e.Certificate({
        tbscertobj: i,
        prvkeyobj: r
    })).sign()), t.sighex && (s = new e.Certificate({
        tbscertobj: i
    })).setSignatureHex(t.sighex), s.getPEMString()
    /*! asn1cms-1.0.3.js (c) 2013-2017 Kenji Urushima | kjur.github.com/jsrsasign/license
 */;
}, void 0 !== c && c || (c = {}), void 0 !== c.asn1 && c.asn1 || (c.asn1 = {}), 
void 0 !== c.asn1.cms && c.asn1.cms || (c.asn1.cms = {}), c.asn1.cms.Attribute = function(t) {
    c.asn1.cms.Attribute.superclass.constructor.call(this);
    this.getEncodedHex = function() {
        var t, e, i;
        t = new c.asn1.DERObjectIdentifier({
            oid: this.attrTypeOid
        }), e = new c.asn1.DERSet({
            array: this.valueList
        });
        try {
            e.getEncodedHex();
        } catch (t) {
            t = VM2_INTERNAL_STATE_DO_NOT_USE_OR_PROGRAM_WILL_FAIL.handleException(t);
            throw "fail valueSet.getEncodedHex in Attribute(1)/" + t;
        }
        i = new c.asn1.DERSequence({
            array: [ t, e ]
        });
        try {
            this.hTLV = i.getEncodedHex();
        } catch (t) {
            t = VM2_INTERNAL_STATE_DO_NOT_USE_OR_PROGRAM_WILL_FAIL.handleException(t);
            throw "failed seq.getEncodedHex in Attribute(2)/" + t;
        }
        return this.hTLV;
    };
}, l.lang.extend(c.asn1.cms.Attribute, c.asn1.ASN1Object), c.asn1.cms.ContentType = function(t) {
    c.asn1.cms.ContentType.superclass.constructor.call(this), this.attrTypeOid = "1.2.840.113549.1.9.3";
    var e = null;
    if (void 0 !== t) {
        e = new c.asn1.DERObjectIdentifier(t);
        this.valueList = [ e ];
    }
}, l.lang.extend(c.asn1.cms.ContentType, c.asn1.cms.Attribute), c.asn1.cms.MessageDigest = function(t) {
    if (c.asn1.cms.MessageDigest.superclass.constructor.call(this), this.attrTypeOid = "1.2.840.113549.1.9.4", 
    void 0 !== t) if (t.eciObj instanceof c.asn1.cms.EncapsulatedContentInfo && "string" == typeof t.hashAlg) {
        var e = t.eciObj.eContentValueHex, i = t.hashAlg, n = c.crypto.Util.hashHex(e, i);
        (r = new c.asn1.DEROctetString({
            hex: n
        })).getEncodedHex(), this.valueList = [ r ];
    } else {
        var r;
        (r = new c.asn1.DEROctetString(t)).getEncodedHex(), this.valueList = [ r ];
    }
}, l.lang.extend(c.asn1.cms.MessageDigest, c.asn1.cms.Attribute), c.asn1.cms.SigningTime = function(t) {
    if (c.asn1.cms.SigningTime.superclass.constructor.call(this), this.attrTypeOid = "1.2.840.113549.1.9.5", 
    void 0 !== t) {
        var e = new c.asn1.x509.Time(t);
        try {
            e.getEncodedHex();
        } catch (t) {
            t = VM2_INTERNAL_STATE_DO_NOT_USE_OR_PROGRAM_WILL_FAIL.handleException(t);
            throw "SigningTime.getEncodedHex() failed/" + t;
        }
        this.valueList = [ e ];
    }
}, l.lang.extend(c.asn1.cms.SigningTime, c.asn1.cms.Attribute), c.asn1.cms.SigningCertificate = function(t) {
    c.asn1.cms.SigningCertificate.superclass.constructor.call(this), this.attrTypeOid = "1.2.840.113549.1.9.16.2.12";
    var e = c.asn1, i = c.asn1.cms, n = c.crypto;
    this.setCerts = function(t) {
        for (var r = [], s = 0; s < t.length; s++) {
            var a = ot.pemToHex(t[s]), o = n.Util.hashHex(a, "sha1"), h = new e.DEROctetString({
                hex: o
            });
            h.getEncodedHex();
            var u = new i.IssuerAndSerialNumber({
                cert: t[s]
            });
            u.getEncodedHex();
            var c = new e.DERSequence({
                array: [ h, u ]
            });
            c.getEncodedHex(), r.push(c);
        }
        var l = new e.DERSequence({
            array: r
        });
        l.getEncodedHex(), this.valueList = [ l ];
    }, void 0 !== t && "object" == a(t.array) && this.setCerts(t.array);
}, l.lang.extend(c.asn1.cms.SigningCertificate, c.asn1.cms.Attribute), c.asn1.cms.SigningCertificateV2 = function(t) {
    c.asn1.cms.SigningCertificateV2.superclass.constructor.call(this), this.attrTypeOid = "1.2.840.113549.1.9.16.2.47";
    var e = c.asn1, i = c.asn1.x509, n = c.asn1.cms, r = c.crypto;
    if (this.setCerts = function(t, s) {
        for (var a = [], o = 0; o < t.length; o++) {
            var h = ot.pemToHex(t[o]), u = [];
            "sha256" != s && u.push(new i.AlgorithmIdentifier({
                name: s
            }));
            var c = r.Util.hashHex(h, s), l = new e.DEROctetString({
                hex: c
            });
            l.getEncodedHex(), u.push(l);
            var f = new n.IssuerAndSerialNumber({
                cert: t[o]
            });
            f.getEncodedHex(), u.push(f);
            var d = new e.DERSequence({
                array: u
            });
            d.getEncodedHex(), a.push(d);
        }
        var g = new e.DERSequence({
            array: a
        });
        g.getEncodedHex(), this.valueList = [ g ];
    }, void 0 !== t && "object" == a(t.array)) {
        var s = "sha256";
        "string" == typeof t.hashAlg && (s = t.hashAlg), this.setCerts(t.array, s);
    }
}, l.lang.extend(c.asn1.cms.SigningCertificateV2, c.asn1.cms.Attribute), c.asn1.cms.IssuerAndSerialNumber = function(t) {
    c.asn1.cms.IssuerAndSerialNumber.superclass.constructor.call(this);
    var e = c.asn1, i = e.x509;
    this.setByCertPEM = function(t) {
        var n = ot.pemToHex(t), r = new Nt();
        r.hex = n;
        var s = r.getIssuerHex();
        this.dIssuer = new i.X500Name(), this.dIssuer.hTLV = s;
        var a = r.getSerialNumberHex();
        this.dSerial = new e.DERInteger({
            hex: a
        });
    }, this.getEncodedHex = function() {
        var t = new c.asn1.DERSequence({
            array: [ this.dIssuer, this.dSerial ]
        });
        return this.hTLV = t.getEncodedHex(), this.hTLV;
    }, void 0 !== t && ("string" == typeof t && -1 != t.indexOf("-----BEGIN ") && this.setByCertPEM(t), 
    t.issuer && t.serial && (t.issuer instanceof c.asn1.x509.X500Name ? this.dIssuer = t.issuer : this.dIssuer = new c.asn1.x509.X500Name(t.issuer), 
    t.serial instanceof c.asn1.DERInteger ? this.dSerial = t.serial : this.dSerial = new c.asn1.DERInteger(t.serial)), 
    "string" == typeof t.cert && this.setByCertPEM(t.cert));
}, l.lang.extend(c.asn1.cms.IssuerAndSerialNumber, c.asn1.ASN1Object), c.asn1.cms.AttributeList = function(t) {
    c.asn1.cms.AttributeList.superclass.constructor.call(this), this.list = new Array(), 
    this.sortFlag = !0, this.add = function(t) {
        t instanceof c.asn1.cms.Attribute && this.list.push(t);
    }, this.length = function() {
        return this.list.length;
    }, this.clear = function() {
        this.list = new Array(), this.hTLV = null, this.hV = null;
    }, this.getEncodedHex = function() {
        if ("string" == typeof this.hTLV) return this.hTLV;
        var t = new c.asn1.DERSet({
            array: this.list,
            sortflag: this.sortFlag
        });
        return this.hTLV = t.getEncodedHex(), this.hTLV;
    }, void 0 !== t && void 0 !== t.sortflag && 0 == t.sortflag && (this.sortFlag = !1);
}, l.lang.extend(c.asn1.cms.AttributeList, c.asn1.ASN1Object), c.asn1.cms.SignerInfo = function(t) {
    c.asn1.cms.SignerInfo.superclass.constructor.call(this);
    var e = c.asn1, i = c.asn1.cms, n = c.asn1.x509;
    this.dCMSVersion = new e.DERInteger({
        int: 1
    }), this.dSignerIdentifier = null, this.dDigestAlgorithm = null, this.dSignedAttrs = new i.AttributeList(), 
    this.dSigAlg = null, this.dSig = null, this.dUnsignedAttrs = new i.AttributeList(), 
    this.setSignerIdentifier = function(t) {
        if ("string" == typeof t && -1 != t.indexOf("CERTIFICATE") && -1 != t.indexOf("BEGIN") && -1 != t.indexOf("END")) {
            this.dSignerIdentifier = new i.IssuerAndSerialNumber({
                cert: t
            });
        }
    }, this.setForContentAndHash = function(t) {
        void 0 !== t && (t.eciObj instanceof c.asn1.cms.EncapsulatedContentInfo && (this.dSignedAttrs.add(new i.ContentType({
            oid: "1.2.840.113549.1.7.1"
        })), this.dSignedAttrs.add(new i.MessageDigest({
            eciObj: t.eciObj,
            hashAlg: t.hashAlg
        }))), void 0 !== t.sdObj && t.sdObj instanceof c.asn1.cms.SignedData && -1 == t.sdObj.digestAlgNameList.join(":").indexOf(t.hashAlg) && t.sdObj.digestAlgNameList.push(t.hashAlg), 
        "string" == typeof t.hashAlg && (this.dDigestAlgorithm = new n.AlgorithmIdentifier({
            name: t.hashAlg
        })));
    }, this.sign = function(t, i) {
        this.dSigAlg = new n.AlgorithmIdentifier({
            name: i
        });
        var r = this.dSignedAttrs.getEncodedHex(), s = Ft.getKey(t), a = new c.crypto.Signature({
            alg: i
        });
        a.init(s), a.updateHex(r);
        var o = a.sign();
        this.dSig = new e.DEROctetString({
            hex: o
        });
    }, this.addUnsigned = function(t) {
        this.hTLV = null, this.dUnsignedAttrs.hTLV = null, this.dUnsignedAttrs.add(t);
    }, this.getEncodedHex = function() {
        if (this.dSignedAttrs instanceof c.asn1.cms.AttributeList && 0 == this.dSignedAttrs.length()) throw "SignedAttrs length = 0 (empty)";
        var t = new e.DERTaggedObject({
            obj: this.dSignedAttrs,
            tag: "a0",
            explicit: !1
        }), i = null;
        this.dUnsignedAttrs.length() > 0 && (i = new e.DERTaggedObject({
            obj: this.dUnsignedAttrs,
            tag: "a1",
            explicit: !1
        }));
        var n = [ this.dCMSVersion, this.dSignerIdentifier, this.dDigestAlgorithm, t, this.dSigAlg, this.dSig ];
        null != i && n.push(i);
        var r = new e.DERSequence({
            array: n
        });
        return this.hTLV = r.getEncodedHex(), this.hTLV;
    };
}, l.lang.extend(c.asn1.cms.SignerInfo, c.asn1.ASN1Object), c.asn1.cms.EncapsulatedContentInfo = function(t) {
    c.asn1.cms.EncapsulatedContentInfo.superclass.constructor.call(this);
    var e = c.asn1;
    c.asn1.cms, c.asn1.x509;
    this.dEContentType = new e.DERObjectIdentifier({
        name: "data"
    }), this.dEContent = null, this.isDetached = !1, this.eContentValueHex = null, this.setContentType = function(t) {
        t.match(/^[0-2][.][0-9.]+$/) ? this.dEContentType = new e.DERObjectIdentifier({
            oid: t
        }) : this.dEContentType = new e.DERObjectIdentifier({
            name: t
        });
    }, this.setContentValue = function(t) {
        void 0 !== t && ("string" == typeof t.hex ? this.eContentValueHex = t.hex : "string" == typeof t.str && (this.eContentValueHex = gt(t.str)));
    }, this.setContentValueHex = function(t) {
        this.eContentValueHex = t;
    }, this.setContentValueStr = function(t) {
        this.eContentValueHex = gt(t);
    }, this.getEncodedHex = function() {
        if ("string" != typeof this.eContentValueHex) throw "eContentValue not yet set";
        var t = new e.DEROctetString({
            hex: this.eContentValueHex
        });
        this.dEContent = new e.DERTaggedObject({
            obj: t,
            tag: "a0",
            explicit: !0
        });
        var i = [ this.dEContentType ];
        this.isDetached || i.push(this.dEContent);
        var n = new e.DERSequence({
            array: i
        });
        return this.hTLV = n.getEncodedHex(), this.hTLV;
    };
}, l.lang.extend(c.asn1.cms.EncapsulatedContentInfo, c.asn1.ASN1Object), c.asn1.cms.ContentInfo = function(t) {
    c.asn1.cms.ContentInfo.superclass.constructor.call(this);
    var e = c.asn1, i = (c.asn1.cms, c.asn1.x509);
    this.dContentType = null, this.dContent = null, this.setContentType = function(t) {
        "string" == typeof t && (this.dContentType = i.OID.name2obj(t));
    }, this.getEncodedHex = function() {
        var t = new e.DERTaggedObject({
            obj: this.dContent,
            tag: "a0",
            explicit: !0
        }), i = new e.DERSequence({
            array: [ this.dContentType, t ]
        });
        return this.hTLV = i.getEncodedHex(), this.hTLV;
    }, void 0 !== t && (t.type && this.setContentType(t.type), t.obj && t.obj instanceof e.ASN1Object && (this.dContent = t.obj));
}, l.lang.extend(c.asn1.cms.ContentInfo, c.asn1.ASN1Object), c.asn1.cms.SignedData = function(t) {
    c.asn1.cms.SignedData.superclass.constructor.call(this);
    var e = c.asn1, i = c.asn1.cms, n = c.asn1.x509;
    this.dCMSVersion = new e.DERInteger({
        int: 1
    }), this.dDigestAlgs = null, this.digestAlgNameList = [], this.dEncapContentInfo = new i.EncapsulatedContentInfo(), 
    this.dCerts = null, this.certificateList = [], this.crlList = [], this.signerInfoList = [ new i.SignerInfo() ], 
    this.addCertificatesByPEM = function(t) {
        var i = ot.pemToHex(t), n = new e.ASN1Object();
        n.hTLV = i, this.certificateList.push(n);
    }, this.getEncodedHex = function() {
        if ("string" == typeof this.hTLV) return this.hTLV;
        if (null == this.dDigestAlgs) {
            for (var t = [], i = 0; i < this.digestAlgNameList.length; i++) {
                var r = this.digestAlgNameList[i], s = new n.AlgorithmIdentifier({
                    name: r
                });
                t.push(s);
            }
            this.dDigestAlgs = new e.DERSet({
                array: t
            });
        }
        var a = [ this.dCMSVersion, this.dDigestAlgs, this.dEncapContentInfo ];
        if (null == this.dCerts && this.certificateList.length > 0) {
            var o = new e.DERSet({
                array: this.certificateList
            });
            this.dCerts = new e.DERTaggedObject({
                obj: o,
                tag: "a0",
                explicit: !1
            });
        }
        null != this.dCerts && a.push(this.dCerts);
        var h = new e.DERSet({
            array: this.signerInfoList
        });
        a.push(h);
        var u = new e.DERSequence({
            array: a
        });
        return this.hTLV = u.getEncodedHex(), this.hTLV;
    }, this.getContentInfo = function() {
        return this.getEncodedHex(), new i.ContentInfo({
            type: "signed-data",
            obj: this
        });
    }, this.getContentInfoEncodedHex = function() {
        return this.getContentInfo().getEncodedHex();
    }, this.getPEM = function() {
        var t = this.getContentInfoEncodedHex();
        return e.ASN1Util.getPEMStringFromHex(t, "CMS");
    };
}, l.lang.extend(c.asn1.cms.SignedData, c.asn1.ASN1Object), c.asn1.cms.CMSUtil = new function() {}(), 
c.asn1.cms.CMSUtil.newSignedData = function(t) {
    var e = c.asn1.cms, i = c.asn1.cades, n = new e.SignedData();
    if (n.dEncapContentInfo.setContentValue(t.content), "object" == a(t.certs)) for (var r = 0; r < t.certs.length; r++) n.addCertificatesByPEM(t.certs[r]);
    n.signerInfoList = [];
    for (r = 0; r < t.signerInfos.length; r++) {
        var s = t.signerInfos[r], o = new e.SignerInfo();
        for (attrName in o.setSignerIdentifier(s.signerCert), o.setForContentAndHash({
            sdObj: n,
            eciObj: n.dEncapContentInfo,
            hashAlg: s.hashAlg
        }), s.sAttr) {
            var h = s.sAttr[attrName];
            if ("SigningTime" == attrName) {
                var u = new e.SigningTime(h);
                o.dSignedAttrs.add(u);
            }
            if ("SigningCertificate" == attrName) {
                u = new e.SigningCertificate(h);
                o.dSignedAttrs.add(u);
            }
            if ("SigningCertificateV2" == attrName) {
                u = new e.SigningCertificateV2(h);
                o.dSignedAttrs.add(u);
            }
            if ("SignaturePolicyIdentifier" == attrName) {
                u = new i.SignaturePolicyIdentifier(h);
                o.dSignedAttrs.add(u);
            }
        }
        o.sign(s.signerPrvKey, s.sigAlg), n.signerInfoList.push(o);
    }
    return n;
}, 
/*! asn1tsp-1.0.1.js (c) 2014 Kenji Urushima | kjur.github.com/jsrsasign/license
 */
void 0 !== c && c || (c = {}), void 0 !== c.asn1 && c.asn1 || (c.asn1 = {}), void 0 !== c.asn1.tsp && c.asn1.tsp || (c.asn1.tsp = {}), 
c.asn1.tsp.Accuracy = function(t) {
    c.asn1.tsp.Accuracy.superclass.constructor.call(this);
    var e = c.asn1;
    this.seconds = null, this.millis = null, this.micros = null, this.getEncodedHex = function() {
        var t = null, i = null, n = null, r = [];
        if (null != this.seconds && (t = new e.DERInteger({
            int: this.seconds
        }), r.push(t)), null != this.millis) {
            var s = new e.DERInteger({
                int: this.millis
            });
            i = new e.DERTaggedObject({
                obj: s,
                tag: "80",
                explicit: !1
            }), r.push(i);
        }
        if (null != this.micros) {
            var a = new e.DERInteger({
                int: this.micros
            });
            n = new e.DERTaggedObject({
                obj: a,
                tag: "81",
                explicit: !1
            }), r.push(n);
        }
        var o = new e.DERSequence({
            array: r
        });
        return this.hTLV = o.getEncodedHex(), this.hTLV;
    }, void 0 !== t && ("number" == typeof t.seconds && (this.seconds = t.seconds), 
    "number" == typeof t.millis && (this.millis = t.millis), "number" == typeof t.micros && (this.micros = t.micros));
}, l.lang.extend(c.asn1.tsp.Accuracy, c.asn1.ASN1Object), c.asn1.tsp.MessageImprint = function(t) {
    c.asn1.tsp.MessageImprint.superclass.constructor.call(this);
    var e = c.asn1, i = c.asn1.x509;
    this.dHashAlg = null, this.dHashValue = null, this.getEncodedHex = function() {
        return "string" == typeof this.hTLV ? this.hTLV : new e.DERSequence({
            array: [ this.dHashAlg, this.dHashValue ]
        }).getEncodedHex();
    }, void 0 !== t && ("string" == typeof t.hashAlg && (this.dHashAlg = new i.AlgorithmIdentifier({
        name: t.hashAlg
    })), "string" == typeof t.hashValue && (this.dHashValue = new e.DEROctetString({
        hex: t.hashValue
    })));
}, l.lang.extend(c.asn1.tsp.MessageImprint, c.asn1.ASN1Object), c.asn1.tsp.TimeStampReq = function(t) {
    c.asn1.tsp.TimeStampReq.superclass.constructor.call(this);
    var e = c.asn1, i = c.asn1.tsp;
    this.dVersion = new e.DERInteger({
        int: 1
    }), this.dMessageImprint = null, this.dPolicy = null, this.dNonce = null, this.certReq = !0, 
    this.setMessageImprint = function(t) {
        t instanceof c.asn1.tsp.MessageImprint ? this.dMessageImprint = t : "object" == a(t) && (this.dMessageImprint = new i.MessageImprint(t));
    }, this.getEncodedHex = function() {
        if (null == this.dMessageImprint) throw "messageImprint shall be specified";
        var t = [ this.dVersion, this.dMessageImprint ];
        null != this.dPolicy && t.push(this.dPolicy), null != this.dNonce && t.push(this.dNonce), 
        this.certReq && t.push(new e.DERBoolean());
        var i = new e.DERSequence({
            array: t
        });
        return this.hTLV = i.getEncodedHex(), this.hTLV;
    }, void 0 !== t && ("object" == a(t.mi) && this.setMessageImprint(t.mi), "object" == a(t.policy) && (this.dPolicy = new e.DERObjectIdentifier(t.policy)), 
    "object" == a(t.nonce) && (this.dNonce = new e.DERInteger(t.nonce)), "boolean" == typeof t.certreq && (this.certReq = t.certreq));
}, l.lang.extend(c.asn1.tsp.TimeStampReq, c.asn1.ASN1Object), c.asn1.tsp.TSTInfo = function(t) {
    c.asn1.tsp.TSTInfo.superclass.constructor.call(this);
    var e = c.asn1, i = c.asn1.x509, n = c.asn1.tsp;
    if (this.dVersion = new e.DERInteger({
        int: 1
    }), this.dPolicy = null, this.dMessageImprint = null, this.dSerialNumber = null, 
    this.dGenTime = null, this.dAccuracy = null, this.dOrdering = null, this.dNonce = null, 
    this.dTsa = null, this.getEncodedHex = function() {
        var t = [ this.dVersion ];
        if (null == this.dPolicy) throw "policy shall be specified.";
        if (t.push(this.dPolicy), null == this.dMessageImprint) throw "messageImprint shall be specified.";
        if (t.push(this.dMessageImprint), null == this.dSerialNumber) throw "serialNumber shall be specified.";
        if (t.push(this.dSerialNumber), null == this.dGenTime) throw "genTime shall be specified.";
        t.push(this.dGenTime), null != this.dAccuracy && t.push(this.dAccuracy), null != this.dOrdering && t.push(this.dOrdering), 
        null != this.dNonce && t.push(this.dNonce), null != this.dTsa && t.push(this.dTsa);
        var i = new e.DERSequence({
            array: t
        });
        return this.hTLV = i.getEncodedHex(), this.hTLV;
    }, void 0 !== t) {
        if ("string" == typeof t.policy) {
            if (!t.policy.match(/^[0-9.]+$/)) throw "policy shall be oid like 0.1.4.134";
            this.dPolicy = new e.DERObjectIdentifier({
                oid: t.policy
            });
        }
        void 0 !== t.messageImprint && (this.dMessageImprint = new n.MessageImprint(t.messageImprint)), 
        void 0 !== t.serialNumber && (this.dSerialNumber = new e.DERInteger(t.serialNumber)), 
        void 0 !== t.genTime && (this.dGenTime = new e.DERGeneralizedTime(t.genTime)), "undefind" != typeof t.accuracy && (this.dAccuracy = new n.Accuracy(t.accuracy)), 
        void 0 !== t.ordering && 1 == t.ordering && (this.dOrdering = new e.DERBoolean()), 
        void 0 !== t.nonce && (this.dNonce = new e.DERInteger(t.nonce)), void 0 !== t.tsa && (this.dTsa = new i.X500Name(t.tsa));
    }
}, l.lang.extend(c.asn1.tsp.TSTInfo, c.asn1.ASN1Object), c.asn1.tsp.TimeStampResp = function(t) {
    c.asn1.tsp.TimeStampResp.superclass.constructor.call(this);
    var e = c.asn1, i = c.asn1.tsp;
    this.dStatus = null, this.dTST = null, this.getEncodedHex = function() {
        if (null == this.dStatus) throw "status shall be specified";
        var t = [ this.dStatus ];
        null != this.dTST && t.push(this.dTST);
        var i = new e.DERSequence({
            array: t
        });
        return this.hTLV = i.getEncodedHex(), this.hTLV;
    }, void 0 !== t && ("object" == a(t.status) && (this.dStatus = new i.PKIStatusInfo(t.status)), 
    void 0 !== t.tst && t.tst instanceof c.asn1.ASN1Object && (this.dTST = t.tst.getContentInfo()));
}, l.lang.extend(c.asn1.tsp.TimeStampResp, c.asn1.ASN1Object), c.asn1.tsp.PKIStatusInfo = function(t) {
    c.asn1.tsp.PKIStatusInfo.superclass.constructor.call(this);
    var e = c.asn1, i = c.asn1.tsp;
    this.dStatus = null, this.dStatusString = null, this.dFailureInfo = null, this.getEncodedHex = function() {
        if (null == this.dStatus) throw "status shall be specified";
        var t = [ this.dStatus ];
        null != this.dStatusString && t.push(this.dStatusString), null != this.dFailureInfo && t.push(this.dFailureInfo);
        var i = new e.DERSequence({
            array: t
        });
        return this.hTLV = i.getEncodedHex(), this.hTLV;
    }, void 0 !== t && ("object" == a(t.status) && (this.dStatus = new i.PKIStatus(t.status)), 
    "object" == a(t.statstr) && (this.dStatusString = new i.PKIFreeText({
        array: t.statstr
    })), "object" == a(t.failinfo) && (this.dFailureInfo = new i.PKIFailureInfo(t.failinfo)));
}, l.lang.extend(c.asn1.tsp.PKIStatusInfo, c.asn1.ASN1Object), c.asn1.tsp.PKIStatus = function(t) {
    c.asn1.tsp.PKIStatus.superclass.constructor.call(this);
    var e = c.asn1, i = c.asn1.tsp;
    if (this.getEncodedHex = function() {
        return this.hTLV = this.dStatus.getEncodedHex(), this.hTLV;
    }, void 0 !== t) if (void 0 !== t.name) {
        var n = i.PKIStatus.valueList;
        if (void 0 === n[t.name]) throw "name undefined: " + t.name;
        this.dStatus = new e.DERInteger({
            int: n[t.name]
        });
    } else this.dStatus = new e.DERInteger(t);
}, l.lang.extend(c.asn1.tsp.PKIStatus, c.asn1.ASN1Object), c.asn1.tsp.PKIStatus.valueList = {
    granted: 0,
    grantedWithMods: 1,
    rejection: 2,
    waiting: 3,
    revocationWarning: 4,
    revocationNotification: 5
}, c.asn1.tsp.PKIFreeText = function(t) {
    c.asn1.tsp.PKIFreeText.superclass.constructor.call(this);
    var e = c.asn1;
    this.textList = [], this.getEncodedHex = function() {
        for (var t = [], i = 0; i < this.textList.length; i++) t.push(new e.DERUTF8String({
            str: this.textList[i]
        }));
        var n = new e.DERSequence({
            array: t
        });
        return this.hTLV = n.getEncodedHex(), this.hTLV;
    }, void 0 !== t && "object" == a(t.array) && (this.textList = t.array);
}, l.lang.extend(c.asn1.tsp.PKIFreeText, c.asn1.ASN1Object), c.asn1.tsp.PKIFailureInfo = function(t) {
    c.asn1.tsp.PKIFailureInfo.superclass.constructor.call(this);
    var e = c.asn1, i = c.asn1.tsp;
    if (this.value = null, this.getEncodedHex = function() {
        if (null == this.value) throw "value shall be specified";
        var t = new Number(this.value).toString(2), i = new e.DERBitString();
        return i.setByBinaryString(t), this.hTLV = i.getEncodedHex(), this.hTLV;
    }, void 0 !== t) if ("string" == typeof t.name) {
        var n = i.PKIFailureInfo.valueList;
        if (void 0 === n[t.name]) throw "name undefined: " + t.name;
        this.value = n[t.name];
    } else "number" == typeof t.int && (this.value = t.int);
}, l.lang.extend(c.asn1.tsp.PKIFailureInfo, c.asn1.ASN1Object), c.asn1.tsp.PKIFailureInfo.valueList = {
    badAlg: 0,
    badRequest: 2,
    badDataFormat: 5,
    timeNotAvailable: 14,
    unacceptedPolicy: 15,
    unacceptedExtension: 16,
    addInfoNotAvailable: 17,
    systemFailure: 25
}, c.asn1.tsp.AbstractTSAAdapter = function(t) {
    this.getTSTHex = function(t, e) {
        throw "not implemented yet";
    };
}, c.asn1.tsp.SimpleTSAAdapter = function(t) {
    c.asn1.tsp.SimpleTSAAdapter.superclass.constructor.call(this), this.params = null, 
    this.serial = 0, this.getTSTHex = function(t, e) {
        var i = c.crypto.Util.hashHex(t, e);
        this.params.tstInfo.messageImprint = {
            hashAlg: e,
            hashValue: i
        }, this.params.tstInfo.serialNumber = {
            int: this.serial++
        };
        var n = Math.floor(1e9 * Math.random());
        return this.params.tstInfo.nonce = {
            int: n
        }, c.asn1.tsp.TSPUtil.newTimeStampToken(this.params).getContentInfoEncodedHex();
    }, void 0 !== t && (this.params = t);
}, l.lang.extend(c.asn1.tsp.SimpleTSAAdapter, c.asn1.tsp.AbstractTSAAdapter), c.asn1.tsp.FixedTSAAdapter = function(t) {
    c.asn1.tsp.FixedTSAAdapter.superclass.constructor.call(this), this.params = null, 
    this.getTSTHex = function(t, e) {
        var i = c.crypto.Util.hashHex(t, e);
        return this.params.tstInfo.messageImprint = {
            hashAlg: e,
            hashValue: i
        }, c.asn1.tsp.TSPUtil.newTimeStampToken(this.params).getContentInfoEncodedHex();
    }, void 0 !== t && (this.params = t);
}, l.lang.extend(c.asn1.tsp.FixedTSAAdapter, c.asn1.tsp.AbstractTSAAdapter), c.asn1.tsp.TSPUtil = new function() {}(), 
c.asn1.tsp.TSPUtil.newTimeStampToken = function(t) {
    var e = c.asn1.cms, i = c.asn1.tsp, n = new e.SignedData(), r = new i.TSTInfo(t.tstInfo).getEncodedHex();
    if (n.dEncapContentInfo.setContentValue({
        hex: r
    }), n.dEncapContentInfo.setContentType("tstinfo"), "object" == a(t.certs)) for (var s = 0; s < t.certs.length; s++) n.addCertificatesByPEM(t.certs[s]);
    var o = n.signerInfoList[0];
    o.setSignerIdentifier(t.signerCert), o.setForContentAndHash({
        sdObj: n,
        eciObj: n.dEncapContentInfo,
        hashAlg: t.hashAlg
    });
    var h = new e.SigningCertificate({
        array: [ t.signerCert ]
    });
    return o.dSignedAttrs.add(h), o.sign(t.signerPrvKey, t.sigAlg), n;
}, c.asn1.tsp.TSPUtil.parseTimeStampReq = function(t) {
    var e = {
        certreq: !1
    }, i = ot.getPosArrayOfChildren_AtObj(t, 0);
    if (i.length < 2) throw "TimeStampReq must have at least 2 items";
    var n = ot.getHexOfTLV_AtObj(t, i[1]);
    e.mi = c.asn1.tsp.TSPUtil.parseMessageImprint(n);
    for (var r = 2; r < i.length; r++) {
        var s = i[r], a = t.substr(s, 2);
        if ("06" == a) {
            var o = ot.getHexOfV_AtObj(t, s);
            e.policy = ot.hextooidstr(o);
        }
        "02" == a && (e.nonce = ot.getHexOfV_AtObj(t, s)), "01" == a && (e.certreq = !0);
    }
    return e;
}, c.asn1.tsp.TSPUtil.parseMessageImprint = function(t) {
    var e = {};
    if ("30" != t.substr(0, 2)) throw "head of messageImprint hex shall be '30'";
    ot.getPosArrayOfChildren_AtObj(t, 0);
    var i = ot.getDecendantIndexByNthList(t, 0, [ 0, 0 ]), n = ot.getHexOfV_AtObj(t, i), r = ot.hextooidstr(n), s = c.asn1.x509.OID.oid2name(r);
    if ("" == s) throw "hashAlg name undefined: " + r;
    var a = s, o = ot.getDecendantIndexByNthList(t, 0, [ 1 ]);
    return e.hashAlg = a, e.hashValue = ot.getHexOfV_AtObj(t, o), e
    /*! asn1cades-1.0.1.js (c) 2014-2017 Kenji Urushima | kjur.github.com/jsrsasign/license
 */;
}, void 0 !== c && c || (c = {}), void 0 !== c.asn1 && c.asn1 || (c.asn1 = {}), 
void 0 !== c.asn1.cades && c.asn1.cades || (c.asn1.cades = {}), c.asn1.cades.SignaturePolicyIdentifier = function(t) {
    c.asn1.cades.SignaturePolicyIdentifier.superclass.constructor.call(this), this.attrTypeOid = "1.2.840.113549.1.9.16.2.15";
    var e = c.asn1, i = c.asn1.cades;
    if (void 0 !== t && "string" == typeof t.oid && "object" == a(t.hash)) {
        var n = new e.DERObjectIdentifier({
            oid: t.oid
        }), r = new i.OtherHashAlgAndValue(t.hash), s = new e.DERSequence({
            array: [ n, r ]
        });
        this.valueList = [ s ];
    }
}, l.lang.extend(c.asn1.cades.SignaturePolicyIdentifier, c.asn1.cms.Attribute), 
c.asn1.cades.OtherHashAlgAndValue = function(t) {
    c.asn1.cades.OtherHashAlgAndValue.superclass.constructor.call(this);
    var e = c.asn1, i = c.asn1.x509;
    this.dAlg = null, this.dHash = null, this.getEncodedHex = function() {
        var t = new e.DERSequence({
            array: [ this.dAlg, this.dHash ]
        });
        return this.hTLV = t.getEncodedHex(), this.hTLV;
    }, void 0 !== t && "string" == typeof t.alg && "string" == typeof t.hash && (this.dAlg = new i.AlgorithmIdentifier({
        name: t.alg
    }), this.dHash = new e.DEROctetString({
        hex: t.hash
    }));
}, l.lang.extend(c.asn1.cades.OtherHashAlgAndValue, c.asn1.ASN1Object), c.asn1.cades.SignatureTimeStamp = function(t) {
    c.asn1.cades.SignatureTimeStamp.superclass.constructor.call(this), this.attrTypeOid = "1.2.840.113549.1.9.16.2.14", 
    this.tstHex = null;
    var e = c.asn1;
    if (void 0 !== t) {
        if (void 0 !== t.res) if ("string" == typeof t.res && t.res.match(/^[0-9A-Fa-f]+$/)) ; else if (!(t.res instanceof c.asn1.ASN1Object)) throw "res param shall be ASN1Object or hex string";
        if (void 0 !== t.tst) if ("string" == typeof t.tst && t.tst.match(/^[0-9A-Fa-f]+$/)) {
            var i = new e.ASN1Object();
            this.tstHex = t.tst, i.hTLV = this.tstHex, i.getEncodedHex(), this.valueList = [ i ];
        } else if (!(t.tst instanceof c.asn1.ASN1Object)) throw "tst param shall be ASN1Object or hex string";
    }
}, l.lang.extend(c.asn1.cades.SignatureTimeStamp, c.asn1.cms.Attribute), c.asn1.cades.CompleteCertificateRefs = function(t) {
    c.asn1.cades.CompleteCertificateRefs.superclass.constructor.call(this), this.attrTypeOid = "1.2.840.113549.1.9.16.2.21";
    c.asn1;
    var e = c.asn1.cades;
    this.setByArray = function(t) {
        this.valueList = [];
        for (var i = 0; i < t.length; i++) {
            var n = new e.OtherCertID(t[i]);
            this.valueList.push(n);
        }
    }, void 0 !== t && "object" == a(t) && "number" == typeof t.length && this.setByArray(t);
}, l.lang.extend(c.asn1.cades.CompleteCertificateRefs, c.asn1.cms.Attribute), c.asn1.cades.OtherCertID = function(t) {
    c.asn1.cades.OtherCertID.superclass.constructor.call(this);
    var e = c.asn1, i = c.asn1.cms, n = c.asn1.cades;
    this.hasIssuerSerial = !0, this.dOtherCertHash = null, this.dIssuerSerial = null, 
    this.setByCertPEM = function(t) {
        this.dOtherCertHash = new n.OtherHash(t), this.hasIssuerSerial && (this.dIssuerSerial = new i.IssuerAndSerialNumber(t));
    }, this.getEncodedHex = function() {
        if (null != this.hTLV) return this.hTLV;
        if (null == this.dOtherCertHash) throw "otherCertHash not set";
        var t = [ this.dOtherCertHash ];
        null != this.dIssuerSerial && t.push(this.dIssuerSerial);
        var i = new e.DERSequence({
            array: t
        });
        return this.hTLV = i.getEncodedHex(), this.hTLV;
    }, void 0 !== t && ("string" == typeof t && -1 != t.indexOf("-----BEGIN ") && this.setByCertPEM(t), 
    "object" == a(t) && (!1 === t.hasis && (this.hasIssuerSerial = !1), "string" == typeof t.cert && this.setByCertPEM(t.cert)));
}, l.lang.extend(c.asn1.cades.OtherCertID, c.asn1.ASN1Object), c.asn1.cades.OtherHash = function(t) {
    c.asn1.cades.OtherHash.superclass.constructor.call(this);
    var e = c.asn1, i = c.asn1.cades;
    if (this.alg = "sha256", this.dOtherHash = null, this.setByCertPEM = function(t) {
        if (-1 == t.indexOf("-----BEGIN ")) throw "certPEM not to seem PEM format";
        var e = ot.pemToHex(t), n = c.crypto.Util.hashHex(e, this.alg);
        this.dOtherHash = new i.OtherHashAlgAndValue({
            alg: this.alg,
            hash: n
        });
    }, this.getEncodedHex = function() {
        if (null == this.dOtherHash) throw "OtherHash not set";
        return this.dOtherHash.getEncodedHex();
    }, void 0 !== t) if ("string" == typeof t) if (-1 != t.indexOf("-----BEGIN ")) this.setByCertPEM(t); else {
        if (!t.match(/^[0-9A-Fa-f]+$/)) throw "unsupported string value for params";
        this.dOtherHash = new e.DEROctetString({
            hex: t
        });
    } else "object" == a(t) && ("string" == typeof t.cert ? ("string" == typeof t.alg && (this.alg = t.alg), 
    this.setByCertPEM(t.cert)) : this.dOtherHash = new i.OtherHashAlgAndValue(t));
}, l.lang.extend(c.asn1.cades.OtherHash, c.asn1.ASN1Object), c.asn1.cades.CAdESUtil = new function() {}(), 
c.asn1.cades.CAdESUtil.addSigTS = function(t, e, i) {}, c.asn1.cades.CAdESUtil.parseSignedDataForAddingUnsigned = function(t) {
    var e = c.asn1, i = c.asn1.cms, n = c.asn1.cades.CAdESUtil, r = {};
    if ("06092a864886f70d010702" != ot.getDecendantHexTLVByNthList(t, 0, [ 0 ])) throw "hex is not CMS SignedData";
    var s = ot.getDecendantIndexByNthList(t, 0, [ 1, 0 ]), a = ot.getPosArrayOfChildren_AtObj(t, s);
    if (a.length < 4) throw "num of SignedData elem shall be 4 at least";
    var o = a.shift();
    r.version = ot.getHexOfTLV_AtObj(t, o);
    var h = a.shift();
    r.algs = ot.getHexOfTLV_AtObj(t, h);
    var u = a.shift();
    r.encapcontent = ot.getHexOfTLV_AtObj(t, u), r.certs = null, r.revs = null, r.si = [];
    var l = a.shift();
    "a0" == t.substr(l, 2) && (r.certs = ot.getHexOfTLV_AtObj(t, l), l = a.shift()), 
    "a1" == t.substr(l, 2) && (r.revs = ot.getHexOfTLV_AtObj(t, l), l = a.shift());
    var f = l;
    if ("31" != t.substr(f, 2)) throw "Can't find signerInfos";
    for (var d = ot.getPosArrayOfChildren_AtObj(t, f), g = 0; g < d.length; g++) {
        var p = d[g], y = n.parseSignerInfoForAddingUnsigned(t, p, g);
        r.si[g] = y;
    }
    var v = null;
    r.obj = new i.SignedData(), (v = new e.ASN1Object()).hTLV = r.version, r.obj.dCMSVersion = v, 
    (v = new e.ASN1Object()).hTLV = r.algs, r.obj.dDigestAlgs = v, (v = new e.ASN1Object()).hTLV = r.encapcontent, 
    r.obj.dEncapContentInfo = v, (v = new e.ASN1Object()).hTLV = r.certs, r.obj.dCerts = v, 
    r.obj.signerInfoList = [];
    for (g = 0; g < r.si.length; g++) r.obj.signerInfoList.push(r.si[g].obj);
    return r;
}, c.asn1.cades.CAdESUtil.parseSignerInfoForAddingUnsigned = function(t, e, i) {
    var n = c.asn1, r = c.asn1.cms, s = {}, a = ot.getPosArrayOfChildren_AtObj(t, e);
    if (6 != a.length) throw "not supported items for SignerInfo (!=6)";
    var o = a.shift();
    s.version = ot.getHexOfTLV_AtObj(t, o);
    var h = a.shift();
    s.si = ot.getHexOfTLV_AtObj(t, h);
    var u = a.shift();
    s.digalg = ot.getHexOfTLV_AtObj(t, u);
    var l = a.shift();
    s.sattrs = ot.getHexOfTLV_AtObj(t, l);
    var f = a.shift();
    s.sigalg = ot.getHexOfTLV_AtObj(t, f);
    var d = a.shift();
    s.sig = ot.getHexOfTLV_AtObj(t, d), s.sigval = ot.getHexOfV_AtObj(t, d);
    var g = null;
    return s.obj = new r.SignerInfo(), (g = new n.ASN1Object()).hTLV = s.version, s.obj.dCMSVersion = g, 
    (g = new n.ASN1Object()).hTLV = s.si, s.obj.dSignerIdentifier = g, (g = new n.ASN1Object()).hTLV = s.digalg, 
    s.obj.dDigestAlgorithm = g, (g = new n.ASN1Object()).hTLV = s.sattrs, s.obj.dSignedAttrs = g, 
    (g = new n.ASN1Object()).hTLV = s.sigalg, s.obj.dSigAlg = g, (g = new n.ASN1Object()).hTLV = s.sig, 
    s.obj.dSig = g, s.obj.dUnsignedAttrs = new r.AttributeList(), s
    /*! asn1csr-1.0.3.js (c) 2015-2017 Kenji Urushima | kjur.github.com/jsrsasign/license
 */;
}, void 0 !== c.asn1.csr && c.asn1.csr || (c.asn1.csr = {}), c.asn1.csr.CertificationRequest = function(t) {
    c.asn1.csr.CertificationRequest.superclass.constructor.call(this);
    this.sign = function(t, e) {
        null == this.prvKey && (this.prvKey = e), this.asn1SignatureAlg = new c.asn1.x509.AlgorithmIdentifier({
            name: t
        }), sig = new c.crypto.Signature({
            alg: t
        }), sig.initSign(this.prvKey), sig.updateHex(this.asn1CSRInfo.getEncodedHex()), 
        this.hexSig = sig.sign(), this.asn1Sig = new c.asn1.DERBitString({
            hex: "00" + this.hexSig
        });
        var i = new c.asn1.DERSequence({
            array: [ this.asn1CSRInfo, this.asn1SignatureAlg, this.asn1Sig ]
        });
        this.hTLV = i.getEncodedHex(), this.isModified = !1;
    }, this.getPEMString = function() {
        return c.asn1.ASN1Util.getPEMStringFromHex(this.getEncodedHex(), "CERTIFICATE REQUEST");
    }, this.getEncodedHex = function() {
        if (0 == this.isModified && null != this.hTLV) return this.hTLV;
        throw "not signed yet";
    }, void 0 !== t && void 0 !== t.csrinfo && (this.asn1CSRInfo = t.csrinfo);
}, l.lang.extend(c.asn1.csr.CertificationRequest, c.asn1.ASN1Object), c.asn1.csr.CertificationRequestInfo = function(t) {
    c.asn1.csr.CertificationRequestInfo.superclass.constructor.call(this), this._initialize = function() {
        this.asn1Array = new Array(), this.asn1Version = new c.asn1.DERInteger({
            int: 0
        }), this.asn1Subject = null, this.asn1SubjPKey = null, this.extensionsArray = new Array();
    }, this.setSubjectByParam = function(t) {
        this.asn1Subject = new c.asn1.x509.X500Name(t);
    }, this.setSubjectPublicKeyByGetKey = function(t) {
        var e = Ft.getKey(t);
        this.asn1SubjPKey = new c.asn1.x509.SubjectPublicKeyInfo(e);
    }, this.appendExtensionByName = function(t, e) {
        c.asn1.x509.Extension.appendByNameToArray(t, e, this.extensionsArray);
    }, this.getEncodedHex = function() {
        if (this.asn1Array = new Array(), this.asn1Array.push(this.asn1Version), this.asn1Array.push(this.asn1Subject), 
        this.asn1Array.push(this.asn1SubjPKey), this.extensionsArray.length > 0) {
            var t = new c.asn1.DERSequence({
                array: this.extensionsArray
            }), e = new c.asn1.DERSet({
                array: [ t ]
            }), i = new c.asn1.DERSequence({
                array: [ new c.asn1.DERObjectIdentifier({
                    oid: "1.2.840.113549.1.9.14"
                }), e ]
            }), n = new c.asn1.DERTaggedObject({
                explicit: !0,
                tag: "a0",
                obj: i
            });
            this.asn1Array.push(n);
        } else {
            n = new c.asn1.DERTaggedObject({
                explicit: !1,
                tag: "a0",
                obj: new c.asn1.DERNull()
            });
            this.asn1Array.push(n);
        }
        var r = new c.asn1.DERSequence({
            array: this.asn1Array
        });
        return this.hTLV = r.getEncodedHex(), this.isModified = !1, this.hTLV;
    }, this._initialize();
}, l.lang.extend(c.asn1.csr.CertificationRequestInfo, c.asn1.ASN1Object), c.asn1.csr.CSRUtil = new function() {}(), 
c.asn1.csr.CSRUtil.newCSRPEM = function(t) {
    var e = c.asn1.csr;
    if (void 0 === t.subject) throw "parameter subject undefined";
    if (void 0 === t.sbjpubkey) throw "parameter sbjpubkey undefined";
    if (void 0 === t.sigalg) throw "parameter sigalg undefined";
    if (void 0 === t.sbjprvkey) throw "parameter sbjpubkey undefined";
    var i = new e.CertificationRequestInfo();
    if (i.setSubjectByParam(t.subject), i.setSubjectPublicKeyByGetKey(t.sbjpubkey), 
    void 0 !== t.ext && void 0 !== t.ext.length) for (var n = 0; n < t.ext.length; n++) for (key in t.ext[n]) i.appendExtensionByName(key, t.ext[n][key]);
    var r = new e.CertificationRequest({
        csrinfo: i
    }), s = Ft.getKey(t.sbjprvkey);
    return r.sign(t.sigalg, s), r.getPEMString();
}, c.asn1.csr.CSRUtil.getInfo = function(t) {
    var e = {
        subject: {},
        pubkey: {}
    };
    if (-1 == t.indexOf("-----BEGIN CERTIFICATE REQUEST")) throw "argument is not PEM file";
    var i = ot.pemToHex(t, "CERTIFICATE REQUEST");
    return e.subject.hex = ot.getDecendantHexTLVByNthList(i, 0, [ 0, 1 ]), e.subject.name = Nt.hex2dn(e.subject.hex), 
    e.pubkey.hex = ot.getDecendantHexTLVByNthList(i, 0, [ 0, 2 ]), e.pubkey.obj = Ft.getKey(e.pubkey.hex, null, "pkcs8pub"), 
    e
    /*! asn1ocsp-1.0.1.js (c) 2016 Kenji Urushima | kjur.github.com/jsrsasign/license
 */;
}, void 0 !== c && c || (c = {}), void 0 !== c.asn1 && c.asn1 || (c.asn1 = {}), 
void 0 !== c.asn1.ocsp && c.asn1.ocsp || (c.asn1.ocsp = {}), c.asn1.ocsp.DEFAULT_HASH = "sha1", 
c.asn1.ocsp.CertID = function(t) {
    c.asn1.ocsp.CertID.superclass.constructor.call(this);
    var e = c.asn1, i = c.asn1.x509;
    if (this.dHashAlg = null, this.dIssuerNameHash = null, this.dIssuerKeyHash = null, 
    this.dSerialNumber = null, this.setByValue = function(t, n, r, s) {
        void 0 === s && (s = c.asn1.ocsp.DEFAULT_HASH), this.dHashAlg = new i.AlgorithmIdentifier({
            name: s
        }), this.dIssuerNameHash = new e.DEROctetString({
            hex: t
        }), this.dIssuerKeyHash = new e.DEROctetString({
            hex: n
        }), this.dSerialNumber = new e.DERInteger({
            hex: r
        });
    }, this.setByCert = function(t, e, i) {
        void 0 === i && (i = c.asn1.ocsp.DEFAULT_HASH);
        var n = new Nt();
        n.readCertPEM(e);
        var r = new Nt();
        r.readCertPEM(t);
        var s = Nt.getPublicKeyInfoPropOfCertPEM(t).keyhex, a = n.getSerialNumberHex(), o = c.crypto.Util.hashHex(r.getSubjectHex(), i), h = c.crypto.Util.hashHex(s, i);
        this.setByValue(o, h, a, i), this.hoge = n.getSerialNumberHex();
    }, this.getEncodedHex = function() {
        if (null === this.dHashAlg && null === this.dIssuerNameHash && null === this.dIssuerKeyHash && null === this.dSerialNumber) throw "not yet set values";
        var t = [ this.dHashAlg, this.dIssuerNameHash, this.dIssuerKeyHash, this.dSerialNumber ], i = new e.DERSequence({
            array: t
        });
        return this.hTLV = i.getEncodedHex(), this.hTLV;
    }, void 0 !== t) {
        var n = t;
        if (void 0 !== n.issuerCert && void 0 !== n.subjectCert) {
            var r = c.asn1.ocsp.DEFAULT_HASH;
            void 0 === n.alg && (r = void 0), this.setByCert(n.issuerCert, n.subjectCert, r);
        } else {
            if (void 0 === n.namehash || void 0 === n.keyhash || void 0 === n.serial) throw "invalid constructor arguments";
            r = c.asn1.ocsp.DEFAULT_HASH;
            void 0 === n.alg && (r = void 0), this.setByValue(n.namehash, n.keyhash, n.serial, r);
        }
    }
}, l.lang.extend(c.asn1.ocsp.CertID, c.asn1.ASN1Object), c.asn1.ocsp.Request = function(t) {
    if (c.asn1.ocsp.Request.superclass.constructor.call(this), this.dReqCert = null, 
    this.dExt = null, this.getEncodedHex = function() {
        var t = [];
        if (null === this.dReqCert) throw "reqCert not set";
        t.push(this.dReqCert);
        var e = new c.asn1.DERSequence({
            array: t
        });
        return this.hTLV = e.getEncodedHex(), this.hTLV;
    }, void 0 !== t) {
        var e = new c.asn1.ocsp.CertID(t);
        this.dReqCert = e;
    }
}, l.lang.extend(c.asn1.ocsp.Request, c.asn1.ASN1Object), c.asn1.ocsp.TBSRequest = function(t) {
    c.asn1.ocsp.TBSRequest.superclass.constructor.call(this), this.version = 0, this.dRequestorName = null, 
    this.dRequestList = [], this.dRequestExt = null, this.setRequestListByParam = function(t) {
        for (var e = [], i = 0; i < t.length; i++) {
            var n = new c.asn1.ocsp.Request(t[0]);
            e.push(n);
        }
        this.dRequestList = e;
    }, this.getEncodedHex = function() {
        var t = [];
        if (0 !== this.version) throw "not supported version: " + this.version;
        if (null !== this.dRequestorName) throw "requestorName not supported";
        var e = new c.asn1.DERSequence({
            array: this.dRequestList
        });
        if (t.push(e), null !== this.dRequestExt) throw "requestExtensions not supported";
        var i = new c.asn1.DERSequence({
            array: t
        });
        return this.hTLV = i.getEncodedHex(), this.hTLV;
    }, void 0 !== t && void 0 !== t.reqList && this.setRequestListByParam(t.reqList);
}, l.lang.extend(c.asn1.ocsp.TBSRequest, c.asn1.ASN1Object), c.asn1.ocsp.OCSPRequest = function(t) {
    if (c.asn1.ocsp.OCSPRequest.superclass.constructor.call(this), this.dTbsRequest = null, 
    this.dOptionalSignature = null, this.getEncodedHex = function() {
        var t = [];
        if (null === this.dTbsRequest) throw "tbsRequest not set";
        if (t.push(this.dTbsRequest), null !== this.dOptionalSignature) throw "optionalSignature not supported";
        var e = new c.asn1.DERSequence({
            array: t
        });
        return this.hTLV = e.getEncodedHex(), this.hTLV;
    }, void 0 !== t && void 0 !== t.reqList) {
        var e = new c.asn1.ocsp.TBSRequest(t);
        this.dTbsRequest = e;
    }
}, l.lang.extend(c.asn1.ocsp.OCSPRequest, c.asn1.ASN1Object), c.asn1.ocsp.OCSPUtil = {}, 
c.asn1.ocsp.OCSPUtil.getRequestHex = function(t, e, i) {
    void 0 === i && (i = c.asn1.ocsp.DEFAULT_HASH);
    var n = {
        alg: i,
        issuerCert: t,
        subjectCert: e
    };
    return new c.asn1.ocsp.OCSPRequest({
        reqList: [ n ]
    }).getEncodedHex();
}, c.asn1.ocsp.OCSPUtil.getOCSPResponseInfo = function(t) {
    var e = {};
    try {
        var i = ot.getVbyList(t, 0, [ 0 ], "0a");
        e.responseStatus = parseInt(i, 16);
    } catch (t) {}
    if (0 !== e.responseStatus) return e;
    try {
        var n = ot.getDecendantIndexByNthList(t, 0, [ 1, 0, 1, 0, 0, 2, 0, 1 ]);
        "80" === t.substr(n, 2) ? e.certStatus = "good" : "a1" === t.substr(n, 2) ? (e.certStatus = "revoked", 
        e.revocationTime = pt(ot.getDecendantHexVByNthList(t, n, [ 0 ]))) : "82" === t.substr(n, 2) && (e.certStatus = "unknown");
    } catch (t) {}
    try {
        var r = ot.getDecendantIndexByNthList(t, 0, [ 1, 0, 1, 0, 0, 2, 0, 2 ]);
        e.thisUpdate = pt(ot.getHexOfV_AtObj(t, r));
    } catch (t) {}
    try {
        var s = ot.getDecendantIndexByNthList(t, 0, [ 1, 0, 1, 0, 0, 2, 0, 3 ]);
        "a0" === t.substr(s, 2) && (e.nextUpdate = pt(ot.getDecendantHexVByNthList(t, s, [ 0 ])));
    } catch (t) {}
    return e;
}, void 0 !== c && c || (c = {}), void 0 !== c.lang && c.lang || (c.lang = {}), 
c.lang.String = function() {}, "function" == typeof Buffer ? (st = function(t) {
    return ct(new Buffer(t, "utf8").toString("base64"));
}, at = function(t) {
    return new Buffer(lt(t), "base64").toString("utf8");
}) : (st = function(t) {
    return ft(mt(xt(t)));
}, at = function(t) {
    return decodeURIComponent(At(dt(t)));
}), c.lang.String.isInteger = function(t) {
    return !!t.match(/^[0-9]+$/) || !!t.match(/^-[0-9]+$/);
}, c.lang.String.isHex = function(t) {
    return !(t.length % 2 != 0 || !t.match(/^[0-9a-f]+$/) && !t.match(/^[0-9A-F]+$/));
}, c.lang.String.isBase64 = function(t) {
    return !(!(t = t.replace(/\s+/g, "")).match(/^[0-9A-Za-z+\/]+={0,3}$/) || t.length % 4 != 0);
}, c.lang.String.isBase64URL = function(t) {
    return !t.match(/[+/ = ] /) && (t = lt(t), c.lang.String.isBase64(t));
}, c.lang.String.isIntegerArray = function(t) {
    return !!(t = t.replace(/\s+/g, "")).match(/^\[[0-9,]+\]$/);
};

/*! crypto-1.1.12.js (c) 2013-2017 Kenji Urushima | kjur.github.com/jsrsasign/license
 */ void 0 !== c && c || (c = {}), void 0 !== c.crypto && c.crypto || (c.crypto = {}), 
c.crypto.Util = new function() {
    this.DIGESTINFOHEAD = {
        sha1: "3021300906052b0e03021a05000414",
        sha224: "302d300d06096086480165030402040500041c",
        sha256: "3031300d060960864801650304020105000420",
        sha384: "3041300d060960864801650304020205000430",
        sha512: "3051300d060960864801650304020305000440",
        md2: "3020300c06082a864886f70d020205000410",
        md5: "3020300c06082a864886f70d020505000410",
        ripemd160: "3021300906052b2403020105000414"
    }, this.DEFAULTPROVIDER = {
        md5: "cryptojs",
        sha1: "cryptojs",
        sha224: "cryptojs",
        sha256: "cryptojs",
        sha384: "cryptojs",
        sha512: "cryptojs",
        ripemd160: "cryptojs",
        hmacmd5: "cryptojs",
        hmacsha1: "cryptojs",
        hmacsha224: "cryptojs",
        hmacsha256: "cryptojs",
        hmacsha384: "cryptojs",
        hmacsha512: "cryptojs",
        hmacripemd160: "cryptojs",
        MD5withRSA: "cryptojs/jsrsa",
        SHA1withRSA: "cryptojs/jsrsa",
        SHA224withRSA: "cryptojs/jsrsa",
        SHA256withRSA: "cryptojs/jsrsa",
        SHA384withRSA: "cryptojs/jsrsa",
        SHA512withRSA: "cryptojs/jsrsa",
        RIPEMD160withRSA: "cryptojs/jsrsa",
        MD5withECDSA: "cryptojs/jsrsa",
        SHA1withECDSA: "cryptojs/jsrsa",
        SHA224withECDSA: "cryptojs/jsrsa",
        SHA256withECDSA: "cryptojs/jsrsa",
        SHA384withECDSA: "cryptojs/jsrsa",
        SHA512withECDSA: "cryptojs/jsrsa",
        RIPEMD160withECDSA: "cryptojs/jsrsa",
        SHA1withDSA: "cryptojs/jsrsa",
        SHA224withDSA: "cryptojs/jsrsa",
        SHA256withDSA: "cryptojs/jsrsa",
        MD5withRSAandMGF1: "cryptojs/jsrsa",
        SHA1withRSAandMGF1: "cryptojs/jsrsa",
        SHA224withRSAandMGF1: "cryptojs/jsrsa",
        SHA256withRSAandMGF1: "cryptojs/jsrsa",
        SHA384withRSAandMGF1: "cryptojs/jsrsa",
        SHA512withRSAandMGF1: "cryptojs/jsrsa",
        RIPEMD160withRSAandMGF1: "cryptojs/jsrsa"
    }, this.CRYPTOJSMESSAGEDIGESTNAME = {
        md5: g.algo.MD5,
        sha1: g.algo.SHA1,
        sha224: g.algo.SHA224,
        sha256: g.algo.SHA256,
        sha384: g.algo.SHA384,
        sha512: g.algo.SHA512,
        ripemd160: g.algo.RIPEMD160
    }, this.getDigestInfoHex = function(t, e) {
        if (void 0 === this.DIGESTINFOHEAD[e]) throw "alg not supported in Util.DIGESTINFOHEAD: " + e;
        return this.DIGESTINFOHEAD[e] + t;
    }, this.getPaddedDigestInfoHex = function(t, e, i) {
        var n = this.getDigestInfoHex(t, e), r = i / 4;
        if (n.length + 22 > r) throw "key is too short for SigAlg: keylen=" + i + "," + e;
        for (var s = "0001", a = "00" + n, o = "", h = r - s.length - a.length, u = 0; u < h; u += 2) o += "ff";
        return s + o + a;
    }, this.hashString = function(t, e) {
        return new c.crypto.MessageDigest({
            alg: e
        }).digestString(t);
    }, this.hashHex = function(t, e) {
        return new c.crypto.MessageDigest({
            alg: e
        }).digestHex(t);
    }, this.sha1 = function(t) {
        return new c.crypto.MessageDigest({
            alg: "sha1",
            prov: "cryptojs"
        }).digestString(t);
    }, this.sha256 = function(t) {
        return new c.crypto.MessageDigest({
            alg: "sha256",
            prov: "cryptojs"
        }).digestString(t);
    }, this.sha256Hex = function(t) {
        return new c.crypto.MessageDigest({
            alg: "sha256",
            prov: "cryptojs"
        }).digestHex(t);
    }, this.sha512 = function(t) {
        return new c.crypto.MessageDigest({
            alg: "sha512",
            prov: "cryptojs"
        }).digestString(t);
    }, this.sha512Hex = function(t) {
        return new c.crypto.MessageDigest({
            alg: "sha512",
            prov: "cryptojs"
        }).digestHex(t);
    };
}(), c.crypto.Util.md5 = function(t) {
    return new c.crypto.MessageDigest({
        alg: "md5",
        prov: "cryptojs"
    }).digestString(t);
}, c.crypto.Util.ripemd160 = function(t) {
    return new c.crypto.MessageDigest({
        alg: "ripemd160",
        prov: "cryptojs"
    }).digestString(t);
}, c.crypto.Util.SECURERANDOMGEN = new Y(), c.crypto.Util.getRandomHexOfNbytes = function(t) {
    var e = new Array(t);
    return c.crypto.Util.SECURERANDOMGEN.nextBytes(e), ht(e);
}, c.crypto.Util.getRandomBigIntegerOfNbytes = function(t) {
    return new m(c.crypto.Util.getRandomHexOfNbytes(t), 16);
}, c.crypto.Util.getRandomHexOfNbits = function(t) {
    var e = t % 8, i = new Array((t - e) / 8 + 1);
    return c.crypto.Util.SECURERANDOMGEN.nextBytes(i), i[0] = (255 << e & 255 ^ 255) & i[0], 
    ht(i);
}, c.crypto.Util.getRandomBigIntegerOfNbits = function(t) {
    return new m(c.crypto.Util.getRandomHexOfNbits(t), 16);
}, c.crypto.Util.getRandomBigIntegerZeroToMax = function(t) {
    for (var e = t.bitLength(); ;) {
        var i = c.crypto.Util.getRandomBigIntegerOfNbits(e);
        if (-1 != t.compareTo(i)) return i;
    }
}, c.crypto.Util.getRandomBigIntegerMinToMax = function(t, e) {
    var i = t.compareTo(e);
    if (1 == i) throw "biMin is greater than biMax";
    if (0 == i) return t;
    var n = e.subtract(t);
    return c.crypto.Util.getRandomBigIntegerZeroToMax(n).add(t);
}, c.crypto.MessageDigest = function(t) {
    this.setAlgAndProvider = function(t, e) {
        if (null !== (t = c.crypto.MessageDigest.getCanonicalAlgName(t)) && void 0 === e && (e = c.crypto.Util.DEFAULTPROVIDER[t]), 
        -1 != ":md5:sha1:sha224:sha256:sha384:sha512:ripemd160:".indexOf(t) && "cryptojs" == e) {
            try {
                this.md = c.crypto.Util.CRYPTOJSMESSAGEDIGESTNAME[t].create();
            } catch (e) {
                e = VM2_INTERNAL_STATE_DO_NOT_USE_OR_PROGRAM_WILL_FAIL.handleException(e);
                throw "setAlgAndProvider hash alg set fail alg=" + t + "/" + e;
            }
            this.updateString = function(t) {
                this.md.update(t);
            }, this.updateHex = function(t) {
                var e = g.enc.Hex.parse(t);
                this.md.update(e);
            }, this.digest = function() {
                return this.md.finalize().toString(g.enc.Hex);
            }, this.digestString = function(t) {
                return this.updateString(t), this.digest();
            }, this.digestHex = function(t) {
                return this.updateHex(t), this.digest();
            };
        }
        if (-1 != ":sha256:".indexOf(t) && "sjcl" == e) {
            try {
                this.md = new sjcl.hash.sha256();
            } catch (e) {
                e = VM2_INTERNAL_STATE_DO_NOT_USE_OR_PROGRAM_WILL_FAIL.handleException(e);
                throw "setAlgAndProvider hash alg set fail alg=" + t + "/" + e;
            }
            this.updateString = function(t) {
                this.md.update(t);
            }, this.updateHex = function(t) {
                var e = sjcl.codec.hex.toBits(t);
                this.md.update(e);
            }, this.digest = function() {
                var t = this.md.finalize();
                return sjcl.codec.hex.fromBits(t);
            }, this.digestString = function(t) {
                return this.updateString(t), this.digest();
            }, this.digestHex = function(t) {
                return this.updateHex(t), this.digest();
            };
        }
    }, this.updateString = function(t) {
        throw "updateString(str) not supported for this alg/prov: " + this.algName + "/" + this.provName;
    }, this.updateHex = function(t) {
        throw "updateHex(hex) not supported for this alg/prov: " + this.algName + "/" + this.provName;
    }, this.digest = function() {
        throw "digest() not supported for this alg/prov: " + this.algName + "/" + this.provName;
    }, this.digestString = function(t) {
        throw "digestString(str) not supported for this alg/prov: " + this.algName + "/" + this.provName;
    }, this.digestHex = function(t) {
        throw "digestHex(hex) not supported for this alg/prov: " + this.algName + "/" + this.provName;
    }, void 0 !== t && void 0 !== t.alg && (this.algName = t.alg, void 0 === t.prov && (this.provName = c.crypto.Util.DEFAULTPROVIDER[this.algName]), 
    this.setAlgAndProvider(this.algName, this.provName));
}, c.crypto.MessageDigest.getCanonicalAlgName = function(t) {
    return "string" == typeof t && (t = (t = t.toLowerCase()).replace(/-/, "")), t;
}, c.crypto.MessageDigest.getHashLength = function(t) {
    var e = c.crypto.MessageDigest, i = e.getCanonicalAlgName(t);
    if (void 0 === e.HASHLENGTH[i]) throw "not supported algorithm: " + t;
    return e.HASHLENGTH[i];
}, c.crypto.MessageDigest.HASHLENGTH = {
    md5: 16,
    sha1: 20,
    sha224: 28,
    sha256: 32,
    sha384: 48,
    sha512: 64,
    ripemd160: 20
}, c.crypto.Mac = function(t) {
    this.setAlgAndProvider = function(t, e) {
        if (null == (t = t.toLowerCase()) && (t = "hmacsha1"), "hmac" != (t = t.toLowerCase()).substr(0, 4)) throw "setAlgAndProvider unsupported HMAC alg: " + t;
        void 0 === e && (e = c.crypto.Util.DEFAULTPROVIDER[t]), this.algProv = t + "/" + e;
        var i = t.substr(4);
        if (-1 != ":md5:sha1:sha224:sha256:sha384:sha512:ripemd160:".indexOf(i) && "cryptojs" == e) {
            try {
                var n = c.crypto.Util.CRYPTOJSMESSAGEDIGESTNAME[i];
                this.mac = g.algo.HMAC.create(n, this.pass);
            } catch (t) {
                t = VM2_INTERNAL_STATE_DO_NOT_USE_OR_PROGRAM_WILL_FAIL.handleException(t);
                throw "setAlgAndProvider hash alg set fail hashAlg=" + i + "/" + t;
            }
            this.updateString = function(t) {
                this.mac.update(t);
            }, this.updateHex = function(t) {
                var e = g.enc.Hex.parse(t);
                this.mac.update(e);
            }, this.doFinal = function() {
                return this.mac.finalize().toString(g.enc.Hex);
            }, this.doFinalString = function(t) {
                return this.updateString(t), this.doFinal();
            }, this.doFinalHex = function(t) {
                return this.updateHex(t), this.doFinal();
            };
        }
    }, this.updateString = function(t) {
        throw "updateString(str) not supported for this alg/prov: " + this.algProv;
    }, this.updateHex = function(t) {
        throw "updateHex(hex) not supported for this alg/prov: " + this.algProv;
    }, this.doFinal = function() {
        throw "digest() not supported for this alg/prov: " + this.algProv;
    }, this.doFinalString = function(t) {
        throw "digestString(str) not supported for this alg/prov: " + this.algProv;
    }, this.doFinalHex = function(t) {
        throw "digestHex(hex) not supported for this alg/prov: " + this.algProv;
    }, this.setPassword = function(t) {
        if ("string" == typeof t) {
            var e = t;
            return t.length % 2 != 1 && t.match(/^[0-9A-Fa-f]+$/) || (e = vt(t)), void (this.pass = g.enc.Hex.parse(e));
        }
        if ("object" != a(t)) throw "KJUR.crypto.Mac unsupported password type: " + t;
        e = null;
        if (void 0 !== t.hex) {
            if (t.hex.length % 2 != 0 || !t.hex.match(/^[0-9A-Fa-f]+$/)) throw "Mac: wrong hex password: " + t.hex;
            e = t.hex;
        }
        if (void 0 !== t.utf8 && (e = gt(t.utf8)), void 0 !== t.rstr && (e = vt(t.rstr)), 
        void 0 !== t.b64 && (e = S(t.b64)), void 0 !== t.b64u && (e = dt(t.b64u)), null == e) throw "KJUR.crypto.Mac unsupported password type: " + t;
        this.pass = g.enc.Hex.parse(e);
    }, void 0 !== t && (void 0 !== t.pass && this.setPassword(t.pass), void 0 !== t.alg && (this.algName = t.alg, 
    void 0 === t.prov && (this.provName = c.crypto.Util.DEFAULTPROVIDER[this.algName]), 
    this.setAlgAndProvider(this.algName, this.provName)));
}, c.crypto.Signature = function(t) {
    var e = null;
    if (this._setAlgNames = function() {
        var t = this.algName.match(/^(.+)with(.+)$/);
        t && (this.mdAlgName = t[1].toLowerCase(), this.pubkeyAlgName = t[2].toLowerCase());
    }, this._zeroPaddingOfSignature = function(t, e) {
        for (var i = "", n = e / 4 - t.length, r = 0; r < n; r++) i += "0";
        return i + t;
    }, this.setAlgAndProvider = function(t, e) {
        if (this._setAlgNames(), "cryptojs/jsrsa" != e) throw "provider not supported: " + e;
        if (-1 != ":md5:sha1:sha224:sha256:sha384:sha512:ripemd160:".indexOf(this.mdAlgName)) {
            try {
                this.md = new c.crypto.MessageDigest({
                    alg: this.mdAlgName
                });
            } catch (t) {
                t = VM2_INTERNAL_STATE_DO_NOT_USE_OR_PROGRAM_WILL_FAIL.handleException(t);
                throw "setAlgAndProvider hash alg set fail alg=" + this.mdAlgName + "/" + t;
            }
            this.init = function(t, e) {
                var i = null;
                try {
                    i = void 0 === e ? Ft.getKey(t) : Ft.getKey(t, e);
                } catch (t) {
                    t = VM2_INTERNAL_STATE_DO_NOT_USE_OR_PROGRAM_WILL_FAIL.handleException(t);
                    throw "init failed:" + t;
                }
                if (!0 === i.isPrivate) this.prvKey = i, this.state = "SIGN"; else {
                    if (!0 !== i.isPublic) throw "init failed.:" + i;
                    this.pubKey = i, this.state = "VERIFY";
                }
            }, this.initSign = function(t) {
                "string" == typeof t.ecprvhex && "string" == typeof t.eccurvename ? (this.ecprvhex = t.ecprvhex, 
                this.eccurvename = t.eccurvename) : this.prvKey = t, this.state = "SIGN";
            }, this.initVerifyByPublicKey = function(t) {
                "string" == typeof t.ecpubhex && "string" == typeof t.eccurvename ? (this.ecpubhex = t.ecpubhex, 
                this.eccurvename = t.eccurvename) : (t instanceof c.crypto.ECDSA || t instanceof Q) && (this.pubKey = t), 
                this.state = "VERIFY";
            }, this.initVerifyByCertificatePEM = function(t) {
                var e = new Nt();
                e.readCertPEM(t), this.pubKey = e.subjectPublicKeyRSA, this.state = "VERIFY";
            }, this.updateString = function(t) {
                this.md.updateString(t);
            }, this.updateHex = function(t) {
                this.md.updateHex(t);
            }, this.sign = function() {
                if (this.sHashHex = this.md.digest(), void 0 !== this.ecprvhex && void 0 !== this.eccurvename) {
                    var t = new c.crypto.ECDSA({
                        curve: this.eccurvename
                    });
                    this.hSign = t.signHex(this.sHashHex, this.ecprvhex);
                } else if (this.prvKey instanceof Q && "rsaandmgf1" == this.pubkeyAlgName) this.hSign = this.prvKey.signWithMessageHashPSS(this.sHashHex, this.mdAlgName, this.pssSaltLen); else if (this.prvKey instanceof Q && "rsa" == this.pubkeyAlgName) this.hSign = this.prvKey.signWithMessageHash(this.sHashHex, this.mdAlgName); else if (this.prvKey instanceof c.crypto.ECDSA) this.hSign = this.prvKey.signWithMessageHash(this.sHashHex); else {
                    if (!(this.prvKey instanceof c.crypto.DSA)) throw "Signature: unsupported public key alg: " + this.pubkeyAlgName;
                    this.hSign = this.prvKey.signWithMessageHash(this.sHashHex);
                }
                return this.hSign;
            }, this.signString = function(t) {
                return this.updateString(t), this.sign();
            }, this.signHex = function(t) {
                return this.updateHex(t), this.sign();
            }, this.verify = function(t) {
                if (this.sHashHex = this.md.digest(), void 0 !== this.ecpubhex && void 0 !== this.eccurvename) return new c.crypto.ECDSA({
                    curve: this.eccurvename
                }).verifyHex(this.sHashHex, t, this.ecpubhex);
                if (this.pubKey instanceof Q && "rsaandmgf1" == this.pubkeyAlgName) return this.pubKey.verifyWithMessageHashPSS(this.sHashHex, t, this.mdAlgName, this.pssSaltLen);
                if (this.pubKey instanceof Q && "rsa" == this.pubkeyAlgName) return this.pubKey.verifyWithMessageHash(this.sHashHex, t);
                if (this.pubKey instanceof c.crypto.ECDSA) return this.pubKey.verifyWithMessageHash(this.sHashHex, t);
                if (this.pubKey instanceof c.crypto.DSA) return this.pubKey.verifyWithMessageHash(this.sHashHex, t);
                throw "Signature: unsupported public key alg: " + this.pubkeyAlgName;
            };
        }
    }, this.init = function(t, e) {
        throw "init(key, pass) not supported for this alg:prov=" + this.algProvName;
    }, this.initVerifyByPublicKey = function(t) {
        throw "initVerifyByPublicKey(rsaPubKeyy) not supported for this alg:prov=" + this.algProvName;
    }, this.initVerifyByCertificatePEM = function(t) {
        throw "initVerifyByCertificatePEM(certPEM) not supported for this alg:prov=" + this.algProvName;
    }, this.initSign = function(t) {
        throw "initSign(prvKey) not supported for this alg:prov=" + this.algProvName;
    }, this.updateString = function(t) {
        throw "updateString(str) not supported for this alg:prov=" + this.algProvName;
    }, this.updateHex = function(t) {
        throw "updateHex(hex) not supported for this alg:prov=" + this.algProvName;
    }, this.sign = function() {
        throw "sign() not supported for this alg:prov=" + this.algProvName;
    }, this.signString = function(t) {
        throw "digestString(str) not supported for this alg:prov=" + this.algProvName;
    }, this.signHex = function(t) {
        throw "digestHex(hex) not supported for this alg:prov=" + this.algProvName;
    }, this.verify = function(t) {
        throw "verify(hSigVal) not supported for this alg:prov=" + this.algProvName;
    }, this.initParams = t, void 0 !== t && (void 0 !== t.alg && (this.algName = t.alg, 
    void 0 === t.prov ? this.provName = c.crypto.Util.DEFAULTPROVIDER[this.algName] : this.provName = t.prov, 
    this.algProvName = this.algName + ":" + this.provName, this.setAlgAndProvider(this.algName, this.provName), 
    this._setAlgNames()), void 0 !== t.psssaltlen && (this.pssSaltLen = t.psssaltlen), 
    void 0 !== t.prvkeypem)) {
        if (void 0 !== t.prvkeypas) throw "both prvkeypem and prvkeypas parameters not supported";
        try {
            (e = new Q()).readPrivateKeyFromPEMString(t.prvkeypem), this.initSign(e);
        } catch (t) {
            t = VM2_INTERNAL_STATE_DO_NOT_USE_OR_PROGRAM_WILL_FAIL.handleException(t);
            throw "fatal error to load pem private key: " + t;
        }
    }
}, c.crypto.Cipher = function(t) {}, c.crypto.Cipher.encrypt = function(t, e, i) {
    if (e instanceof Q && e.isPublic) {
        var n = c.crypto.Cipher.getAlgByKeyAndName(e, i);
        if ("RSA" === n) return e.encrypt(t);
        if ("RSAOAEP" === n) return e.encryptOAEP(t, "sha1");
        var r = n.match(/^RSAOAEP(\d+)$/);
        if (null !== r) return e.encryptOAEP(t, "sha" + r[1]);
        throw "Cipher.encrypt: unsupported algorithm for RSAKey: " + i;
    }
    throw "Cipher.encrypt: unsupported key or algorithm";
}, c.crypto.Cipher.decrypt = function(t, e, i) {
    if (e instanceof Q && e.isPrivate) {
        var n = c.crypto.Cipher.getAlgByKeyAndName(e, i);
        if ("RSA" === n) return e.decrypt(t);
        if ("RSAOAEP" === n) return e.decryptOAEP(t, "sha1");
        var r = n.match(/^RSAOAEP(\d+)$/);
        if (null !== r) return e.decryptOAEP(t, "sha" + r[1]);
        throw "Cipher.decrypt: unsupported algorithm for RSAKey: " + i;
    }
    throw "Cipher.decrypt: unsupported key or algorithm";
}, c.crypto.Cipher.getAlgByKeyAndName = function(t, e) {
    if (t instanceof Q) {
        if (-1 != ":RSA:RSAOAEP:RSAOAEP224:RSAOAEP256:RSAOAEP384:RSAOAEP512:".indexOf(e)) return e;
        if (null == e) return "RSA";
        throw "getAlgByKeyAndName: not supported algorithm name for RSAKey: " + e;
    }
    throw "getAlgByKeyAndName: not supported algorithm name: " + e;
}, c.crypto.OID = new function() {
    this.oidhex2name = {
        "2a864886f70d010101": "rsaEncryption",
        "2a8648ce3d0201": "ecPublicKey",
        "2a8648ce380401": "dsa",
        "2a8648ce3d030107": "secp256r1",
        "2b8104001f": "secp192k1",
        "2b81040021": "secp224r1",
        "2b8104000a": "secp256k1",
        "2b81040023": "secp521r1",
        "2b81040022": "secp384r1",
        "2a8648ce380403": "SHA1withDSA",
        "608648016503040301": "SHA224withDSA",
        "608648016503040302": "SHA256withDSA"
    };
}(), 
/*! ecdsa-modified-1.1.0.js (c) Stephan Thomas, Kenji Urushima | github.com/bitcoinjs/bitcoinjs-lib/blob/master/LICENSE
 */
void 0 !== c && c || (c = {}), void 0 !== c.crypto && c.crypto || (c.crypto = {}), 
c.crypto.ECDSA = function(t) {
    var e = new Y();
    this.type = "EC", this.isPrivate = !1, this.isPublic = !1, this.getBigRandom = function(t) {
        return new m(t.bitLength(), e).mod(t.subtract(m.ONE)).add(m.ONE);
    }, this.setNamedCurve = function(t) {
        this.ecparams = c.crypto.ECParameterDB.getByName(t), this.prvKeyHex = null, this.pubKeyHex = null, 
        this.curveName = t;
    }, this.setPrivateKeyHex = function(t) {
        this.isPrivate = !0, this.prvKeyHex = t;
    }, this.setPublicKeyHex = function(t) {
        this.isPublic = !0, this.pubKeyHex = t;
    }, this.getPublicKeyXYHex = function() {
        var t = this.pubKeyHex;
        if ("04" !== t.substr(0, 2)) throw "this method supports uncompressed format(04) only";
        var e = this.ecparams.keylen / 4;
        if (t.length !== 2 + 2 * e) throw "malformed public key hex length";
        var i = {};
        return i.x = t.substr(2, e), i.y = t.substr(2 + e), i;
    }, this.getShortNISTPCurveName = function() {
        var t = this.curveName;
        return "secp256r1" === t || "NIST P-256" === t || "P-256" === t || "prime256v1" === t ? "P-256" : "secp384r1" === t || "NIST P-384" === t || "P-384" === t ? "P-384" : null;
    }, this.generateKeyPairHex = function() {
        var t = this.ecparams.n, e = this.getBigRandom(t), i = this.ecparams.G.multiply(e), n = i.getX().toBigInteger(), r = i.getY().toBigInteger(), s = this.ecparams.keylen / 4, a = ("0000000000" + e.toString(16)).slice(-s), o = "04" + ("0000000000" + n.toString(16)).slice(-s) + ("0000000000" + r.toString(16)).slice(-s);
        return this.setPrivateKeyHex(a), this.setPublicKeyHex(o), {
            ecprvhex: a,
            ecpubhex: o
        };
    }, this.signWithMessageHash = function(t) {
        return this.signHex(t, this.prvKeyHex);
    }, this.signHex = function(t, e) {
        var i = new m(e, 16), n = this.ecparams.n, r = new m(t, 16);
        do {
            var s = this.getBigRandom(n), a = this.ecparams.G.multiply(s).getX().toBigInteger().mod(n);
        } while (a.compareTo(m.ZERO) <= 0);
        var o = s.modInverse(n).multiply(r.add(i.multiply(a))).mod(n);
        return c.crypto.ECDSA.biRSSigToASN1Sig(a, o);
    }, this.sign = function(t, e) {
        var i = e, n = this.ecparams.n, r = m.fromByteArrayUnsigned(t);
        do {
            var s = this.getBigRandom(n), a = this.ecparams.G.multiply(s).getX().toBigInteger().mod(n);
        } while (a.compareTo(m.ZERO) <= 0);
        var o = s.modInverse(n).multiply(r.add(i.multiply(a))).mod(n);
        return this.serializeSig(a, o);
    }, this.verifyWithMessageHash = function(t, e) {
        return this.verifyHex(t, e, this.pubKeyHex);
    }, this.verifyHex = function(t, e, i) {
        var n, r, s, a = c.crypto.ECDSA.parseSigHex(e);
        n = a.r, r = a.s, s = it.decodeFromHex(this.ecparams.curve, i);
        var o = new m(t, 16);
        return this.verifyRaw(o, n, r, s);
    }, this.verify = function(t, e, i) {
        var n, r, s;
        if (Bitcoin.Util.isArray(e)) {
            var o = this.parseSig(e);
            n = o.r, r = o.s;
        } else {
            if ("object" !== a(e) || !e.r || !e.s) throw "Invalid value for signature";
            n = e.r, r = e.s;
        }
        if (i instanceof it) s = i; else {
            if (!Bitcoin.Util.isArray(i)) throw "Invalid format for pubkey value, must be byte array or ECPointFp";
            s = it.decodeFrom(this.ecparams.curve, i);
        }
        var h = m.fromByteArrayUnsigned(t);
        return this.verifyRaw(h, n, r, s);
    }, this.verifyRaw = function(t, e, i, n) {
        var r = this.ecparams.n, s = this.ecparams.G;
        if (e.compareTo(m.ONE) < 0 || e.compareTo(r) >= 0) return !1;
        if (i.compareTo(m.ONE) < 0 || i.compareTo(r) >= 0) return !1;
        var a = i.modInverse(r), o = t.multiply(a).mod(r), h = e.multiply(a).mod(r);
        return s.multiply(o).add(n.multiply(h)).getX().toBigInteger().mod(r).equals(e);
    }, this.serializeSig = function(t, e) {
        var i = t.toByteArraySigned(), n = e.toByteArraySigned(), r = [];
        return r.push(2), r.push(i.length), (r = r.concat(i)).push(2), r.push(n.length), 
        (r = r.concat(n)).unshift(r.length), r.unshift(48), r;
    }, this.parseSig = function(t) {
        var e;
        if (48 != t[0]) throw new Error("Signature not a valid DERSequence");
        if (2 != t[e = 2]) throw new Error("First element in signature must be a DERInteger");
        var i = t.slice(e + 2, e + 2 + t[e + 1]);
        if (2 != t[e += 2 + t[e + 1]]) throw new Error("Second element in signature must be a DERInteger");
        var n = t.slice(e + 2, e + 2 + t[e + 1]);
        return e += 2 + t[e + 1], {
            r: m.fromByteArrayUnsigned(i),
            s: m.fromByteArrayUnsigned(n)
        };
    }, this.parseSigCompact = function(t) {
        if (65 !== t.length) throw "Signature has the wrong length";
        var e = t[0] - 27;
        if (e < 0 || e > 7) throw "Invalid signature type";
        var i = this.ecparams.n;
        return {
            r: m.fromByteArrayUnsigned(t.slice(1, 33)).mod(i),
            s: m.fromByteArrayUnsigned(t.slice(33, 65)).mod(i),
            i: e
        };
    }, this.readPKCS5PrvKeyHex = function(t) {
        var e, i, n, r = ot, s = c.crypto.ECDSA.getName, a = r.getVbyList;
        if (!1 === r.isASN1HEX(t)) throw "not ASN.1 hex string";
        try {
            e = a(t, 0, [ 2, 0 ], "06"), i = a(t, 0, [ 1 ], "04");
            try {
                n = a(t, 0, [ 3, 0 ], "03").substr(2);
            } catch (t) {}
        } catch (t) {
            t = VM2_INTERNAL_STATE_DO_NOT_USE_OR_PROGRAM_WILL_FAIL.handleException(t);
            throw "malformed PKCS#1/5 plain ECC private key";
        }
        if (this.curveName = s(e), void 0 === this.curveName) throw "unsupported curve name";
        this.setNamedCurve(this.curveName), this.setPublicKeyHex(n), this.setPrivateKeyHex(i), 
        this.isPublic = !1;
    }, this.readPKCS8PrvKeyHex = function(t) {
        var e, i, n, r = ot, s = c.crypto.ECDSA.getName, a = r.getVbyList;
        if (!1 === r.isASN1HEX(t)) throw "not ASN.1 hex string";
        try {
            a(t, 0, [ 1, 0 ], "06"), e = a(t, 0, [ 1, 1 ], "06"), i = a(t, 0, [ 2, 0, 1 ], "04");
            try {
                n = a(t, 0, [ 2, 0, 2, 0 ], "03").substr(2);
            } catch (t) {}
        } catch (t) {
            t = VM2_INTERNAL_STATE_DO_NOT_USE_OR_PROGRAM_WILL_FAIL.handleException(t);
            throw "malformed PKCS#8 plain ECC private key";
        }
        if (this.curveName = s(e), void 0 === this.curveName) throw "unsupported curve name";
        this.setNamedCurve(this.curveName), this.setPublicKeyHex(n), this.setPrivateKeyHex(i), 
        this.isPublic = !1;
    }, this.readPKCS8PubKeyHex = function(t) {
        var e, i, n = ot, r = c.crypto.ECDSA.getName, s = n.getVbyList;
        if (!1 === n.isASN1HEX(t)) throw "not ASN.1 hex string";
        try {
            s(t, 0, [ 0, 0 ], "06"), e = s(t, 0, [ 0, 1 ], "06"), i = s(t, 0, [ 1 ], "03").substr(2);
        } catch (t) {
            t = VM2_INTERNAL_STATE_DO_NOT_USE_OR_PROGRAM_WILL_FAIL.handleException(t);
            throw "malformed PKCS#8 ECC public key";
        }
        if (this.curveName = r(e), null === this.curveName) throw "unsupported curve name";
        this.setNamedCurve(this.curveName), this.setPublicKeyHex(i);
    }, this.readCertPubKeyHex = function(t, e) {
        5 !== e && (e = 6);
        var i, n, r = ot, s = c.crypto.ECDSA.getName, a = r.getVbyList;
        if (!1 === r.isASN1HEX(t)) throw "not ASN.1 hex string";
        try {
            i = a(t, 0, [ 0, e, 0, 1 ], "06"), n = a(t, 0, [ 0, e, 1 ], "03").substr(2);
        } catch (t) {
            t = VM2_INTERNAL_STATE_DO_NOT_USE_OR_PROGRAM_WILL_FAIL.handleException(t);
            throw "malformed X.509 certificate ECC public key";
        }
        if (this.curveName = s(i), null === this.curveName) throw "unsupported curve name";
        this.setNamedCurve(this.curveName), this.setPublicKeyHex(n);
    }, void 0 !== t && void 0 !== t.curve && (this.curveName = t.curve), void 0 === this.curveName && (this.curveName = "secp256r1"), 
    this.setNamedCurve(this.curveName), void 0 !== t && (void 0 !== t.prv && this.setPrivateKeyHex(t.prv), 
    void 0 !== t.pub && this.setPublicKeyHex(t.pub));
}, c.crypto.ECDSA.parseSigHex = function(t) {
    var e = c.crypto.ECDSA.parseSigHexInHexRS(t);
    return {
        r: new m(e.r, 16),
        s: new m(e.s, 16)
    };
}, c.crypto.ECDSA.parseSigHexInHexRS = function(t) {
    if ("30" != t.substr(0, 2)) throw "signature is not a ASN.1 sequence";
    var e = ot.getPosArrayOfChildren_AtObj(t, 0);
    if (2 != e.length) throw "number of signature ASN.1 sequence elements seem wrong";
    var i = e[0], n = e[1];
    if ("02" != t.substr(i, 2)) throw "1st item of sequene of signature is not ASN.1 integer";
    if ("02" != t.substr(n, 2)) throw "2nd item of sequene of signature is not ASN.1 integer";
    return {
        r: ot.getHexOfV_AtObj(t, i),
        s: ot.getHexOfV_AtObj(t, n)
    };
}, c.crypto.ECDSA.asn1SigToConcatSig = function(t) {
    var e = c.crypto.ECDSA.parseSigHexInHexRS(t), i = e.r, n = e.s;
    if ("00" == i.substr(0, 2) && i.length / 2 * 8 % 128 == 8 && (i = i.substr(2)), 
    "00" == n.substr(0, 2) && n.length / 2 * 8 % 128 == 8 && (n = n.substr(2)), i.length / 2 * 8 % 128 != 0) throw "unknown ECDSA sig r length error";
    if (n.length / 2 * 8 % 128 != 0) throw "unknown ECDSA sig s length error";
    return i + n;
}, c.crypto.ECDSA.concatSigToASN1Sig = function(t) {
    if (t.length / 2 * 8 % 128 != 0) throw "unknown ECDSA concatinated r-s sig  length error";
    var e = t.substr(0, t.length / 2), i = t.substr(t.length / 2);
    return c.crypto.ECDSA.hexRSSigToASN1Sig(e, i);
}, c.crypto.ECDSA.hexRSSigToASN1Sig = function(t, e) {
    var i = new m(t, 16), n = new m(e, 16);
    return c.crypto.ECDSA.biRSSigToASN1Sig(i, n);
}, c.crypto.ECDSA.biRSSigToASN1Sig = function(t, e) {
    var i = new c.asn1.DERInteger({
        bigint: t
    }), n = new c.asn1.DERInteger({
        bigint: e
    });
    return new c.asn1.DERSequence({
        array: [ i, n ]
    }).getEncodedHex();
}, c.crypto.ECDSA.getName = function(t) {
    return "2a8648ce3d030107" === t ? "secp256r1" : "2b8104000a" === t ? "secp256k1" : "2b81040022" === t ? "secp384r1" : -1 !== "|secp256r1|NIST P-256|P-256|prime256v1|".indexOf(t) ? "secp256r1" : -1 !== "|secp256k1|".indexOf(t) ? "secp256k1" : -1 !== "|secp384r1|NIST P-384|P-384|".indexOf(t) ? "secp384r1" : null;
}, 
/*! ecparam-1.0.0.js (c) 2013 Kenji Urushima | kjur.github.com/jsrsasign/license
 */
void 0 !== c && c || (c = {}), void 0 !== c.crypto && c.crypto || (c.crypto = {}), 
c.crypto.ECParameterDB = new function() {
    var t = {}, e = {};
    function i(t) {
        return new m(t, 16);
    }
    this.getByName = function(i) {
        var n = i;
        if (void 0 !== e[n] && (n = e[i]), void 0 !== t[n]) return t[n];
        throw "unregistered EC curve name: " + n;
    }, this.regist = function(n, r, s, a, o, h, u, c, l, f, d, g) {
        t[n] = {};
        var p = i(s), y = i(a), v = i(o), S = i(h), m = i(u), A = new nt(p, y, v), x = A.decodePointHex("04" + c + l);
        t[n].name = n, t[n].keylen = r, t[n].curve = A, t[n].G = x, t[n].n = S, t[n].h = m, 
        t[n].oid = d, t[n].info = g;
        for (var b = 0; b < f.length; b++) e[f[b]] = n;
    };
}(), c.crypto.ECParameterDB.regist("secp128r1", 128, "FFFFFFFDFFFFFFFFFFFFFFFFFFFFFFFF", "FFFFFFFDFFFFFFFFFFFFFFFFFFFFFFFC", "E87579C11079F43DD824993C2CEE5ED3", "FFFFFFFE0000000075A30D1B9038A115", "1", "161FF7528B899B2D0C28607CA52C5B86", "CF5AC8395BAFEB13C02DA292DDED7A83", [], "", "secp128r1 : SECG curve over a 128 bit prime field"), 
c.crypto.ECParameterDB.regist("secp160k1", 160, "FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEFFFFAC73", "0", "7", "0100000000000000000001B8FA16DFAB9ACA16B6B3", "1", "3B4C382CE37AA192A4019E763036F4F5DD4D7EBB", "938CF935318FDCED6BC28286531733C3F03C4FEE", [], "", "secp160k1 : SECG curve over a 160 bit prime field"), 
c.crypto.ECParameterDB.regist("secp160r1", 160, "FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF7FFFFFFF", "FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF7FFFFFFC", "****************************************", "0100000000000000000001F4C8F927AED3CA752257", "1", "4A96B5688EF573284664698968C38BB913CBFC82", "23A628553168947D59DCC912042351377AC5FB32", [], "", "secp160r1 : SECG curve over a 160 bit prime field"), 
c.crypto.ECParameterDB.regist("secp192k1", 192, "FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEFFFFEE37", "0", "3", "FFFFFFFFFFFFFFFFFFFFFFFE26F2FC170F69466A74DEFD8D", "1", "DB4FF10EC057E9AE26B07D0280B7F4341DA5D1B1EAE06C7D", "9B2F2F6D9C5628A7844163D015BE86344082AA88D95E2F9D", []), 
c.crypto.ECParameterDB.regist("secp192r1", 192, "FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEFFFFFFFFFFFFFFFF", "FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEFFFFFFFFFFFFFFFC", "64210519E59C80E70FA7E9AB72243049FEB8DEECC146B9B1", "FFFFFFFFFFFFFFFFFFFFFFFF99DEF836146BC9B1B4D22831", "1", "188DA80EB03090F67CBF20EB43A18800F4FF0AFD82FF1012", "07192B95FFC8DA78631011ED6B24CDD573F977A11E794811", []), 
c.crypto.ECParameterDB.regist("secp224r1", 224, "FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF000000000000000000000001", "FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEFFFFFFFFFFFFFFFFFFFFFFFE", "B4050A850C04B3ABF54132565044B0B7D7BFD8BA270B39432355FFB4", "FFFFFFFFFFFFFFFFFFFFFFFFFFFF16A2E0B8F03E13DD29455C5C2A3D", "1", "B70E0CBD6BB4BF7F321390B94A03C1D356C21122343280D6115C1D21", "BD376388B5F723FB4C22DFE6CD4375A05A07476444D5819985007E34", []), 
c.crypto.ECParameterDB.regist("secp256k1", 256, "FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEFFFFFC2F", "0", "7", "FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEBAAEDCE6AF48A03BBFD25E8CD0364141", "1", "79BE667EF9DCBBAC55A06295CE870B07029BFCDB2DCE28D959F2815B16F81798", "483ADA7726A3C4655DA4FBFC0E1108A8FD17B448A68554199C47D08FFB10D4B8", []), 
c.crypto.ECParameterDB.regist("secp256r1", 256, "FFFFFFFF00000001000000000000000000000000FFFFFFFFFFFFFFFFFFFFFFFF", "FFFFFFFF00000001000000000000000000000000FFFFFFFFFFFFFFFFFFFFFFFC", "5AC635D8AA3A93E7B3EBBD55769886BC651D06B0CC53B0F63BCE3C3E27D2604B", "FFFFFFFF00000000FFFFFFFFFFFFFFFFBCE6FAADA7179E84F3B9CAC2FC632551", "1", "6B17D1F2E12C4247F8BCE6E563A440F277037D812DEB33A0F4A13945D898C296", "4FE342E2FE1A7F9B8EE7EB4A7C0F9E162BCE33576B315ECECBB6406837BF51F5", [ "NIST P-256", "P-256", "prime256v1" ]), 
c.crypto.ECParameterDB.regist("secp384r1", 384, "FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEFFFFFFFF0000000000000000FFFFFFFF", "FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEFFFFFFFF0000000000000000FFFFFFFC", "B3312FA7E23EE7E4988E056BE3F82D19181D9C6EFE8141120314088F5013875AC656398D8A2ED19D2A85C8EDD3EC2AEF", "FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFC7634D81F4372DDF581A0DB248B0A77AECEC196ACCC52973", "1", "AA87CA22BE8B05378EB1C71EF320AD746E1D3B628BA79B9859F741E082542A385502F25DBF55296C3A545E3872760AB7", "3617de4a96262c6f5d9e98bf9292dc29f8f41dbd289a147ce9da3113b5f0b8c00a60b1ce1d7e819d7a431d7c90ea0e5f", [ "NIST P-384", "P-384" ]), 
c.crypto.ECParameterDB.regist("secp521r1", 521, "1FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF", "1FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFC", "051953EB9618E1C9A1F929A21A0B68540EEA2DA725B99B315F3B8B489918EF109E156193951EC7E937B1652C0BD3BB1BF073573DF883D2C34F1EF451FD46B503F00", "1FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFA51868783BF2F966B7FCC0148F709A5D03BB5C9B8899C47AEBB6FB71E91386409", "1", "C6858E06B70404E9CD9E3ECB662395B4429C648139053FB521F828AF606B4D3DBAA14B5E77EFE75928FE1DC127A2FFA8DE3348B3C1856A429BF97E7E31C2E5BD66", "011839296a789a3bc0045c8a5fb42c7d1bd998f54449579b446817afbd17273e662c97ee72995ef42640c550b9013fad0761353c7086a272c24088be94769fd16650", [ "NIST P-521", "P-521" ]), 
/*! dsa-2.1.0.js (c) 2016-2017 Kenji Urushimma | kjur.github.com/jsrsasign/license
 */
void 0 !== c && c || (c = {}), void 0 !== c.crypto && c.crypto || (c.crypto = {}), 
c.crypto.DSA = function() {
    this.p = null, this.q = null, this.g = null, this.y = null, this.x = null, this.type = "DSA", 
    this.isPrivate = !1, this.isPublic = !1, this.setPrivate = function(t, e, i, n, r) {
        this.isPrivate = !0, this.p = t, this.q = e, this.g = i, this.y = n, this.x = r;
    }, this.setPrivateHex = function(t, e, i, n, r) {
        var s, a, o, h, u;
        s = new m(t, 16), a = new m(e, 16), o = new m(i, 16), h = "string" == typeof n && n.length > 1 ? new m(n, 16) : null, 
        u = new m(r, 16), this.setPrivate(s, a, o, h, u);
    }, this.setPublic = function(t, e, i, n) {
        this.isPublic = !0, this.p = t, this.q = e, this.g = i, this.y = n, this.x = null;
    }, this.setPublicHex = function(t, e, i, n) {
        var r, s, a, o;
        r = new m(t, 16), s = new m(e, 16), a = new m(i, 16), o = new m(n, 16), this.setPublic(r, s, a, o);
    }, this.signWithMessageHash = function(t) {
        var e = this.p, i = this.q, n = this.g, r = (this.y, this.x), s = c.crypto.Util.getRandomBigIntegerMinToMax(m.ONE.add(m.ONE), i.subtract(m.ONE)), a = new m(t.substr(0, i.bitLength() / 4), 16), o = n.modPow(s, e).mod(i), h = s.modInverse(i).multiply(a.add(r.multiply(o))).mod(i);
        return c.asn1.ASN1Util.jsonToASN1HEX({
            seq: [ {
                int: {
                    bigint: o
                }
            }, {
                int: {
                    bigint: h
                }
            } ]
        });
    }, this.verifyWithMessageHash = function(t, e) {
        var i = this.p, n = this.q, r = this.g, s = this.y, a = this.parseASN1Signature(e), o = a[0], h = a[1], u = new m(t.substr(0, n.bitLength() / 4), 16);
        if (m.ZERO.compareTo(o) > 0 || o.compareTo(n) > 0) throw "invalid DSA signature";
        if (m.ZERO.compareTo(h) > 0 || h.compareTo(n) > 0) throw "invalid DSA signature";
        var c = h.modInverse(n), l = u.multiply(c).mod(n), f = o.multiply(c).mod(n);
        return 0 == r.modPow(l, i).multiply(s.modPow(f, i)).mod(i).mod(n).compareTo(o);
    }, this.parseASN1Signature = function(t) {
        try {
            return [ new m(ot.getVbyList(t, 0, [ 0 ], "02"), 16), new m(ot.getVbyList(t, 0, [ 1 ], "02"), 16) ];
        } catch (t) {
            t = VM2_INTERNAL_STATE_DO_NOT_USE_OR_PROGRAM_WILL_FAIL.handleException(t);
            throw "malformed ASN.1 DSA signature";
        }
    }, this.readPKCS5PrvKeyHex = function(t) {
        var e, i, n, r, s, a = ot, o = a.getVbyList;
        if (!1 === a.isASN1HEX(t)) throw "not ASN.1 hex string";
        try {
            e = o(t, 0, [ 1 ], "02"), i = o(t, 0, [ 2 ], "02"), n = o(t, 0, [ 3 ], "02"), r = o(t, 0, [ 4 ], "02"), 
            s = o(t, 0, [ 5 ], "02");
        } catch (t) {
            t = VM2_INTERNAL_STATE_DO_NOT_USE_OR_PROGRAM_WILL_FAIL.handleException(t);
            throw console.log("EXCEPTION:" + t), "malformed PKCS#1/5 plain DSA private key";
        }
        this.setPrivateHex(e, i, n, r, s);
    }, this.readPKCS8PrvKeyHex = function(t) {
        var e, i, n, r, s = ot, a = s.getVbyList;
        if (!1 === s.isASN1HEX(t)) throw "not ASN.1 hex string";
        try {
            e = a(t, 0, [ 1, 1, 0 ], "02"), i = a(t, 0, [ 1, 1, 1 ], "02"), n = a(t, 0, [ 1, 1, 2 ], "02"), 
            r = a(t, 0, [ 2, 0 ], "02");
        } catch (t) {
            t = VM2_INTERNAL_STATE_DO_NOT_USE_OR_PROGRAM_WILL_FAIL.handleException(t);
            throw console.log("EXCEPTION:" + t), "malformed PKCS#8 plain DSA private key";
        }
        this.setPrivateHex(e, i, n, null, r);
    }, this.readPKCS8PubKeyHex = function(t) {
        var e, i, n, r, s = ot, a = s.getVbyList;
        if (!1 === s.isASN1HEX(t)) throw "not ASN.1 hex string";
        try {
            e = a(t, 0, [ 0, 1, 0 ], "02"), i = a(t, 0, [ 0, 1, 1 ], "02"), n = a(t, 0, [ 0, 1, 2 ], "02"), 
            r = a(t, 0, [ 1, 0 ], "02");
        } catch (t) {
            t = VM2_INTERNAL_STATE_DO_NOT_USE_OR_PROGRAM_WILL_FAIL.handleException(t);
            throw console.log("EXCEPTION:" + t), "malformed PKCS#8 DSA public key";
        }
        this.setPublicHex(e, i, n, r);
    }, this.readCertPubKeyHex = function(t, e) {
        var i, n, r, s;
        5 !== e && (e = 6);
        var a = ot, o = a.getVbyList;
        if (!1 === a.isASN1HEX(t)) throw "not ASN.1 hex string";
        try {
            i = o(t, 0, [ 0, e, 0, 1, 0 ], "02"), n = o(t, 0, [ 0, e, 0, 1, 1 ], "02"), r = o(t, 0, [ 0, e, 0, 1, 2 ], "02"), 
            s = o(t, 0, [ 0, e, 1, 0 ], "02");
        } catch (t) {
            t = VM2_INTERNAL_STATE_DO_NOT_USE_OR_PROGRAM_WILL_FAIL.handleException(t);
            throw console.log("EXCEPTION:" + t), "malformed X.509 certificate DSA public key";
        }
        this.setPublicHex(i, n, r, s);
    }
    /*! pkcs5pkey-1.1.0.js (c) 2013-2017 Kenji Urushima | kjur.github.com/jsrsasign/license
 */;
};

var bt = function() {
    var t = function(t, i, n) {
        return e(g.AES, t, i, n);
    }, e = function(t, e, i, n) {
        var r = g.enc.Hex.parse(e), s = g.enc.Hex.parse(i), a = g.enc.Hex.parse(n), o = {};
        o.key = s, o.iv = a, o.ciphertext = r;
        var h = t.decrypt(o, s, {
            iv: a
        });
        return g.enc.Hex.stringify(h);
    }, i = function(t, e, i) {
        return n(g.AES, t, e, i);
    }, n = function(t, e, i, n) {
        var r = g.enc.Hex.parse(e), s = g.enc.Hex.parse(i), a = g.enc.Hex.parse(n), o = t.encrypt(r, s, {
            iv: a
        }), h = g.enc.Hex.parse(o.toString());
        return g.enc.Base64.stringify(h);
    }, r = {
        "AES-256-CBC": {
            proc: t,
            eproc: i,
            keylen: 32,
            ivlen: 16
        },
        "AES-192-CBC": {
            proc: t,
            eproc: i,
            keylen: 24,
            ivlen: 16
        },
        "AES-128-CBC": {
            proc: t,
            eproc: i,
            keylen: 16,
            ivlen: 16
        },
        "DES-EDE3-CBC": {
            proc: function(t, i, n) {
                return e(g.TripleDES, t, i, n);
            },
            eproc: function(t, e, i) {
                return n(g.TripleDES, t, e, i);
            },
            keylen: 24,
            ivlen: 8
        }
    }, s = function(t) {
        var e = {}, i = t.match(new RegExp("DEK-Info: ([^,]+),([0-9A-Fa-f]+)", "m"));
        i && (e.cipher = i[1], e.ivsalt = i[2]);
        var n = t.match(new RegExp("-----BEGIN ([A-Z]+) PRIVATE KEY-----"));
        n && (e.type = n[1]);
        var r = -1, s = 0;
        -1 != t.indexOf("\r\n\r\n") && (r = t.indexOf("\r\n\r\n"), s = 2), -1 != t.indexOf("\n\n") && (r = t.indexOf("\n\n"), 
        s = 1);
        var a = t.indexOf("-----END");
        if (-1 != r && -1 != a) {
            var o = t.substring(r + 2 * s, a - s);
            o = o.replace(/\s+/g, ""), e.data = o;
        }
        return e;
    }, a = function(t, e, i) {
        for (var n = i.substring(0, 16), s = g.enc.Hex.parse(n), a = g.enc.Utf8.parse(e), o = r[t].keylen + r[t].ivlen, h = "", u = null; ;) {
            var c = g.algo.MD5.create();
            if (null != u && c.update(u), c.update(a), c.update(s), u = c.finalize(), (h += g.enc.Hex.stringify(u)).length >= 2 * o) break;
        }
        var l = {};
        return l.keyhex = h.substr(0, 2 * r[t].keylen), l.ivhex = h.substr(2 * r[t].keylen, 2 * r[t].ivlen), 
        l;
    }, o = function(t, e, i, n) {
        var s = g.enc.Base64.parse(t), a = g.enc.Hex.stringify(s);
        return (0, r[e].proc)(a, i, n);
    };
    return {
        version: "1.0.5",
        getHexFromPEM: function(t, e) {
            return ot.pemToHex(t, e);
        },
        getDecryptedKeyHexByKeyIV: function(t, e, i, n) {
            return function(t) {
                return r[t].proc;
            }(e)(t, i, n);
        },
        parsePKCS5PEM: function(t) {
            return s(t);
        },
        getKeyAndUnusedIvByPasscodeAndIvsalt: function(t, e, i) {
            return a(t, e, i);
        },
        decryptKeyB64: function(t, e, i, n) {
            return o(t, e, i, n);
        },
        getDecryptedKeyHex: function(t, e) {
            var i = s(t), n = (i.type, i.cipher), r = i.ivsalt, h = i.data, u = a(n, e, r).keyhex;
            return o(h, n, u, r);
        },
        getRSAKeyFromEncryptedPKCS5PEM: function(t, e) {
            var i = this.getDecryptedKeyHex(t, e), n = new Q();
            return n.readPrivateKeyFromASN1HexString(i), n;
        },
        getEncryptedPKCS5PEMFromPrvKeyHex: function(t, e, i, n) {
            if (void 0 !== i && null != i || (i = "AES-256-CBC"), void 0 === r[i]) throw "PKCS5PKEY unsupported algorithm: " + i;
            void 0 !== n && null != n || (n = function(t) {
                var e = g.lib.WordArray.random(t);
                return g.enc.Hex.stringify(e);
            }(r[i].ivlen).toUpperCase());
            var s = function(t, e, i, n) {
                return (0, r[e].eproc)(t, i, n);
            }(t, i, a(i, e, n).keyhex, n), o = "-----BEGIN RSA PRIVATE KEY-----\r\n";
            return o += "Proc-Type: 4,ENCRYPTED\r\n", o += "DEK-Info: " + i + "," + n + "\r\n", 
            o += "\r\n", o += s.replace(/(.{64})/g, "$1\r\n"), o += "\r\n-----END RSA PRIVATE KEY-----\r\n";
        },
        getEncryptedPKCS5PEMFromRSAKey: function(t, e, i, n) {
            var r = new c.asn1.DERInteger({
                int: 0
            }), s = new c.asn1.DERInteger({
                bigint: t.n
            }), a = new c.asn1.DERInteger({
                int: t.e
            }), o = new c.asn1.DERInteger({
                bigint: t.d
            }), h = new c.asn1.DERInteger({
                bigint: t.p
            }), u = new c.asn1.DERInteger({
                bigint: t.q
            }), l = new c.asn1.DERInteger({
                bigint: t.dmp1
            }), f = new c.asn1.DERInteger({
                bigint: t.dmq1
            }), d = new c.asn1.DERInteger({
                bigint: t.coeff
            }), g = new c.asn1.DERSequence({
                array: [ r, s, a, o, h, u, l, f, d ]
            }).getEncodedHex();
            return this.getEncryptedPKCS5PEMFromPrvKeyHex(g, e, i, n);
        },
        newEncryptedPKCS5PEM: function(t, e, i, n) {
            void 0 !== e && null != e || (e = 1024), void 0 !== i && null != i || (i = "10001"), 
            new Q().generate(e, i);
            return void 0 === n || null == n ? this.getEncryptedPKCS5PEMFromRSAKey(pkey, t) : this.getEncryptedPKCS5PEMFromRSAKey(pkey, t, n);
        },
        getRSAKeyFromPlainPKCS8PEM: function(t) {
            if (t.match(/ENCRYPTED/)) throw "pem shall be not ENCRYPTED";
            var e = ot.pemToHex(t, "PRIVATE KEY");
            return this.getRSAKeyFromPlainPKCS8Hex(e);
        },
        getRSAKeyFromPlainPKCS8Hex: function(t) {
            var e = new Q();
            return e.readPKCS8PrvKeyHex(t), e;
        },
        parseHexOfEncryptedPKCS8: function(t) {
            var e = {}, i = ot.getPosArrayOfChildren_AtObj(t, 0);
            if (2 != i.length) throw "malformed format: SEQUENCE(0).items != 2: " + i.length;
            e.ciphertext = ot.getHexOfV_AtObj(t, i[1]);
            var n = ot.getPosArrayOfChildren_AtObj(t, i[0]);
            if (2 != n.length) throw "malformed format: SEQUENCE(0.0).items != 2: " + n.length;
            if ("2a864886f70d01050d" != ot.getHexOfV_AtObj(t, n[0])) throw "this only supports pkcs5PBES2";
            var r = ot.getPosArrayOfChildren_AtObj(t, n[1]);
            if (2 != n.length) throw "malformed format: SEQUENCE(0.0.1).items != 2: " + r.length;
            var s = ot.getPosArrayOfChildren_AtObj(t, r[1]);
            if (2 != s.length) throw "malformed format: SEQUENCE(*******).items != 2: " + s.length;
            if ("2a864886f70d0307" != ot.getHexOfV_AtObj(t, s[0])) throw "this only supports TripleDES";
            e.encryptionSchemeAlg = "TripleDES", e.encryptionSchemeIV = ot.getHexOfV_AtObj(t, s[1]);
            var a = ot.getPosArrayOfChildren_AtObj(t, r[0]);
            if (2 != a.length) throw "malformed format: SEQUENCE(*******).items != 2: " + a.length;
            if ("2a864886f70d01050c" != ot.getHexOfV_AtObj(t, a[0])) throw "this only supports pkcs5PBKDF2";
            var o = ot.getPosArrayOfChildren_AtObj(t, a[1]);
            if (o.length < 2) throw "malformed format: SEQUENCE(*******.1).items < 2: " + o.length;
            e.pbkdf2Salt = ot.getHexOfV_AtObj(t, o[0]);
            var h = ot.getHexOfV_AtObj(t, o[1]);
            try {
                e.pbkdf2Iter = parseInt(h, 16);
            } catch (t) {
                t = VM2_INTERNAL_STATE_DO_NOT_USE_OR_PROGRAM_WILL_FAIL.handleException(t);
                throw "malformed format pbkdf2Iter: " + h;
            }
            return e;
        },
        getPBKDF2KeyHexFromParam: function(t, e) {
            var i = g.enc.Hex.parse(t.pbkdf2Salt), n = t.pbkdf2Iter, r = g.PBKDF2(e, i, {
                keySize: 6,
                iterations: n
            });
            return g.enc.Hex.stringify(r);
        },
        getPlainPKCS8HexFromEncryptedPKCS8PEM: function(t, e) {
            var i = ot.pemToHex(t, "ENCRYPTED PRIVATE KEY"), n = this.parseHexOfEncryptedPKCS8(i), r = bt.getPBKDF2KeyHexFromParam(n, e), s = {};
            s.ciphertext = g.enc.Hex.parse(n.ciphertext);
            var a = g.enc.Hex.parse(r), o = g.enc.Hex.parse(n.encryptionSchemeIV), h = g.TripleDES.decrypt(s, a, {
                iv: o
            });
            return g.enc.Hex.stringify(h);
        },
        getRSAKeyFromEncryptedPKCS8PEM: function(t, e) {
            var i = this.getPlainPKCS8HexFromEncryptedPKCS8PEM(t, e);
            return this.getRSAKeyFromPlainPKCS8Hex(i);
        },
        getKeyFromEncryptedPKCS8PEM: function(t, e) {
            var i = this.getPlainPKCS8HexFromEncryptedPKCS8PEM(t, e);
            return this.getKeyFromPlainPrivatePKCS8Hex(i);
        },
        parsePlainPrivatePKCS8Hex: function(t) {
            var e = {
                algparam: null
            };
            if ("30" != t.substr(0, 2)) throw "malformed plain PKCS8 private key(code:001)";
            var i = ot.getPosArrayOfChildren_AtObj(t, 0);
            if (3 != i.length) throw "malformed plain PKCS8 private key(code:002)";
            if ("30" != t.substr(i[1], 2)) throw "malformed PKCS8 private key(code:003)";
            var n = ot.getPosArrayOfChildren_AtObj(t, i[1]);
            if (2 != n.length) throw "malformed PKCS8 private key(code:004)";
            if ("06" != t.substr(n[0], 2)) throw "malformed PKCS8 private key(code:005)";
            if (e.algoid = ot.getHexOfV_AtObj(t, n[0]), "06" == t.substr(n[1], 2) && (e.algparam = ot.getHexOfV_AtObj(t, n[1])), 
            "04" != t.substr(i[2], 2)) throw "malformed PKCS8 private key(code:006)";
            return e.keyidx = ot.getStartPosOfV_AtObj(t, i[2]), e;
        },
        getKeyFromPlainPrivatePKCS8PEM: function(t) {
            var e = ot.pemToHex(t, "PRIVATE KEY");
            return this.getKeyFromPlainPrivatePKCS8Hex(e);
        },
        getKeyFromPlainPrivatePKCS8Hex: function(t) {
            var e, i = this.parsePlainPrivatePKCS8Hex(t);
            if ("2a864886f70d010101" == i.algoid) e = new Q(); else if ("2a8648ce380401" == i.algoid) e = new c.crypto.DSA(); else {
                if ("2a8648ce3d0201" != i.algoid) throw "unsupported private key algorithm";
                e = new c.crypto.ECDSA();
            }
            return e.readPKCS8PrvKeyHex(t), e;
        },
        getRSAKeyFromPublicPKCS8PEM: function(t) {
            var e = ot.pemToHex(t, "PUBLIC KEY");
            return this.getRSAKeyFromPublicPKCS8Hex(e);
        },
        getKeyFromPublicPKCS8PEM: function(t) {
            var e = ot.pemToHex(t, "PUBLIC KEY");
            return this.getKeyFromPublicPKCS8Hex(e);
        },
        getKeyFromPublicPKCS8Hex: function(t) {
            var e, i = ot.getVbyList(h, 0, [ 0, 0 ], "06");
            if ("2a864886f70d010101" === i) e = new Q(); else if ("2a8648ce380401" === i) e = new c.crypto.DSA(); else {
                if ("2a8648ce3d0201" !== i) throw "unsupported PKCS#8 public key hex";
                e = new c.crypto.ECDSA();
            }
            return e.readPKCS8PubKeyHex(h), e;
        },
        parsePublicRawRSAKeyHex: function(t) {
            var e = {};
            if ("30" != t.substr(0, 2)) throw "malformed RSA key(code:001)";
            var i = ot.getPosArrayOfChildren_AtObj(t, 0);
            if (2 != i.length) throw "malformed RSA key(code:002)";
            if ("02" != t.substr(i[0], 2)) throw "malformed RSA key(code:003)";
            if (e.n = ot.getHexOfV_AtObj(t, i[0]), "02" != t.substr(i[1], 2)) throw "malformed RSA key(code:004)";
            return e.e = ot.getHexOfV_AtObj(t, i[1]), e;
        },
        parsePrivateRawRSAKeyHexAtObj: function(t, e) {
            var i = e.keyidx;
            if ("30" != t.substr(i, 2)) throw "malformed RSA private key(code:001)";
            var n = ot.getPosArrayOfChildren_AtObj(t, i);
            if (9 != n.length) throw "malformed RSA private key(code:002)";
            e.key = {}, e.key.n = ot.getHexOfV_AtObj(t, n[1]), e.key.e = ot.getHexOfV_AtObj(t, n[2]), 
            e.key.d = ot.getHexOfV_AtObj(t, n[3]), e.key.p = ot.getHexOfV_AtObj(t, n[4]), e.key.q = ot.getHexOfV_AtObj(t, n[5]), 
            e.key.dp = ot.getHexOfV_AtObj(t, n[6]), e.key.dq = ot.getHexOfV_AtObj(t, n[7]), 
            e.key.co = ot.getHexOfV_AtObj(t, n[8]);
        },
        parsePrivateRawECKeyHexAtObj: function(t, e) {
            var i = e.keyidx;
            if ("30" != t.substr(i, 2)) throw "malformed ECC private key(code:001)";
            var n = ot.getPosArrayOfChildren_AtObj(t, i);
            if (3 != n.length) throw "malformed ECC private key(code:002)";
            if ("04" != t.substr(n[1], 2)) throw "malformed ECC private key(code:003)";
            e.key = ot.getHexOfV_AtObj(t, n[1]);
        },
        parsePublicPKCS8Hex: function(t) {
            var e = {
                algparam: null
            }, i = ot.getPosArrayOfChildren_AtObj(t, 0);
            if (2 != i.length) throw "outer DERSequence shall have 2 elements: " + i.length;
            var n = i[0];
            if ("30" != t.substr(n, 2)) throw "malformed PKCS8 public key(code:001)";
            var r = ot.getPosArrayOfChildren_AtObj(t, n);
            if (2 != r.length) throw "malformed PKCS8 public key(code:002)";
            if ("06" != t.substr(r[0], 2)) throw "malformed PKCS8 public key(code:003)";
            if (e.algoid = ot.getHexOfV_AtObj(t, r[0]), "06" == t.substr(r[1], 2) && (e.algparam = ot.getHexOfV_AtObj(t, r[1])), 
            "03" != t.substr(i[1], 2)) throw "malformed PKCS8 public key(code:004)";
            return e.key = ot.getHexOfV_AtObj(t, i[1]).substr(2), e;
        },
        getRSAKeyFromPublicPKCS8Hex: function(t) {
            var e = new Q();
            return e.readPKCS8PubKeyHex(t), e;
        }
    };
}(), Ft = function() {
    var t = function(t, i, n) {
        return e(g.AES, t, i, n);
    }, e = function(t, e, i, n) {
        var r = g.enc.Hex.parse(e), s = g.enc.Hex.parse(i), a = g.enc.Hex.parse(n), o = {};
        o.key = s, o.iv = a, o.ciphertext = r;
        var h = t.decrypt(o, s, {
            iv: a
        });
        return g.enc.Hex.stringify(h);
    }, i = function(t, e, i) {
        return n(g.AES, t, e, i);
    }, n = function(t, e, i, n) {
        var r = g.enc.Hex.parse(e), s = g.enc.Hex.parse(i), a = g.enc.Hex.parse(n), o = t.encrypt(r, s, {
            iv: a
        }), h = g.enc.Hex.parse(o.toString());
        return g.enc.Base64.stringify(h);
    }, r = {
        "AES-256-CBC": {
            proc: t,
            eproc: i,
            keylen: 32,
            ivlen: 16
        },
        "AES-192-CBC": {
            proc: t,
            eproc: i,
            keylen: 24,
            ivlen: 16
        },
        "AES-128-CBC": {
            proc: t,
            eproc: i,
            keylen: 16,
            ivlen: 16
        },
        "DES-EDE3-CBC": {
            proc: function(t, i, n) {
                return e(g.TripleDES, t, i, n);
            },
            eproc: function(t, e, i) {
                return n(g.TripleDES, t, e, i);
            },
            keylen: 24,
            ivlen: 8
        },
        "DES-CBC": {
            proc: function(t, i, n) {
                return e(g.DES, t, i, n);
            },
            eproc: function(t, e, i) {
                return n(g.DES, t, e, i);
            },
            keylen: 8,
            ivlen: 8
        }
    }, s = function(t) {
        var e = {}, i = t.match(new RegExp("DEK-Info: ([^,]+),([0-9A-Fa-f]+)", "m"));
        i && (e.cipher = i[1], e.ivsalt = i[2]);
        var n = t.match(new RegExp("-----BEGIN ([A-Z]+) PRIVATE KEY-----"));
        n && (e.type = n[1]);
        var r = -1, s = 0;
        -1 != t.indexOf("\r\n\r\n") && (r = t.indexOf("\r\n\r\n"), s = 2), -1 != t.indexOf("\n\n") && (r = t.indexOf("\n\n"), 
        s = 1);
        var a = t.indexOf("-----END");
        if (-1 != r && -1 != a) {
            var o = t.substring(r + 2 * s, a - s);
            o = o.replace(/\s+/g, ""), e.data = o;
        }
        return e;
    }, a = function(t, e, i) {
        for (var n = i.substring(0, 16), s = g.enc.Hex.parse(n), a = g.enc.Utf8.parse(e), o = r[t].keylen + r[t].ivlen, h = "", u = null; ;) {
            var c = g.algo.MD5.create();
            if (null != u && c.update(u), c.update(a), c.update(s), u = c.finalize(), (h += g.enc.Hex.stringify(u)).length >= 2 * o) break;
        }
        var l = {};
        return l.keyhex = h.substr(0, 2 * r[t].keylen), l.ivhex = h.substr(2 * r[t].keylen, 2 * r[t].ivlen), 
        l;
    }, o = function(t, e, i, n) {
        var s = g.enc.Base64.parse(t), a = g.enc.Hex.stringify(s);
        return (0, r[e].proc)(a, i, n);
    };
    return {
        version: "1.0.0",
        getHexFromPEM: function(t, e) {
            return ot.pemToHex(t, e);
        },
        getDecryptedKeyHexByKeyIV: function(t, e, i, n) {
            return function(t) {
                return r[t].proc;
            }(e)(t, i, n);
        },
        parsePKCS5PEM: function(t) {
            return s(t);
        },
        getKeyAndUnusedIvByPasscodeAndIvsalt: function(t, e, i) {
            return a(t, e, i);
        },
        decryptKeyB64: function(t, e, i, n) {
            return o(t, e, i, n);
        },
        getDecryptedKeyHex: function(t, e) {
            var i = s(t), n = (i.type, i.cipher), r = i.ivsalt, h = i.data, u = a(n, e, r).keyhex;
            return o(h, n, u, r);
        },
        getRSAKeyFromEncryptedPKCS5PEM: function(t, e) {
            var i = this.getDecryptedKeyHex(t, e), n = new Q();
            return n.readPrivateKeyFromASN1HexString(i), n;
        },
        getEncryptedPKCS5PEMFromPrvKeyHex: function(t, e, i, n, s) {
            var o = "";
            if (void 0 !== n && null != n || (n = "AES-256-CBC"), void 0 === r[n]) throw "KEYUTIL unsupported algorithm: " + n;
            void 0 !== s && null != s || (s = function(t) {
                var e = g.lib.WordArray.random(t);
                return g.enc.Hex.stringify(e);
            }(r[n].ivlen).toUpperCase());
            var h = function(t, e, i, n) {
                return (0, r[e].eproc)(t, i, n);
            }(e, n, a(n, i, s).keyhex, s);
            o = "-----BEGIN " + t + " PRIVATE KEY-----\r\n";
            return o += "Proc-Type: 4,ENCRYPTED\r\n", o += "DEK-Info: " + n + "," + s + "\r\n", 
            o += "\r\n", o += h.replace(/(.{64})/g, "$1\r\n"), o += "\r\n-----END " + t + " PRIVATE KEY-----\r\n";
        },
        getEncryptedPKCS5PEMFromRSAKey: function(t, e, i, n) {
            var r = new c.asn1.DERInteger({
                int: 0
            }), s = new c.asn1.DERInteger({
                bigint: t.n
            }), a = new c.asn1.DERInteger({
                int: t.e
            }), o = new c.asn1.DERInteger({
                bigint: t.d
            }), h = new c.asn1.DERInteger({
                bigint: t.p
            }), u = new c.asn1.DERInteger({
                bigint: t.q
            }), l = new c.asn1.DERInteger({
                bigint: t.dmp1
            }), f = new c.asn1.DERInteger({
                bigint: t.dmq1
            }), d = new c.asn1.DERInteger({
                bigint: t.coeff
            }), g = new c.asn1.DERSequence({
                array: [ r, s, a, o, h, u, l, f, d ]
            }).getEncodedHex();
            return this.getEncryptedPKCS5PEMFromPrvKeyHex("RSA", g, e, i, n);
        },
        newEncryptedPKCS5PEM: function(t, e, i, n) {
            void 0 !== e && null != e || (e = 1024), void 0 !== i && null != i || (i = "10001");
            var r = new Q();
            r.generate(e, i);
            return void 0 === n || null == n ? this.getEncryptedPKCS5PEMFromRSAKey(r, t) : this.getEncryptedPKCS5PEMFromRSAKey(r, t, n);
        },
        getRSAKeyFromPlainPKCS8PEM: function(t) {
            if (t.match(/ENCRYPTED/)) throw "pem shall be not ENCRYPTED";
            var e = ot.pemToHex(t, "PRIVATE KEY");
            return this.getRSAKeyFromPlainPKCS8Hex(e);
        },
        getRSAKeyFromPlainPKCS8Hex: function(t) {
            var e = new Q();
            return e.readPKCS8PrvKeyHex(t), e;
        },
        parseHexOfEncryptedPKCS8: function(t) {
            var e = {}, i = ot.getPosArrayOfChildren_AtObj(t, 0);
            if (2 != i.length) throw "malformed format: SEQUENCE(0).items != 2: " + i.length;
            e.ciphertext = ot.getHexOfV_AtObj(t, i[1]);
            var n = ot.getPosArrayOfChildren_AtObj(t, i[0]);
            if (2 != n.length) throw "malformed format: SEQUENCE(0.0).items != 2: " + n.length;
            if ("2a864886f70d01050d" != ot.getHexOfV_AtObj(t, n[0])) throw "this only supports pkcs5PBES2";
            var r = ot.getPosArrayOfChildren_AtObj(t, n[1]);
            if (2 != n.length) throw "malformed format: SEQUENCE(0.0.1).items != 2: " + r.length;
            var s = ot.getPosArrayOfChildren_AtObj(t, r[1]);
            if (2 != s.length) throw "malformed format: SEQUENCE(*******).items != 2: " + s.length;
            if ("2a864886f70d0307" != ot.getHexOfV_AtObj(t, s[0])) throw "this only supports TripleDES";
            e.encryptionSchemeAlg = "TripleDES", e.encryptionSchemeIV = ot.getHexOfV_AtObj(t, s[1]);
            var a = ot.getPosArrayOfChildren_AtObj(t, r[0]);
            if (2 != a.length) throw "malformed format: SEQUENCE(*******).items != 2: " + a.length;
            if ("2a864886f70d01050c" != ot.getHexOfV_AtObj(t, a[0])) throw "this only supports pkcs5PBKDF2";
            var o = ot.getPosArrayOfChildren_AtObj(t, a[1]);
            if (o.length < 2) throw "malformed format: SEQUENCE(*******.1).items < 2: " + o.length;
            e.pbkdf2Salt = ot.getHexOfV_AtObj(t, o[0]);
            var h = ot.getHexOfV_AtObj(t, o[1]);
            try {
                e.pbkdf2Iter = parseInt(h, 16);
            } catch (t) {
                t = VM2_INTERNAL_STATE_DO_NOT_USE_OR_PROGRAM_WILL_FAIL.handleException(t);
                throw "malformed format pbkdf2Iter: " + h;
            }
            return e;
        },
        getPBKDF2KeyHexFromParam: function(t, e) {
            var i = g.enc.Hex.parse(t.pbkdf2Salt), n = t.pbkdf2Iter, r = g.PBKDF2(e, i, {
                keySize: 6,
                iterations: n
            });
            return g.enc.Hex.stringify(r);
        },
        getPlainPKCS8HexFromEncryptedPKCS8PEM: function(t, e) {
            var i = ot.pemToHex(t, "ENCRYPTED PRIVATE KEY"), n = this.parseHexOfEncryptedPKCS8(i), r = Ft.getPBKDF2KeyHexFromParam(n, e), s = {};
            s.ciphertext = g.enc.Hex.parse(n.ciphertext);
            var a = g.enc.Hex.parse(r), o = g.enc.Hex.parse(n.encryptionSchemeIV), h = g.TripleDES.decrypt(s, a, {
                iv: o
            });
            return g.enc.Hex.stringify(h);
        },
        getRSAKeyFromEncryptedPKCS8PEM: function(t, e) {
            var i = this.getPlainPKCS8HexFromEncryptedPKCS8PEM(t, e);
            return this.getRSAKeyFromPlainPKCS8Hex(i);
        },
        getKeyFromEncryptedPKCS8PEM: function(t, e) {
            var i = this.getPlainPKCS8HexFromEncryptedPKCS8PEM(t, e);
            return this.getKeyFromPlainPrivatePKCS8Hex(i);
        },
        parsePlainPrivatePKCS8Hex: function(t) {
            var e = {
                algparam: null
            };
            if ("30" != t.substr(0, 2)) throw "malformed plain PKCS8 private key(code:001)";
            var i = ot.getPosArrayOfChildren_AtObj(t, 0);
            if (3 != i.length) throw "malformed plain PKCS8 private key(code:002)";
            if ("30" != t.substr(i[1], 2)) throw "malformed PKCS8 private key(code:003)";
            var n = ot.getPosArrayOfChildren_AtObj(t, i[1]);
            if (2 != n.length) throw "malformed PKCS8 private key(code:004)";
            if ("06" != t.substr(n[0], 2)) throw "malformed PKCS8 private key(code:005)";
            if (e.algoid = ot.getHexOfV_AtObj(t, n[0]), "06" == t.substr(n[1], 2) && (e.algparam = ot.getHexOfV_AtObj(t, n[1])), 
            "04" != t.substr(i[2], 2)) throw "malformed PKCS8 private key(code:006)";
            return e.keyidx = ot.getStartPosOfV_AtObj(t, i[2]), e;
        },
        getKeyFromPlainPrivatePKCS8PEM: function(t) {
            var e = ot.pemToHex(t, "PRIVATE KEY");
            return this.getKeyFromPlainPrivatePKCS8Hex(e);
        },
        getKeyFromPlainPrivatePKCS8Hex: function(t) {
            var e, i = this.parsePlainPrivatePKCS8Hex(t);
            if ("2a864886f70d010101" == i.algoid) e = new Q(); else if ("2a8648ce380401" == i.algoid) e = new c.crypto.DSA(); else {
                if ("2a8648ce3d0201" != i.algoid) throw "unsupported private key algorithm";
                e = new c.crypto.ECDSA();
            }
            return e.readPKCS8PrvKeyHex(t), e;
        },
        getRSAKeyFromPublicPKCS8PEM: function(t) {
            var e = ot.pemToHex(t, "PUBLIC KEY");
            return this.getRSAKeyFromPublicPKCS8Hex(e);
        },
        getKeyFromPublicPKCS8PEM: function(t) {
            var e = ot.pemToHex(t, "PUBLIC KEY");
            return this.getKeyFromPublicPKCS8Hex(e);
        },
        getKeyFromPublicPKCS8Hex: function(t) {
            var e, i = ot.getVbyList(t, 0, [ 0, 0 ], "06");
            if ("2a864886f70d010101" === i) e = new Q(); else if ("2a8648ce380401" === i) e = new c.crypto.DSA(); else {
                if ("2a8648ce3d0201" !== i) throw "unsupported PKCS#8 public key hex";
                e = new c.crypto.ECDSA();
            }
            return e.readPKCS8PubKeyHex(t), e;
        },
        parsePublicRawRSAKeyHex: function(t) {
            var e = {};
            if ("30" != t.substr(0, 2)) throw "malformed RSA key(code:001)";
            var i = ot.getPosArrayOfChildren_AtObj(t, 0);
            if (2 != i.length) throw "malformed RSA key(code:002)";
            if ("02" != t.substr(i[0], 2)) throw "malformed RSA key(code:003)";
            if (e.n = ot.getHexOfV_AtObj(t, i[0]), "02" != t.substr(i[1], 2)) throw "malformed RSA key(code:004)";
            return e.e = ot.getHexOfV_AtObj(t, i[1]), e;
        },
        parsePrivateRawRSAKeyHexAtObj: function(t, e) {
            var i = ot, n = i.getHexOfV_AtObj, r = i.getDecendantIndexByNthList(t, 0, [ 2, 0 ]), s = i.getPosArrayOfChildren_AtObj(t, r);
            if (9 !== s.length) throw "malformed PKCS#8 plain RSA private key";
            e.key = {}, e.key.n = n(t, s[1]), e.key.e = n(t, s[2]), e.key.d = n(t, s[3]), e.key.p = n(t, s[4]), 
            e.key.q = n(t, s[5]), e.key.dp = n(t, s[6]), e.key.dq = n(t, s[7]), e.key.co = n(t, s[8]);
        },
        parsePrivateRawECKeyHexAtObj: function(t, e) {
            e.keyidx;
            var i = new c.crypto.ECDSA();
            i.readPKCS8PrvKeyHex(t), e.key = i.prvKeyHex, e.pubkey = i.pubKeyHex;
        },
        parsePublicPKCS8Hex: function(t) {
            var e = {
                algparam: null
            }, i = ot.getPosArrayOfChildren_AtObj(t, 0);
            if (2 != i.length) throw "outer DERSequence shall have 2 elements: " + i.length;
            var n = i[0];
            if ("30" != t.substr(n, 2)) throw "malformed PKCS8 public key(code:001)";
            var r = ot.getPosArrayOfChildren_AtObj(t, n);
            if (2 != r.length) throw "malformed PKCS8 public key(code:002)";
            if ("06" != t.substr(r[0], 2)) throw "malformed PKCS8 public key(code:003)";
            if (e.algoid = ot.getHexOfV_AtObj(t, r[0]), "06" == t.substr(r[1], 2) ? e.algparam = ot.getHexOfV_AtObj(t, r[1]) : "30" == t.substr(r[1], 2) && (e.algparam = {}, 
            e.algparam.p = ot.getVbyList(t, r[1], [ 0 ], "02"), e.algparam.q = ot.getVbyList(t, r[1], [ 1 ], "02"), 
            e.algparam.g = ot.getVbyList(t, r[1], [ 2 ], "02")), "03" != t.substr(i[1], 2)) throw "malformed PKCS8 public key(code:004)";
            return e.key = ot.getHexOfV_AtObj(t, i[1]).substr(2), e;
        },
        getRSAKeyFromPublicPKCS8Hex: function(t) {
            var e = new Q();
            return e.readPKCS8PubKeyHex(t), e;
        }
    };
}();

/*! keyutil-1.0.15.js (c) 2013-2017 Kenji Urushima | kjur.github.com/jsrsasign/license
 */ Ft.getKey = function(t, e, i) {
    if (t instanceof Q) return t;
    if (void 0 !== c.crypto.ECDSA && t instanceof c.crypto.ECDSA) return t;
    if (void 0 !== c.crypto.DSA && t instanceof c.crypto.DSA) return t;
    if (void 0 !== t.curve && void 0 !== t.xy && void 0 === t.d) return new c.crypto.ECDSA({
        pub: t.xy,
        curve: t.curve
    });
    if (void 0 !== t.curve && void 0 !== t.d) return new c.crypto.ECDSA({
        prv: t.d,
        curve: t.curve
    });
    if (void 0 === t.kty && void 0 !== t.n && void 0 !== t.e && void 0 === t.d) return (v = new Q()).setPublic(t.n, t.e), 
    v;
    if (void 0 === t.kty && void 0 !== t.n && void 0 !== t.e && void 0 !== t.d && void 0 !== t.p && void 0 !== t.q && void 0 !== t.dp && void 0 !== t.dq && void 0 !== t.co && void 0 === t.qi) return (v = new Q()).setPrivateEx(t.n, t.e, t.d, t.p, t.q, t.dp, t.dq, t.co), 
    v;
    if (void 0 === t.kty && void 0 !== t.n && void 0 !== t.e && void 0 !== t.d && void 0 === t.p) return (v = new Q()).setPrivate(t.n, t.e, t.d), 
    v;
    if (void 0 !== t.p && void 0 !== t.q && void 0 !== t.g && void 0 !== t.y && void 0 === t.x) return (v = new c.crypto.DSA()).setPublic(t.p, t.q, t.g, t.y), 
    v;
    if (void 0 !== t.p && void 0 !== t.q && void 0 !== t.g && void 0 !== t.y && void 0 !== t.x) return (v = new c.crypto.DSA()).setPrivate(t.p, t.q, t.g, t.y, t.x), 
    v;
    if ("RSA" === t.kty && void 0 !== t.n && void 0 !== t.e && void 0 === t.d) return (v = new Q()).setPublic(dt(t.n), dt(t.e)), 
    v;
    if ("RSA" === t.kty && void 0 !== t.n && void 0 !== t.e && void 0 !== t.d && void 0 !== t.p && void 0 !== t.q && void 0 !== t.dp && void 0 !== t.dq && void 0 !== t.qi) return (v = new Q()).setPrivateEx(dt(t.n), dt(t.e), dt(t.d), dt(t.p), dt(t.q), dt(t.dp), dt(t.dq), dt(t.qi)), 
    v;
    if ("RSA" === t.kty && void 0 !== t.n && void 0 !== t.e && void 0 !== t.d) return (v = new Q()).setPrivate(dt(t.n), dt(t.e), dt(t.d)), 
    v;
    if ("EC" === t.kty && void 0 !== t.crv && void 0 !== t.x && void 0 !== t.y && void 0 === t.d) {
        var n = (y = new c.crypto.ECDSA({
            curve: t.crv
        })).ecparams.keylen / 4, r = "04" + ("0000000000" + dt(t.x)).slice(-n) + ("0000000000" + dt(t.y)).slice(-n);
        return y.setPublicKeyHex(r), y;
    }
    if ("EC" === t.kty && void 0 !== t.crv && void 0 !== t.x && void 0 !== t.y && void 0 !== t.d) {
        n = (y = new c.crypto.ECDSA({
            curve: t.crv
        })).ecparams.keylen / 4, r = "04" + ("0000000000" + dt(t.x)).slice(-n) + ("0000000000" + dt(t.y)).slice(-n);
        var s = ("0000000000" + dt(t.d)).slice(-n);
        return y.setPublicKeyHex(r), y.setPrivateKeyHex(s), y;
    }
    if ("pkcs5prv" === i) {
        var a, o = t;
        if (9 === (a = ot.getPosArrayOfChildren_AtObj(o, 0)).length) (v = new Q()).readPrivateKeyFromASN1HexString(t); else if (6 === a.length) (v = new c.crypto.DSA()).readPKCS5PrvKeyHex(o); else {
            if (!(a.length > 2 && "04" === o.substr(a[1], 2))) throw "unsupported PKCS#1/5 hexadecimal key";
            (v = new c.crypto.ECDSA()).readPKCS5PrvKeyHex(o);
        }
        return v;
    }
    if ("pkcs8prv" === i) return v = Ft.getKeyFromPlainPrivatePKCS8Hex(t);
    if ("pkcs8pub" === i) return Ft.getKeyFromPublicPKCS8Hex(t);
    if ("x509pub" === i) return Nt.getPublicKeyFromCertHex(t);
    if (-1 != t.indexOf("-END CERTIFICATE-", 0) || -1 != t.indexOf("-END X509 CERTIFICATE-", 0) || -1 != t.indexOf("-END TRUSTED CERTIFICATE-", 0)) return Nt.getPublicKeyFromCertPEM(t);
    if (-1 != t.indexOf("-END PUBLIC KEY-")) return Ft.getKeyFromPublicPKCS8PEM(t);
    if (-1 != t.indexOf("-END RSA PRIVATE KEY-") && -1 == t.indexOf("4,ENCRYPTED")) {
        var h = ot.pemToHex(t, "RSA PRIVATE KEY");
        return Ft.getKey(h, null, "pkcs5prv");
    }
    if (-1 != t.indexOf("-END DSA PRIVATE KEY-") && -1 == t.indexOf("4,ENCRYPTED")) {
        var u = ot.pemToHex(t, "DSA PRIVATE KEY"), l = ot.getVbyList(u, 0, [ 1 ], "02"), f = ot.getVbyList(u, 0, [ 2 ], "02"), d = ot.getVbyList(u, 0, [ 3 ], "02"), g = ot.getVbyList(u, 0, [ 4 ], "02"), p = ot.getVbyList(u, 0, [ 5 ], "02");
        return (v = new c.crypto.DSA()).setPrivate(new m(l, 16), new m(f, 16), new m(d, 16), new m(g, 16), new m(p, 16)), 
        v;
    }
    if (-1 != t.indexOf("-END PRIVATE KEY-")) return Ft.getKeyFromPlainPrivatePKCS8PEM(t);
    if (-1 != t.indexOf("-END RSA PRIVATE KEY-") && -1 != t.indexOf("4,ENCRYPTED")) return Ft.getRSAKeyFromEncryptedPKCS5PEM(t, e);
    if (-1 != t.indexOf("-END EC PRIVATE KEY-") && -1 != t.indexOf("4,ENCRYPTED")) {
        u = Ft.getDecryptedKeyHex(t, e);
        var y, v = ot.getVbyList(u, 0, [ 1 ], "04"), S = ot.getVbyList(u, 0, [ 2, 0 ], "06"), A = ot.getVbyList(u, 0, [ 3, 0 ], "03").substr(2), x = "";
        if (void 0 === c.crypto.OID.oidhex2name[S]) throw "undefined OID(hex) in KJUR.crypto.OID: " + S;
        return x = c.crypto.OID.oidhex2name[S], (y = new c.crypto.ECDSA({
            curve: x
        })).setPublicKeyHex(A), y.setPrivateKeyHex(v), y.isPublic = !1, y;
    }
    if (-1 != t.indexOf("-END DSA PRIVATE KEY-") && -1 != t.indexOf("4,ENCRYPTED")) {
        u = Ft.getDecryptedKeyHex(t, e), l = ot.getVbyList(u, 0, [ 1 ], "02"), f = ot.getVbyList(u, 0, [ 2 ], "02"), 
        d = ot.getVbyList(u, 0, [ 3 ], "02"), g = ot.getVbyList(u, 0, [ 4 ], "02"), p = ot.getVbyList(u, 0, [ 5 ], "02");
        return (v = new c.crypto.DSA()).setPrivate(new m(l, 16), new m(f, 16), new m(d, 16), new m(g, 16), new m(p, 16)), 
        v;
    }
    if (-1 != t.indexOf("-END ENCRYPTED PRIVATE KEY-")) return Ft.getKeyFromEncryptedPKCS8PEM(t, e);
    throw "not supported argument";
}, Ft.generateKeypair = function(t, e) {
    if ("RSA" == t) {
        var i = e;
        (a = new Q()).generate(i, "10001"), a.isPrivate = !0, a.isPublic = !0;
        var n = new Q(), r = a.n.toString(16), s = a.e.toString(16);
        return n.setPublic(r, s), n.isPrivate = !1, n.isPublic = !0, (o = {}).prvKeyObj = a, 
        o.pubKeyObj = n, o;
    }
    if ("EC" == t) {
        var a, o, h = e, u = new c.crypto.ECDSA({
            curve: h
        }).generateKeyPairHex();
        return (a = new c.crypto.ECDSA({
            curve: h
        })).setPublicKeyHex(u.ecpubhex), a.setPrivateKeyHex(u.ecprvhex), a.isPrivate = !0, 
        a.isPublic = !1, (n = new c.crypto.ECDSA({
            curve: h
        })).setPublicKeyHex(u.ecpubhex), n.isPrivate = !1, n.isPublic = !0, (o = {}).prvKeyObj = a, 
        o.pubKeyObj = n, o;
    }
    throw "unknown algorithm: " + t;
}, Ft.getPEM = function(t, e, i, n, r) {
    var s = c.asn1, a = c.crypto;
    function o(t) {
        return c.asn1.ASN1Util.newObject({
            seq: [ {
                int: 0
            }, {
                int: {
                    bigint: t.n
                }
            }, {
                int: t.e
            }, {
                int: {
                    bigint: t.d
                }
            }, {
                int: {
                    bigint: t.p
                }
            }, {
                int: {
                    bigint: t.q
                }
            }, {
                int: {
                    bigint: t.dmp1
                }
            }, {
                int: {
                    bigint: t.dmq1
                }
            }, {
                int: {
                    bigint: t.coeff
                }
            } ]
        });
    }
    function h(t) {
        return c.asn1.ASN1Util.newObject({
            seq: [ {
                int: 1
            }, {
                octstr: {
                    hex: t.prvKeyHex
                }
            }, {
                tag: [ "a0", !0, {
                    oid: {
                        name: t.curveName
                    }
                } ]
            }, {
                tag: [ "a1", !0, {
                    bitstr: {
                        hex: "00" + t.pubKeyHex
                    }
                } ]
            } ]
        });
    }
    function u(t) {
        return c.asn1.ASN1Util.newObject({
            seq: [ {
                int: 0
            }, {
                int: {
                    bigint: t.p
                }
            }, {
                int: {
                    bigint: t.q
                }
            }, {
                int: {
                    bigint: t.g
                }
            }, {
                int: {
                    bigint: t.y
                }
            }, {
                int: {
                    bigint: t.x
                }
            } ]
        });
    }
    if ((t instanceof Q || void 0 !== a.DSA && t instanceof a.DSA || void 0 !== a.ECDSA && t instanceof a.ECDSA) && 1 == t.isPublic && (void 0 === e || "PKCS8PUB" == e)) {
        var l = new c.asn1.x509.SubjectPublicKeyInfo(t).getEncodedHex();
        return s.ASN1Util.getPEMStringFromHex(l, "PUBLIC KEY");
    }
    if ("PKCS1PRV" == e && t instanceof Q && (void 0 === i || null == i) && 1 == t.isPrivate) {
        l = o(t).getEncodedHex();
        return s.ASN1Util.getPEMStringFromHex(l, "RSA PRIVATE KEY");
    }
    if ("PKCS1PRV" == e && t instanceof c.crypto.ECDSA && (void 0 === i || null == i) && 1 == t.isPrivate) {
        var f = new c.asn1.DERObjectIdentifier({
            name: t.curveName
        }).getEncodedHex(), d = h(t).getEncodedHex(), p = "";
        return p += s.ASN1Util.getPEMStringFromHex(f, "EC PARAMETERS"), p += s.ASN1Util.getPEMStringFromHex(d, "EC PRIVATE KEY");
    }
    if ("PKCS1PRV" == e && void 0 !== c.crypto.DSA && t instanceof c.crypto.DSA && (void 0 === i || null == i) && 1 == t.isPrivate) {
        l = u(t).getEncodedHex();
        return s.ASN1Util.getPEMStringFromHex(l, "DSA PRIVATE KEY");
    }
    if ("PKCS5PRV" == e && t instanceof Q && void 0 !== i && null != i && 1 == t.isPrivate) {
        l = o(t).getEncodedHex();
        return void 0 === n && (n = "DES-EDE3-CBC"), this.getEncryptedPKCS5PEMFromPrvKeyHex("RSA", l, i, n);
    }
    if ("PKCS5PRV" == e && void 0 !== c.crypto.ECDSA && t instanceof c.crypto.ECDSA && void 0 !== i && null != i && 1 == t.isPrivate) {
        l = h(t).getEncodedHex();
        return void 0 === n && (n = "DES-EDE3-CBC"), this.getEncryptedPKCS5PEMFromPrvKeyHex("EC", l, i, n);
    }
    if ("PKCS5PRV" == e && void 0 !== c.crypto.DSA && t instanceof c.crypto.DSA && void 0 !== i && null != i && 1 == t.isPrivate) {
        l = u(t).getEncodedHex();
        return void 0 === n && (n = "DES-EDE3-CBC"), this.getEncryptedPKCS5PEMFromPrvKeyHex("DSA", l, i, n);
    }
    var y = function(t, e) {
        var i = v(t, e);
        return new c.asn1.ASN1Util.newObject({
            seq: [ {
                seq: [ {
                    oid: {
                        name: "pkcs5PBES2"
                    }
                }, {
                    seq: [ {
                        seq: [ {
                            oid: {
                                name: "pkcs5PBKDF2"
                            }
                        }, {
                            seq: [ {
                                octstr: {
                                    hex: i.pbkdf2Salt
                                }
                            }, {
                                int: i.pbkdf2Iter
                            } ]
                        } ]
                    }, {
                        seq: [ {
                            oid: {
                                name: "des-EDE3-CBC"
                            }
                        }, {
                            octstr: {
                                hex: i.encryptionSchemeIV
                            }
                        } ]
                    } ]
                } ]
            }, {
                octstr: {
                    hex: i.ciphertext
                }
            } ]
        }).getEncodedHex();
    }, v = function(t, e) {
        var i = g.lib.WordArray.random(8), n = g.lib.WordArray.random(8), r = g.PBKDF2(e, i, {
            keySize: 6,
            iterations: 100
        }), s = g.enc.Hex.parse(t), a = g.TripleDES.encrypt(s, r, {
            iv: n
        }) + "", o = {};
        return o.ciphertext = a, o.pbkdf2Salt = g.enc.Hex.stringify(i), o.pbkdf2Iter = 100, 
        o.encryptionSchemeAlg = "DES-EDE3-CBC", o.encryptionSchemeIV = g.enc.Hex.stringify(n), 
        o;
    };
    if ("PKCS8PRV" == e && t instanceof Q && 1 == t.isPrivate) {
        var S = o(t).getEncodedHex();
        l = c.asn1.ASN1Util.newObject({
            seq: [ {
                int: 0
            }, {
                seq: [ {
                    oid: {
                        name: "rsaEncryption"
                    }
                }, {
                    null: !0
                } ]
            }, {
                octstr: {
                    hex: S
                }
            } ]
        }).getEncodedHex();
        if (void 0 === i || null == i) return s.ASN1Util.getPEMStringFromHex(l, "PRIVATE KEY");
        d = y(l, i);
        return s.ASN1Util.getPEMStringFromHex(d, "ENCRYPTED PRIVATE KEY");
    }
    if ("PKCS8PRV" == e && void 0 !== c.crypto.ECDSA && t instanceof c.crypto.ECDSA && 1 == t.isPrivate) {
        S = new c.asn1.ASN1Util.newObject({
            seq: [ {
                int: 1
            }, {
                octstr: {
                    hex: t.prvKeyHex
                }
            }, {
                tag: [ "a1", !0, {
                    bitstr: {
                        hex: "00" + t.pubKeyHex
                    }
                } ]
            } ]
        }).getEncodedHex(), l = c.asn1.ASN1Util.newObject({
            seq: [ {
                int: 0
            }, {
                seq: [ {
                    oid: {
                        name: "ecPublicKey"
                    }
                }, {
                    oid: {
                        name: t.curveName
                    }
                } ]
            }, {
                octstr: {
                    hex: S
                }
            } ]
        }).getEncodedHex();
        if (void 0 === i || null == i) return s.ASN1Util.getPEMStringFromHex(l, "PRIVATE KEY");
        d = y(l, i);
        return s.ASN1Util.getPEMStringFromHex(d, "ENCRYPTED PRIVATE KEY");
    }
    if ("PKCS8PRV" == e && void 0 !== c.crypto.DSA && t instanceof c.crypto.DSA && 1 == t.isPrivate) {
        S = new c.asn1.DERInteger({
            bigint: t.x
        }).getEncodedHex(), l = c.asn1.ASN1Util.newObject({
            seq: [ {
                int: 0
            }, {
                seq: [ {
                    oid: {
                        name: "dsa"
                    }
                }, {
                    seq: [ {
                        int: {
                            bigint: t.p
                        }
                    }, {
                        int: {
                            bigint: t.q
                        }
                    }, {
                        int: {
                            bigint: t.g
                        }
                    } ]
                } ]
            }, {
                octstr: {
                    hex: S
                }
            } ]
        }).getEncodedHex();
        if (void 0 === i || null == i) return s.ASN1Util.getPEMStringFromHex(l, "PRIVATE KEY");
        d = y(l, i);
        return s.ASN1Util.getPEMStringFromHex(d, "ENCRYPTED PRIVATE KEY");
    }
    throw "unsupported object nor format";
}, Ft.getKeyFromCSRPEM = function(t) {
    var e = ot.pemToHex(t, "CERTIFICATE REQUEST");
    return Ft.getKeyFromCSRHex(e);
}, Ft.getKeyFromCSRHex = function(t) {
    var e = Ft.parseCSRHex(t);
    return Ft.getKey(e.p8pubkeyhex, null, "pkcs8pub");
}, Ft.parseCSRHex = function(t) {
    var e = {}, i = t;
    if ("30" != i.substr(0, 2)) throw "malformed CSR(code:001)";
    var n = ot.getPosArrayOfChildren_AtObj(i, 0);
    if (n.length < 1) throw "malformed CSR(code:002)";
    if ("30" != i.substr(n[0], 2)) throw "malformed CSR(code:003)";
    var r = ot.getPosArrayOfChildren_AtObj(i, n[0]);
    if (r.length < 3) throw "malformed CSR(code:004)";
    return e.p8pubkeyhex = ot.getHexOfTLV_AtObj(i, r[2]), e;
}, Ft.getJWKFromKey = function(t) {
    var e = {};
    if (t instanceof Q && t.isPrivate) return e.kty = "RSA", e.n = ft(t.n.toString(16)), 
    e.e = ft(t.e.toString(16)), e.d = ft(t.d.toString(16)), e.p = ft(t.p.toString(16)), 
    e.q = ft(t.q.toString(16)), e.dp = ft(t.dmp1.toString(16)), e.dq = ft(t.dmq1.toString(16)), 
    e.qi = ft(t.coeff.toString(16)), e;
    if (t instanceof Q && t.isPublic) return e.kty = "RSA", e.n = ft(t.n.toString(16)), 
    e.e = ft(t.e.toString(16)), e;
    if (t instanceof c.crypto.ECDSA && t.isPrivate) {
        if ("P-256" !== (n = t.getShortNISTPCurveName()) && "P-384" !== n) throw "unsupported curve name for JWT: " + n;
        var i = t.getPublicKeyXYHex();
        return e.kty = "EC", e.crv = n, e.x = ft(i.x), e.y = ft(i.y), e.d = ft(t.prvKeyHex), 
        e;
    }
    if (t instanceof c.crypto.ECDSA && t.isPublic) {
        var n;
        if ("P-256" !== (n = t.getShortNISTPCurveName()) && "P-384" !== n) throw "unsupported curve name for JWT: " + n;
        i = t.getPublicKeyXYHex();
        return e.kty = "EC", e.crv = n, e.x = ft(i.x), e.y = ft(i.y), e;
    }
    throw "not supported key object";
}, 
/*! rsapem-1.2.0.js (c) 2012-2017 Kenji Urushima | kjur.github.com/jsrsasign/license
 */
Q.pemToBase64 = function(t) {
    var e = t;
    return e = (e = (e = e.replace("-----BEGIN RSA PRIVATE KEY-----", "")).replace("-----END RSA PRIVATE KEY-----", "")).replace(/[ \n]+/g, "");
}, Q.getPosArrayOfChildrenFromHex = function(t) {
    var e = new Array(), i = ot.getStartPosOfV_AtObj(t, 0), n = ot.getPosOfNextSibling_AtObj(t, i), r = ot.getPosOfNextSibling_AtObj(t, n), s = ot.getPosOfNextSibling_AtObj(t, r), a = ot.getPosOfNextSibling_AtObj(t, s), o = ot.getPosOfNextSibling_AtObj(t, a), h = ot.getPosOfNextSibling_AtObj(t, o), u = ot.getPosOfNextSibling_AtObj(t, h), c = ot.getPosOfNextSibling_AtObj(t, u);
    return e.push(i, n, r, s, a, o, h, u, c), e;
}, Q.getHexValueArrayOfChildrenFromHex = function(t) {
    var e = Q.getPosArrayOfChildrenFromHex(t), i = ot.getHexOfV_AtObj(t, e[0]), n = ot.getHexOfV_AtObj(t, e[1]), r = ot.getHexOfV_AtObj(t, e[2]), s = ot.getHexOfV_AtObj(t, e[3]), a = ot.getHexOfV_AtObj(t, e[4]), o = ot.getHexOfV_AtObj(t, e[5]), h = ot.getHexOfV_AtObj(t, e[6]), u = ot.getHexOfV_AtObj(t, e[7]), c = ot.getHexOfV_AtObj(t, e[8]), l = new Array();
    return l.push(i, n, r, s, a, o, h, u, c), l;
}, Q.prototype.readPrivateKeyFromPEMString = function(t) {
    var e = S(Q.pemToBase64(t)), i = Q.getHexValueArrayOfChildrenFromHex(e);
    this.setPrivateEx(i[1], i[2], i[3], i[4], i[5], i[6], i[7], i[8]);
}, Q.prototype.readPrivateKeyFromASN1HexString = function(t) {
    this.readPKCS5PrvKeyHex(t);
}, Q.prototype.readPKCS5PrvKeyHex = function(t) {
    var e = Q.getHexValueArrayOfChildrenFromHex(t);
    this.setPrivateEx(e[1], e[2], e[3], e[4], e[5], e[6], e[7], e[8]);
}, Q.prototype.readPKCS8PrvKeyHex = function(t) {
    var e, i, n, r, s, a, o, h, u = ot, c = u.getVbyList;
    if (!1 === u.isASN1HEX(t)) throw "not ASN.1 hex string";
    try {
        e = c(t, 0, [ 2, 0, 1 ], "02"), i = c(t, 0, [ 2, 0, 2 ], "02"), n = c(t, 0, [ 2, 0, 3 ], "02"), 
        r = c(t, 0, [ 2, 0, 4 ], "02"), s = c(t, 0, [ 2, 0, 5 ], "02"), a = c(t, 0, [ 2, 0, 6 ], "02"), 
        o = c(t, 0, [ 2, 0, 7 ], "02"), h = c(t, 0, [ 2, 0, 8 ], "02");
    } catch (t) {
        t = VM2_INTERNAL_STATE_DO_NOT_USE_OR_PROGRAM_WILL_FAIL.handleException(t);
        throw "malformed PKCS#8 plain RSA private key";
    }
    this.setPrivateEx(e, i, n, r, s, a, o, h);
}, Q.prototype.readPKCS5PubKeyHex = function(t) {
    if (!1 === ot.isASN1HEX(t)) throw "keyHex is not ASN.1 hex string";
    var e = ot.getPosArrayOfChildren_AtObj(t, 0);
    if (2 !== e.length || "02" !== t.substr(e[0], 2) || "02" !== t.substr(e[1], 2)) throw "wrong hex for PKCS#5 public key";
    var i = ot.getHexOfV_AtObj(t, e[0]), n = ot.getHexOfV_AtObj(t, e[1]);
    this.setPublic(i, n);
}, Q.prototype.readPKCS8PubKeyHex = function(t) {
    if (!1 === ot.isASN1HEX(t)) throw "not ASN.1 hex string";
    if ("06092a864886f70d010101" !== ot.getDecendantHexTLVByNthList(t, 0, [ 0, 0 ])) throw "not PKCS8 RSA public key";
    var e = ot.getDecendantHexTLVByNthList(t, 0, [ 1, 0 ]);
    this.readPKCS5PubKeyHex(e);
}, Q.prototype.readCertPubKeyHex = function(t, e) {
    if (5 !== e && (e = 6), !1 === ot.isASN1HEX(t)) throw "not ASN.1 hex string";
    var i = ot.getDecendantHexTLVByNthList(t, 0, [ 0, e ]);
    this.readPKCS8PubKeyHex(i);
};

/*! rsasign-1.2.7.js (c) 2012 Kenji Urushima | kjur.github.com/jsrsasign/license
 */ var Et = new RegExp("");

function wt(t, e) {
    for (var i = "", n = e / 4 - t.length, r = 0; r < n; r++) i += "0";
    return i + t;
}

function Pt(t, e) {
    var i = function(t) {
        return c.crypto.Util.hashString(t, e);
    }(t);
    return this.signWithMessageHash(i, e);
}

function Ht(t) {
    return Pt.call(this, t, "sha1");
}

function Ct(t) {
    return Pt.call(this, t, "sha256");
}

function Ot(t, e, i) {
    for (var n = "", r = 0; n.length < e; ) n += yt(i(vt(t + String.fromCharCode.apply(String, [ (********** & r) >> 24, (16711680 & r) >> 16, (65280 & r) >> 8, 255 & r ])))), 
    r += 1;
    return n;
}

function Dt(t, e, i) {
    var n = function(t) {
        return c.crypto.Util.hashHex(t, e);
    }(vt(t));
    return void 0 === i && (i = -1), this.signWithMessageHashPSS(n, e, i);
}

function Tt(t, e, i) {
    return function(t, e, i) {
        var n = new Q();
        return n.setPublic(e, i), n.doPublic(t);
    }(t, e, i).toString(16).replace(/^1f+00/, "");
}

function jt(t) {
    for (var e in c.crypto.Util.DIGESTINFOHEAD) {
        var i = c.crypto.Util.DIGESTINFOHEAD[e], n = i.length;
        if (t.substring(0, n) == i) return [ e, t.substring(n) ];
    }
    return [];
}

function It(t, e) {
    return function(t, e, i, n) {
        var r = jt(Tt(e, i, n));
        if (0 == r.length) return !1;
        var s = r[0];
        return r[1] == function(t) {
            return c.crypto.Util.hashString(t, s);
        }(t);
    }(e, $(t, 16), this.n.toString(16), this.e.toString(16));
}

function Rt(t, e) {
    var i = $(e = (e = e.replace(Et, "")).replace(/[ \n]+/g, ""), 16);
    if (i.bitLength() > this.n.bitLength()) return 0;
    var n = jt(this.doPublic(i).toString(16).replace(/^1f+00/, ""));
    if (0 == n.length) return !1;
    var r = n[0];
    return n[1] == function(t) {
        return c.crypto.Util.hashString(t, r);
    }(t);
}

function Bt(t, e, i, n) {
    var r = function(t) {
        return c.crypto.Util.hashHex(t, i);
    }(vt(t));
    return void 0 === n && (n = -1), this.verifyWithMessageHashPSS(r, e, i, n)
    /*! x509-1.1.12.js (c) 2012-2017 Kenji Urushima | kjur.github.com/jsrsasign/license
 */;
}

function Nt() {
    this.subjectPublicKeyRSA = null, this.subjectPublicKeyRSA_hN = null, this.subjectPublicKeyRSA_hE = null, 
    this.hex = null, this.getSerialNumberHex = function() {
        return ot.getDecendantHexVByNthList(this.hex, 0, [ 0, 1 ]);
    }, this.getSignatureAlgorithmField = function() {
        var t = ot.getDecendantHexVByNthList(this.hex, 0, [ 0, 2, 0 ]), e = c.asn1.ASN1Util.oidHexToInt(t);
        return c.asn1.x509.OID.oid2name(e);
    }, this.getIssuerHex = function() {
        return ot.getDecendantHexTLVByNthList(this.hex, 0, [ 0, 3 ]);
    }, this.getIssuerString = function() {
        return Nt.hex2dn(ot.getDecendantHexTLVByNthList(this.hex, 0, [ 0, 3 ]));
    }, this.getSubjectHex = function() {
        return ot.getDecendantHexTLVByNthList(this.hex, 0, [ 0, 5 ]);
    }, this.getSubjectString = function() {
        return Nt.hex2dn(ot.getDecendantHexTLVByNthList(this.hex, 0, [ 0, 5 ]));
    }, this.getNotBefore = function() {
        var t = ot.getDecendantHexVByNthList(this.hex, 0, [ 0, 4, 0 ]);
        return t = t.replace(/(..)/g, "%$1"), t = decodeURIComponent(t);
    }, this.getNotAfter = function() {
        var t = ot.getDecendantHexVByNthList(this.hex, 0, [ 0, 4, 1 ]);
        return t = t.replace(/(..)/g, "%$1"), t = decodeURIComponent(t);
    }, this.readCertPEM = function(t) {
        var e = ot.pemToHex(t), i = Nt.getPublicKeyHexArrayFromCertHex(e), n = new Q();
        n.setPublic(i[0], i[1]), this.subjectPublicKeyRSA = n, this.subjectPublicKeyRSA_hN = i[0], 
        this.subjectPublicKeyRSA_hE = i[1], this.hex = e;
    }, this.readCertPEMWithoutRSAInit = function(t) {
        var e = ot.pemToHex(t), i = Nt.getPublicKeyHexArrayFromCertHex(e);
        "function" == typeof this.subjectPublicKeyRSA.setPublic && this.subjectPublicKeyRSA.setPublic(i[0], i[1]), 
        this.subjectPublicKeyRSA_hN = i[0], this.subjectPublicKeyRSA_hE = i[1], this.hex = e;
    }, this.getInfo = function() {
        var t = "Basic Fields\n";
        t += "  serial number: " + this.getSerialNumberHex() + "\n", t += "  signature algorithm: " + this.getSignatureAlgorithmField() + "\n", 
        t += "  issuer: " + this.getIssuerString() + "\n", t += "  notBefore: " + this.getNotBefore() + "\n", 
        t += "  notAfter: " + this.getNotAfter() + "\n", t += "  subject: " + this.getSubjectString() + "\n", 
        t += "  subject public key info: \n";
        var e = Nt.getSubjectPublicKeyInfoPosFromCertHex(this.hex), i = ot.getHexOfTLV_AtObj(this.hex, e), n = Ft.getKey(i, null, "pkcs8pub");
        n instanceof Q && (t += "    key algorithm: RSA\n", t += "    n=" + n.n.toString(16).substr(0, 16) + "...\n", 
        t += "    e=" + n.e.toString(16) + "\n"), t += "X509v3 Extensions:\n";
        for (var r = Nt.getV3ExtInfoListOfCertHex(this.hex), s = 0; s < r.length; s++) {
            var a = r[s], o = c.asn1.x509.OID.oid2name(a.oid);
            "" === o && (o = a.oid);
            var h = "";
            if (!0 === a.critical && (h = "CRITICAL"), t += "  " + o + " " + h + ":\n", "basicConstraints" === o) {
                var u = Nt.getExtBasicConstraints(this.hex);
                void 0 === u.cA ? t += "    {}\n" : (t += "    cA=true", void 0 !== u.pathLen && (t += ", pathLen=" + u.pathLen), 
                t += "\n");
            } else if ("keyUsage" === o) t += "    " + Nt.getExtKeyUsageString(this.hex) + "\n"; else if ("subjectKeyIdentifier" === o) t += "    " + Nt.getExtSubjectKeyIdentifier(this.hex) + "\n"; else if ("authorityKeyIdentifier" === o) {
                var l = Nt.getExtAuthorityKeyIdentifier(this.hex);
                void 0 !== l.kid && (t += "    kid=" + l.kid + "\n");
            } else {
                if ("extKeyUsage" === o) t += "    " + Nt.getExtExtKeyUsageName(this.hex).join(", ") + "\n"; else if ("subjectAltName" === o) t += "    " + Nt.getExtSubjectAltName(this.hex).join(", ") + "\n"; else if ("cRLDistributionPoints" === o) t += "    " + Nt.getExtCRLDistributionPointsURI(this.hex) + "\n"; else if ("authorityInfoAccess" === o) {
                    var f = Nt.getExtAIAInfo(this.hex);
                    void 0 !== f.ocsp && (t += "    ocsp: " + f.ocsp.join(",") + "\n"), void 0 !== f.caissuer && (t += "    caissuer: " + f.caissuer.join(",") + "\n");
                }
            }
        }
        return t += "signature algorithm: " + Nt.getSignatureAlgorithmName(this.hex) + "\n", 
        t += "signature: " + Nt.getSignatureValueHex(this.hex).substr(0, 16) + "...\n";
    };
}

Et.compile("[^0-9a-f]", "gi"), Q.prototype.signWithMessageHash = function(t, e) {
    var i = $(c.crypto.Util.getPaddedDigestInfoHex(t, e, this.n.bitLength()), 16);
    return wt(this.doPrivate(i).toString(16), this.n.bitLength());
}, Q.prototype.signString = Pt, Q.prototype.signStringWithSHA1 = Ht, Q.prototype.signStringWithSHA256 = Ct, 
Q.prototype.sign = Pt, Q.prototype.signWithSHA1 = Ht, Q.prototype.signWithSHA256 = Ct, 
Q.prototype.signWithMessageHashPSS = function(t, e, i) {
    var n, r = yt(t), s = r.length, a = this.n.bitLength() - 1, o = Math.ceil(a / 8), h = function(t) {
        return c.crypto.Util.hashHex(t, e);
    };
    if (-1 === i || void 0 === i) i = s; else if (-2 === i) i = o - s - 2; else if (i < -2) throw "invalid salt length";
    if (o < s + i + 2) throw "data too long";
    var u = "";
    i > 0 && (u = new Array(i), new Y().nextBytes(u), u = String.fromCharCode.apply(String, u));
    var l = yt(h(vt("\0\0\0\0\0\0\0\0" + r + u))), f = [];
    for (n = 0; n < o - i - s - 2; n += 1) f[n] = 0;
    var d = String.fromCharCode.apply(String, f) + "" + u, g = Ot(l, d.length, h), p = [];
    for (n = 0; n < d.length; n += 1) p[n] = d.charCodeAt(n) ^ g.charCodeAt(n);
    var y = 65280 >> 8 * o - a & 255;
    for (p[0] &= ~y, n = 0; n < s; n++) p.push(l.charCodeAt(n));
    return p.push(188), wt(this.doPrivate(new m(p)).toString(16), this.n.bitLength());
}, Q.prototype.signStringPSS = Dt, Q.prototype.signPSS = Dt, Q.SALT_LEN_HLEN = -1, 
Q.SALT_LEN_MAX = -2, Q.prototype.verifyWithMessageHash = function(t, e) {
    var i = $(e = (e = e.replace(Et, "")).replace(/[ \n]+/g, ""), 16);
    if (i.bitLength() > this.n.bitLength()) return 0;
    var n = jt(this.doPublic(i).toString(16).replace(/^1f+00/, ""));
    return 0 != n.length && (n[0], n[1] == t);
}, Q.prototype.verifyString = Rt, Q.prototype.verifyHexSignatureForMessage = It, 
Q.prototype.verify = Rt, Q.prototype.verifyHexSignatureForByteArrayMessage = It, 
Q.prototype.verifyWithMessageHashPSS = function(t, e, i, n) {
    var r = new m(e, 16);
    if (r.bitLength() > this.n.bitLength()) return !1;
    var s, a = function(t) {
        return c.crypto.Util.hashHex(t, i);
    }, o = yt(t), h = o.length, u = this.n.bitLength() - 1, l = Math.ceil(u / 8);
    if (-1 === n || void 0 === n) n = h; else if (-2 === n) n = l - h - 2; else if (n < -2) throw "invalid salt length";
    if (l < h + n + 2) throw "data too long";
    var f = this.doPublic(r).toByteArray();
    for (s = 0; s < f.length; s += 1) f[s] &= 255;
    for (;f.length < l; ) f.unshift(0);
    if (188 !== f[l - 1]) throw "encoded message does not end in 0xbc";
    var d = (f = String.fromCharCode.apply(String, f)).substr(0, l - h - 1), g = f.substr(d.length, h), p = 65280 >> 8 * l - u & 255;
    if (0 != (d.charCodeAt(0) & p)) throw "bits beyond keysize not zero";
    var y = Ot(g, d.length, a), v = [];
    for (s = 0; s < d.length; s += 1) v[s] = d.charCodeAt(s) ^ y.charCodeAt(s);
    v[0] &= ~p;
    var S = l - h - n - 2;
    for (s = 0; s < S; s += 1) if (0 !== v[s]) throw "leftmost octets not zero";
    if (1 !== v[S]) throw "0x01 marker not found";
    return g === yt(a(vt("\0\0\0\0\0\0\0\0" + o + String.fromCharCode.apply(String, v.slice(-n)))));
}, Q.prototype.verifyStringPSS = Bt, Q.prototype.verifyPSS = Bt, Q.SALT_LEN_RECOVER = -2, 
Nt.pemToBase64 = function(t) {
    var e = t;
    return e = (e = (e = e.replace("-----BEGIN CERTIFICATE-----", "")).replace("-----END CERTIFICATE-----", "")).replace(/[ \n]+/g, "");
}, Nt.pemToHex = function(t) {
    return ot.pemToHex(t);
}, Nt.getSubjectPublicKeyPosFromCertHex = function(t) {
    var e = Nt.getSubjectPublicKeyInfoPosFromCertHex(t);
    if (-1 == e) return -1;
    var i = ot.getPosArrayOfChildren_AtObj(t, e);
    if (2 != i.length) return -1;
    var n = i[1];
    if ("03" != t.substring(n, n + 2)) return -1;
    var r = ot.getStartPosOfV_AtObj(t, n);
    return "00" != t.substring(r, r + 2) ? -1 : r + 2;
}, Nt.getSubjectPublicKeyInfoPosFromCertHex = function(t) {
    var e = ot.getStartPosOfV_AtObj(t, 0), i = ot.getPosArrayOfChildren_AtObj(t, e);
    return i.length < 1 ? -1 : "a003020102" == t.substring(i[0], i[0] + 10) ? i.length < 6 ? -1 : i[6] : i.length < 5 ? -1 : i[5];
}, Nt.getPublicKeyHexArrayFromCertHex = function(t) {
    var e = Nt.getSubjectPublicKeyPosFromCertHex(t), i = ot.getPosArrayOfChildren_AtObj(t, e);
    if (2 != i.length) return [];
    var n = ot.getHexOfV_AtObj(t, i[0]), r = ot.getHexOfV_AtObj(t, i[1]);
    return null != n && null != r ? [ n, r ] : [];
}, Nt.getHexTbsCertificateFromCert = function(t) {
    return ot.getStartPosOfV_AtObj(t, 0);
}, Nt.getPublicKeyHexArrayFromCertPEM = function(t) {
    var e = ot.pemToHex(t);
    return Nt.getPublicKeyHexArrayFromCertHex(e);
}, Nt.hex2dn = function(t, e) {
    if (void 0 === e && (e = 0), "30" !== t.substr(e, 2)) throw "malformed DN";
    for (var i = new Array(), n = ot.getPosArrayOfChildren_AtObj(t, e), r = 0; r < n.length; r++) i.push(Nt.hex2rdn(t, n[r]));
    return "/" + (i = i.map(function(t) {
        return t.replace("/", "\\/");
    })).join("/");
}, Nt.hex2rdn = function(t, e) {
    if (void 0 === e && (e = 0), "31" !== t.substr(e, 2)) throw "malformed RDN";
    for (var i = new Array(), n = ot.getPosArrayOfChildren_AtObj(t, e), r = 0; r < n.length; r++) i.push(Nt.hex2attrTypeValue(t, n[r]));
    return (i = i.map(function(t) {
        return t.replace("+", "\\+");
    })).join("+");
}, Nt.hex2attrTypeValue = function(t, e) {
    if (void 0 === e && (e = 0), "30" !== t.substr(e, 2)) throw "malformed attribute type and value";
    var i = ot.getPosArrayOfChildren_AtObj(t, e);
    2 !== i.length || t.substr(i[0], 2);
    var n = ot.getHexOfV_AtObj(t, i[0]), r = c.asn1.ASN1Util.oidHexToInt(n);
    return c.asn1.x509.OID.oid2atype(r) + "=" + yt(ot.getHexOfV_AtObj(t, i[1]));
}, Nt.getPublicKeyFromCertHex = function(t) {
    var e, i, n = 6, r = ot, s = r.getVbyList;
    if ("a003020102" !== r.getDecendantHexTLVByNthList(t, 0, [ 0, 0 ]) && (n = 5), "2a864886f70d010101" === (i = s(t, 0, [ 0, n, 0, 0 ], "06"))) e = new Q(); else if ("2a8648ce380401" === i) e = new c.crypto.DSA(); else {
        if ("2a8648ce3d0201" !== i) throw "unsupported public key in X.509 cert";
        e = new c.crypto.ECDSA();
    }
    return e.readCertPubKeyHex(t, n), e;
}, Nt.getPublicKeyFromCertPEM = function(t) {
    var e = ot.pemToHex(t);
    return Nt.getPublicKeyFromCertHex(e);
}, Nt.getPublicKeyInfoPropOfCertPEM = function(t) {
    var e = {
        algparam: null
    }, i = ot.pemToHex(t), n = ot.getPosArrayOfChildren_AtObj(i, 0);
    if (3 != n.length) throw "malformed X.509 certificate PEM (code:001)";
    if ("30" != i.substr(n[0], 2)) throw "malformed X.509 certificate PEM (code:002)";
    var r = ot.getPosArrayOfChildren_AtObj(i, n[0]), s = 6;
    if ("a0" !== i.substr(r[0], 2) && (s = 5), r.length < s + 1) throw "malformed X.509 certificate PEM (code:003)";
    var a = ot.getPosArrayOfChildren_AtObj(i, r[s]);
    if (2 != a.length) throw "malformed X.509 certificate PEM (code:004)";
    var o = ot.getPosArrayOfChildren_AtObj(i, a[0]);
    if (2 != o.length) throw "malformed X.509 certificate PEM (code:005)";
    if (e.algoid = ot.getHexOfV_AtObj(i, o[0]), "06" == i.substr(o[1], 2) ? e.algparam = ot.getHexOfV_AtObj(i, o[1]) : "30" == i.substr(o[1], 2) && (e.algparam = ot.getHexOfTLV_AtObj(i, o[1])), 
    "03" != i.substr(a[1], 2)) throw "malformed X.509 certificate PEM (code:006)";
    var h = ot.getHexOfV_AtObj(i, a[1]);
    return e.keyhex = h.substr(2), e;
}, Nt.getPublicKeyInfoPosOfCertHEX = function(t) {
    var e = ot.getPosArrayOfChildren_AtObj(t, 0);
    if (3 != e.length) throw "malformed X.509 certificate PEM (code:001)";
    if ("30" != t.substr(e[0], 2)) throw "malformed X.509 certificate PEM (code:002)";
    var i = ot.getPosArrayOfChildren_AtObj(t, e[0]);
    if (i.length < 7) throw "malformed X.509 certificate PEM (code:003)";
    return i[6];
}, Nt.getV3ExtInfoListOfCertHex = function(t) {
    var e = ot.getPosArrayOfChildren_AtObj(t, 0);
    if (3 != e.length) throw "malformed X.509 certificate PEM (code:001)";
    if ("30" != t.substr(e[0], 2)) throw "malformed X.509 certificate PEM (code:002)";
    var i = ot.getPosArrayOfChildren_AtObj(t, e[0]);
    if (i.length < 8) throw "malformed X.509 certificate PEM (code:003)";
    if ("a3" != t.substr(i[7], 2)) throw "malformed X.509 certificate PEM (code:004)";
    var n = ot.getPosArrayOfChildren_AtObj(t, i[7]);
    if (1 != n.length) throw "malformed X.509 certificate PEM (code:005)";
    if ("30" != t.substr(n[0], 2)) throw "malformed X.509 certificate PEM (code:006)";
    for (var r = ot.getPosArrayOfChildren_AtObj(t, n[0]), s = r.length, a = new Array(s), o = 0; o < s; o++) a[o] = Nt.getV3ExtItemInfo_AtObj(t, r[o]);
    return a;
}, Nt.getV3ExtItemInfo_AtObj = function(t, e) {
    var i = {};
    i.posTLV = e;
    var n = ot.getPosArrayOfChildren_AtObj(t, e);
    if (2 != n.length && 3 != n.length) throw "malformed X.509v3 Ext (code:001)";
    if ("06" != t.substr(n[0], 2)) throw "malformed X.509v3 Ext (code:002)";
    var r = ot.getHexOfV_AtObj(t, n[0]);
    i.oid = ot.hextooidstr(r), i.critical = !1, 3 == n.length && (i.critical = !0);
    var s = n[n.length - 1];
    if ("04" != t.substr(s, 2)) throw "malformed X.509v3 Ext (code:003)";
    return i.posV = ot.getStartPosOfV_AtObj(t, s), i;
}, Nt.getHexOfTLV_V3ExtValue = function(t, e) {
    var i = Nt.getPosOfTLV_V3ExtValue(t, e);
    return -1 == i ? null : ot.getHexOfTLV_AtObj(t, i);
}, Nt.getHexOfV_V3ExtValue = function(t, e) {
    var i = Nt.getPosOfTLV_V3ExtValue(t, e);
    return -1 == i ? null : ot.getHexOfV_AtObj(t, i);
}, Nt.getPosOfTLV_V3ExtValue = function(t, e) {
    var i = e;
    if (e.match(/^[0-9.]+$/) || (i = c.asn1.x509.OID.name2oid(e)), "" == i) return -1;
    for (var n = Nt.getV3ExtInfoListOfCertHex(t), r = 0; r < n.length; r++) {
        var s = n[r];
        if (s.oid == i) return s.posV;
    }
    return -1;
}, Nt.getExtBasicConstraints = function(t) {
    var e = Nt.getHexOfV_V3ExtValue(t, "basicConstraints");
    if (null === e) return null;
    if ("" === e) return {};
    if ("0101ff" === e) return {
        cA: !0
    };
    if ("0101ff02" === e.substr(0, 8)) {
        var i = ot.getHexOfV_AtObj(e, 6);
        return {
            cA: !0,
            pathLen: parseInt(i, 16)
        };
    }
    throw "unknown error";
}, Nt.KEYUSAGE_NAME = [ "digitalSignature", "nonRepudiation", "keyEncipherment", "dataEncipherment", "keyAgreement", "keyCertSign", "cRLSign", "encipherOnly", "decipherOnly" ], 
Nt.getExtKeyUsageBin = function(t) {
    var e = Nt.getHexOfV_V3ExtValue(t, "keyUsage");
    if ("" == e) return "";
    if (e.length % 2 != 0 || e.length <= 2) throw "malformed key usage value";
    var i = parseInt(e.substr(0, 2)), n = parseInt(e.substr(2), 16).toString(2);
    return n.substr(0, n.length - i);
}, Nt.getExtKeyUsageString = function(t) {
    for (var e = Nt.getExtKeyUsageBin(t), i = new Array(), n = 0; n < e.length; n++) "1" == e.substr(n, 1) && i.push(Nt.KEYUSAGE_NAME[n]);
    return i.join(",");
}, Nt.getExtSubjectKeyIdentifier = function(t) {
    return Nt.getHexOfV_V3ExtValue(t, "subjectKeyIdentifier");
}, Nt.getExtAuthorityKeyIdentifier = function(t) {
    var e = {}, i = Nt.getHexOfTLV_V3ExtValue(t, "authorityKeyIdentifier");
    if (null === i) return null;
    for (var n = ot.getPosArrayOfChildren_AtObj(i, 0), r = 0; r < n.length; r++) "80" === i.substr(n[r], 2) && (e.kid = ot.getHexOfV_AtObj(i, n[r]));
    return e;
}, Nt.getExtExtKeyUsageName = function(t) {
    var e = new Array(), i = Nt.getHexOfTLV_V3ExtValue(t, "extKeyUsage");
    if (null === i) return null;
    for (var n = ot.getPosArrayOfChildren_AtObj(i, 0), r = 0; r < n.length; r++) {
        var s = ot.getHexOfV_AtObj(i, n[r]), a = c.asn1.ASN1Util.oidHexToInt(s), o = c.asn1.x509.OID.oid2name(a);
        e.push(o);
    }
    return e;
}, Nt.getExtSubjectAltName = function(t) {
    for (var e = new Array(), i = Nt.getHexOfTLV_V3ExtValue(t, "subjectAltName"), n = ot.getPosArrayOfChildren_AtObj(i, 0), r = 0; r < n.length; r++) if ("82" === i.substr(n[r], 2)) {
        var s = pt(ot.getHexOfV_AtObj(i, n[r]));
        e.push(s);
    }
    return e;
}, Nt.getExtCRLDistributionPointsURI = function(t) {
    for (var e = new Array(), i = Nt.getHexOfTLV_V3ExtValue(t, "cRLDistributionPoints"), n = ot.getPosArrayOfChildren_AtObj(i, 0), r = 0; r < n.length; r++) for (var s = ot.getHexOfTLV_AtObj(i, n[r]), a = ot.getPosArrayOfChildren_AtObj(s, 0), o = 0; o < a.length; o++) if ("a0" === s.substr(a[o], 2)) {
        var h = ot.getHexOfV_AtObj(s, a[o]);
        if ("a0" === h.substr(0, 2)) {
            var u = ot.getHexOfV_AtObj(h, 0);
            if ("86" === u.substr(0, 2)) {
                var c = pt(ot.getHexOfV_AtObj(u, 0));
                e.push(c);
            }
        }
    }
    return e;
}, Nt.getExtAIAInfo = function(t) {
    var e = {
        ocsp: [],
        caissuer: []
    }, i = Nt.getPosOfTLV_V3ExtValue(t, "authorityInfoAccess");
    if (-1 == i) return null;
    if ("30" != t.substr(i, 2)) throw "malformed AIA Extn Value";
    for (var n = ot.getPosArrayOfChildren_AtObj(t, i), r = 0; r < n.length; r++) {
        var s = n[r], a = ot.getPosArrayOfChildren_AtObj(t, s);
        if (2 != a.length) throw "malformed AccessDescription of AIA Extn";
        var o = a[0], h = a[1];
        "2b06010505073001" == ot.getHexOfV_AtObj(t, o) && "86" == t.substr(h, 2) && e.ocsp.push(pt(ot.getHexOfV_AtObj(t, h))), 
        "2b06010505073002" == ot.getHexOfV_AtObj(t, o) && "86" == t.substr(h, 2) && e.caissuer.push(pt(ot.getHexOfV_AtObj(t, h)));
    }
    return e;
}, Nt.getSignatureAlgorithmName = function(t) {
    var e = ot.getDecendantHexVByNthList(t, 0, [ 1, 0 ]), i = c.asn1.ASN1Util.oidHexToInt(e);
    return c.asn1.x509.OID.oid2name(i);
}, Nt.getSignatureValueHex = function(t) {
    var e = ot.getDecendantHexVByNthList(t, 0, [ 2 ]);
    if ("00" !== e.substr(0, 2)) throw "can't get signature value";
    return e.substr(2);
}, Nt.getSerialNumberHex = function(t) {
    return ot.getDecendantHexVByNthList(t, 0, [ 0, 1 ]);
}, Nt.verifySignature = function(t, e) {
    var i = Nt.getSignatureAlgorithmName(t), n = Nt.getSignatureValueHex(t), r = ot.getDecendantHexTLVByNthList(t, 0, [ 0 ]), s = new c.crypto.Signature({
        alg: i
    });
    return s.init(e), s.updateHex(r), s.verify(n)
    /*! jws-3.3.5 (c) 2013-2016 Kenji Urushima | kjur.github.com/jsrsasign/license
 */;
}, void 0 !== c && c || (c = {}), void 0 !== c.jws && c.jws || (c.jws = {}), c.jws.JWS = function() {
    var t = c.jws.JWS;
    this.parseJWS = function(e, i) {
        if (void 0 === this.parsedJWS || !i && void 0 === this.parsedJWS.sigvalH) {
            var n = e.match(/^([^.]+)\.([^.]+)\.([^.]+)$/);
            if (null == n) throw "JWS signature is not a form of 'Head.Payload.SigValue'.";
            var r = n[1], s = n[2], a = n[3], o = r + "." + s;
            if (this.parsedJWS = {}, this.parsedJWS.headB64U = r, this.parsedJWS.payloadB64U = s, 
            this.parsedJWS.sigvalB64U = a, this.parsedJWS.si = o, !i) {
                var h = dt(a), u = $(h, 16);
                this.parsedJWS.sigvalH = h, this.parsedJWS.sigvalBI = u;
            }
            var c = at(r), l = at(s);
            if (this.parsedJWS.headS = c, this.parsedJWS.payloadS = l, !t.isSafeJSONString(c, this.parsedJWS, "headP")) throw "malformed JSON string for JWS Head: " + c;
        }
    };
}, c.jws.JWS.sign = function(t, e, i, n, r) {
    var s, o, h, u = c.jws.JWS;
    if ("string" != typeof e && "object" != a(e)) throw "spHeader must be JSON string or object: " + e;
    if ("object" == a(e) && (o = e, s = JSON.stringify(o)), "string" == typeof e) {
        if (s = e, !u.isSafeJSONString(s)) throw "JWS Head is not safe JSON string: " + s;
        o = u.readSafeJSONString(s);
    }
    if (h = i, "object" == a(i) && (h = JSON.stringify(i)), "" != t && null != t || void 0 === o.alg || (t = o.alg), 
    "" != t && null != t && void 0 === o.alg && (o.alg = t, s = JSON.stringify(o)), 
    t !== o.alg) throw "alg and sHeader.alg doesn't match: " + t + "!=" + o.alg;
    var l = null;
    if (void 0 === u.jwsalg2sigalg[t]) throw "unsupported alg name: " + t;
    l = u.jwsalg2sigalg[t];
    var f = st(s) + "." + st(h), d = "";
    if ("Hmac" == l.substr(0, 4)) {
        if (void 0 === n) throw "mac key shall be specified for HS* alg";
        var g = new c.crypto.Mac({
            alg: l,
            prov: "cryptojs",
            pass: n
        });
        g.updateString(f), d = g.doFinal();
    } else {
        var p;
        if (-1 != l.indexOf("withECDSA")) (p = new c.crypto.Signature({
            alg: l
        })).init(n, r), p.updateString(f), hASN1Sig = p.sign(), d = c.crypto.ECDSA.asn1SigToConcatSig(hASN1Sig); else if ("none" != l) (p = new c.crypto.Signature({
            alg: l
        })).init(n, r), p.updateString(f), d = p.sign();
    }
    return f + "." + ft(d);
}, c.jws.JWS.verify = function(t, e, i) {
    var n = c.jws.JWS, r = t.split("."), s = r[0] + "." + r[1], a = dt(r[2]), o = n.readSafeJSONString(at(r[0])), h = null, u = null;
    if (void 0 === o.alg) throw "algorithm not specified in header";
    if ((u = (h = o.alg).substr(0, 2), null != i && "[object Array]" === Object.prototype.toString.call(i) && i.length > 0) && -1 == (":" + i.join(":") + ":").indexOf(":" + h + ":")) throw "algorithm '" + h + "' not accepted in the list";
    if ("none" != h && null === e) throw "key shall be specified to verify.";
    if ("string" == typeof e && -1 != e.indexOf("-----BEGIN ") && (e = Ft.getKey(e)), 
    !("RS" != u && "PS" != u || e instanceof Q)) throw "key shall be a RSAKey obj for RS* and PS* algs";
    if ("ES" == u && !(e instanceof c.crypto.ECDSA)) throw "key shall be a ECDSA obj for ES* algs";
    var l = null;
    if (void 0 === n.jwsalg2sigalg[o.alg]) throw "unsupported alg name: " + h;
    if ("none" == (l = n.jwsalg2sigalg[h])) throw "not supported";
    if ("Hmac" == l.substr(0, 4)) {
        if (void 0 === e) throw "hexadecimal key shall be specified for HMAC";
        var f = new c.crypto.Mac({
            alg: l,
            pass: e
        });
        return f.updateString(s), a == f.doFinal();
    }
    if (-1 != l.indexOf("withECDSA")) {
        var d, g = null;
        try {
            g = c.crypto.ECDSA.concatSigToASN1Sig(a);
        } catch (t) {
            t = VM2_INTERNAL_STATE_DO_NOT_USE_OR_PROGRAM_WILL_FAIL.handleException(t);
            return !1;
        }
        return (d = new c.crypto.Signature({
            alg: l
        })).init(e), d.updateString(s), d.verify(g);
    }
    return (d = new c.crypto.Signature({
        alg: l
    })).init(e), d.updateString(s), d.verify(a);
}, c.jws.JWS.parse = function(t) {
    var e, i, n, r = t.split("."), s = {};
    if (2 != r.length && 3 != r.length) throw "malformed sJWS: wrong number of '.' splitted elements";
    return e = r[0], i = r[1], 3 == r.length && (n = r[2]), s.headerObj = c.jws.JWS.readSafeJSONString(at(e)), 
    s.payloadObj = c.jws.JWS.readSafeJSONString(at(i)), s.headerPP = JSON.stringify(s.headerObj, null, "  "), 
    null == s.payloadObj ? s.payloadPP = at(i) : s.payloadPP = JSON.stringify(s.payloadObj, null, "  "), 
    void 0 !== n && (s.sigHex = dt(n)), s;
}, c.jws.JWS.verifyJWT = function(t, e, i) {
    var n = c.jws.JWS, r = t.split("."), s = r[0], o = r[1], h = (dt(r[2]), n.readSafeJSONString(at(s))), u = n.readSafeJSONString(at(o));
    if (void 0 === h.alg) return !1;
    if (void 0 === i.alg) throw "acceptField.alg shall be specified";
    if (!n.inArray(h.alg, i.alg)) return !1;
    if (void 0 !== u.iss && "object" === a(i.iss) && !n.inArray(u.iss, i.iss)) return !1;
    if (void 0 !== u.sub && "object" === a(i.sub) && !n.inArray(u.sub, i.sub)) return !1;
    if (void 0 !== u.aud && "object" === a(i.aud)) if ("string" == typeof u.aud) {
        if (!n.inArray(u.aud, i.aud)) return !1;
    } else if ("object" == a(u.aud) && !n.includedArray(u.aud, i.aud)) return !1;
    var l = c.jws.IntDate.getNow();
    return void 0 !== i.verifyAt && "number" == typeof i.verifyAt && (l = i.verifyAt), 
    void 0 !== i.gracePeriod && "number" == typeof i.gracePeriod || (i.gracePeriod = 0), 
    !(void 0 !== u.exp && "number" == typeof u.exp && u.exp + i.gracePeriod < l) && (!(void 0 !== u.nbf && "number" == typeof u.nbf && l < u.nbf - i.gracePeriod) && (!(void 0 !== u.iat && "number" == typeof u.iat && l < u.iat - i.gracePeriod) && ((void 0 === u.jti || void 0 === i.jti || u.jti === i.jti) && !!c.jws.JWS.verify(t, e, i.alg))));
}, c.jws.JWS.includedArray = function(t, e) {
    var i = c.jws.JWS.inArray;
    if (null === t) return !1;
    if ("object" !== a(t)) return !1;
    if ("number" != typeof t.length) return !1;
    for (var n = 0; n < t.length; n++) if (!i(t[n], e)) return !1;
    return !0;
}, c.jws.JWS.inArray = function(t, e) {
    if (null === e) return !1;
    if ("object" !== a(e)) return !1;
    if ("number" != typeof e.length) return !1;
    for (var i = 0; i < e.length; i++) if (e[i] == t) return !0;
    return !1;
}, c.jws.JWS.jwsalg2sigalg = {
    HS256: "HmacSHA256",
    HS384: "HmacSHA384",
    HS512: "HmacSHA512",
    RS256: "SHA256withRSA",
    RS384: "SHA384withRSA",
    RS512: "SHA512withRSA",
    ES256: "SHA256withECDSA",
    ES384: "SHA384withECDSA",
    PS256: "SHA256withRSAandMGF1",
    PS384: "SHA384withRSAandMGF1",
    PS512: "SHA512withRSAandMGF1",
    none: "none"
}, c.jws.JWS.isSafeJSONString = function(t, e, i) {
    var n = null;
    try {
        return n = rt(t), "object" != a(n) ? 0 : n.constructor === Array ? 0 : (e && (e[i] = n), 
        1);
    } catch (t) {
        t = VM2_INTERNAL_STATE_DO_NOT_USE_OR_PROGRAM_WILL_FAIL.handleException(t);
        return 0;
    }
}, c.jws.JWS.readSafeJSONString = function(t) {
    var e = null;
    try {
        return e = rt(t), "object" != a(e) || e.constructor === Array ? null : e;
    } catch (t) {
        t = VM2_INTERNAL_STATE_DO_NOT_USE_OR_PROGRAM_WILL_FAIL.handleException(t);
        return null;
    }
}, c.jws.JWS.getEncodedSignatureValueFromJWS = function(t) {
    var e = t.match(/^[^.]+\.[^.]+\.([^.]+)$/);
    if (null == e) throw "JWS signature is not a form of 'Head.Payload.SigValue'.";
    return e[1];
}, c.jws.JWS.getJWKthumbprint = function(t) {
    if ("RSA" !== t.kty && "EC" !== t.kty && "oct" !== t.kty) throw "unsupported algorithm for JWK Thumprint";
    var e = "{";
    if ("RSA" === t.kty) {
        if ("string" != typeof t.n || "string" != typeof t.e) throw "wrong n and e value for RSA key";
        e += '"e":"' + t.e + '",', e += '"kty":"' + t.kty + '",', e += '"n":"' + t.n + '"}';
    } else if ("EC" === t.kty) {
        if ("string" != typeof t.crv || "string" != typeof t.x || "string" != typeof t.y) throw "wrong crv, x and y value for EC key";
        e += '"crv":"' + t.crv + '",', e += '"kty":"' + t.kty + '",', e += '"x":"' + t.x + '",', 
        e += '"y":"' + t.y + '"}';
    } else if ("oct" === t.kty) {
        if ("string" != typeof t.k) throw "wrong k value for oct(symmetric) key";
        e += '"kty":"' + t.kty + '",', e += '"k":"' + t.k + '"}';
    }
    var i = vt(e);
    return ft(c.crypto.Util.hashHex(i, "sha256"));
}, c.jws.IntDate = {}, c.jws.IntDate.get = function(t) {
    if ("now" == t) return c.jws.IntDate.getNow();
    if ("now + 1hour" == t) return c.jws.IntDate.getNow() + 3600;
    if ("now + 1day" == t) return c.jws.IntDate.getNow() + 86400;
    if ("now + 1month" == t) return c.jws.IntDate.getNow() + 2592e3;
    if ("now + 1year" == t) return c.jws.IntDate.getNow() + 31536e3;
    if (t.match(/Z$/)) return c.jws.IntDate.getZulu(t);
    if (t.match(/^[0-9]+$/)) return parseInt(t);
    throw "unsupported format: " + t;
}, c.jws.IntDate.getZulu = function(t) {
    var e = t.match(/(\d+)(\d\d)(\d\d)(\d\d)(\d\d)(\d\d)Z/);
    if (e) {
        var i = e[1], n = parseInt(i);
        if (4 == i.length) ; else {
            if (2 != i.length) throw "malformed year string";
            if (50 <= n && n < 100) n = 1900 + n; else {
                if (!(0 <= n && n < 50)) throw "malformed year string for UTCTime";
                n = 2e3 + n;
            }
        }
        var r = parseInt(e[2]) - 1, s = parseInt(e[3]), a = parseInt(e[4]), o = parseInt(e[5]), h = parseInt(e[6]);
        return ~~(new Date(Date.UTC(n, r, s, a, o, h)) / 1e3);
    }
    throw "unsupported format: " + t;
}, c.jws.IntDate.getNow = function() {
    return ~~(new Date() / 1e3);
}, c.jws.IntDate.intDate2UTCString = function(t) {
    return new Date(1e3 * t).toUTCString();
}, c.jws.IntDate.intDate2Zulu = function(t) {
    var e = new Date(1e3 * t);
    return ("0000" + e.getUTCFullYear()).slice(-4) + ("00" + (e.getUTCMonth() + 1)).slice(-2) + ("00" + e.getUTCDate()).slice(-2) + ("00" + e.getUTCHours()).slice(-2) + ("00" + e.getUTCMinutes()).slice(-2) + ("00" + e.getUTCSeconds()).slice(-2) + "Z";
}, void 0 !== c && c || (c = {}), void 0 !== c.jws && c.jws || (c.jws = {}), c.jws.JWSJS = function() {
    var t = c.jws.JWS, e = c.jws.JWS;
    this.aHeader = [], this.sPayload = "", this.aSignature = [], this.init = function() {
        this.aHeader = [], this.sPayload = void 0, this.aSignature = [];
    }, this.initWithJWS = function(t) {
        this.init();
        var e = t.split(".");
        if (3 != e.length) throw "malformed input JWS";
        this.aHeader.push(e[0]), this.sPayload = e[1], this.aSignature.push(e[2]);
    }, this.addSignature = function(t, e, i, n) {
        if (void 0 === this.sPayload || null === this.sPayload) throw "there's no JSON-JS signature to add.";
        var r = this.aHeader.length;
        if (this.aHeader.length != this.aSignature.length) throw "aHeader.length != aSignature.length";
        try {
            var s = c.jws.JWS.sign(t, e, this.sPayload, i, n).split(".");
            s[0], s[2];
            this.aHeader.push(s[0]), this.aSignature.push(s[2]);
        } catch (t) {
            t = VM2_INTERNAL_STATE_DO_NOT_USE_OR_PROGRAM_WILL_FAIL.handleException(t);
            throw this.aHeader.length > r && this.aHeader.pop(), this.aSignature.length > r && this.aSignature.pop(), 
            "addSignature failed: " + t;
        }
    }, this.addSignatureByHeaderKey = function(t, e) {
        var i = at(this.sPayload), n = new c.jws.JWS();
        n.generateJWSByP1PrvKey(t, i, e);
        this.aHeader.push(n.parsedJWS.headB64U), this.aSignature.push(n.parsedJWS.sigvalB64U);
    }, this.addSignatureByHeaderPayloadKey = function(t, e, i) {
        var n = new c.jws.JWS();
        n.generateJWSByP1PrvKey(t, e, i);
        this.aHeader.push(n.parsedJWS.headB64U), this.sPayload = n.parsedJWS.payloadB64U, 
        this.aSignature.push(n.parsedJWS.sigvalB64U);
    }, this.verifyAll = function(t) {
        if (this.aHeader.length !== t.length || this.aSignature.length !== t.length) return !1;
        for (var e = 0; e < t.length; e++) {
            var i = t[e];
            if (2 !== i.length) return !1;
            if (!1 === this.verifyNth(e, i[0], i[1])) return !1;
        }
        return !0;
    }, this.verifyNth = function(t, i, n) {
        if (this.aHeader.length <= t || this.aSignature.length <= t) return !1;
        var r = this.aHeader[t], s = this.aSignature[t], a = r + "." + this.sPayload + "." + s, o = !1;
        try {
            o = e.verify(a, i, n);
        } catch (t) {
            t = VM2_INTERNAL_STATE_DO_NOT_USE_OR_PROGRAM_WILL_FAIL.handleException(t);
            return !1;
        }
        return o;
    }, this.verifyWithCerts = function(t) {
        if (this.aHeader.length != t.length) throw "num headers does not match with num certs";
        if (this.aSignature.length != t.length) throw "num signatures does not match with num certs";
        for (var e = this.sPayload, i = "", n = 0; n < t.length; n++) {
            var r = t[n], s = this.aHeader[n] + "." + e + "." + this.aSignature[n], a = new c.jws.JWS();
            try {
                1 != a.verifyJWSByPemX509Cert(s, r) && (i += n + 1 + "th signature unmatch. ");
            } catch (t) {
                t = VM2_INTERNAL_STATE_DO_NOT_USE_OR_PROGRAM_WILL_FAIL.handleException(t);
                i += n + 1 + "th signature fail(" + t + "). ";
            }
        }
        if ("" == i) return 1;
        throw i;
    }, this.readJWSJS = function(e) {
        if ("string" == typeof e) {
            var i = t.readSafeJSONString(e);
            if (null == i) throw "argument is not safe JSON object string";
            this.aHeader = i.headers, this.sPayload = i.payload, this.aSignature = i.signatures;
        } else try {
            if (!(e.headers.length > 0)) throw "malformed header";
            if (this.aHeader = e.headers, "string" != typeof e.payload) throw "malformed signatures";
            if (this.sPayload = e.payload, !(e.signatures.length > 0)) throw "malformed signatures";
            this.signatures = e.signatures;
        } catch (t) {
            t = VM2_INTERNAL_STATE_DO_NOT_USE_OR_PROGRAM_WILL_FAIL.handleException(t);
            throw "malformed JWS-JS JSON object: " + t;
        }
    }, this.getJSON = function() {
        return {
            headers: this.aHeader,
            payload: this.sPayload,
            signatures: this.aSignature
        };
    }, this.isEmpty = function() {
        return 0 == this.aHeader.length ? 1 : 0;
    };
}, module.exports = {
    RSAKey: Q,
    KEYUTIL: Ft,
    hex2b64: v,
    b64tohex: S
};