var n = function(n) {
    return (n = n.toString())[1] ? n : "0" + n;
};

var t = 0;

module.exports = {
    showModel: function() {
        wx.showModal({
            title: "提示",
            content: "您还没有订购12580惠出行业务哟,赶紧去订购吧！",
            cancelText: "再看看",
            confirmText: "去订购",
            confirmColor: "#3C83EB",
            success: function(n) {
                n.confirm ? wx.navigateTo({
                    url: "/packagehn/pages/index/businessHandlinghn/businessHandling"
                }) : n.cancel && console.log("用户点击取消");
            }
        });
    },
    delayAwait: function n(e) {
        var r = !(arguments.length > 1 && void 0 !== arguments[1]) || arguments[1];
        return r && (t = 0), new Promise(function(n, r) {
            var o = e();
            return o ? n(o) : t < 100 ? r() : n();
        }).catch(function() {
            return new Promise(function(n) {
                t++, setTimeout(n, 200);
            }).then(function() {
                return n(e, !1);
            });
        });
    },
    unction: function(n, t) {
        null != t && null != t || (t = 2e3);
        var e = null;
        return function() {
            var r = +new Date();
            (r - e > t || !e) && (n.apply(this, arguments), e = r);
        };
    },
    formatTime: function(t) {
        var e = t.getFullYear(), r = t.getMonth() + 1, o = t.getDate(), a = t.getHours(), i = t.getMinutes(), u = t.getSeconds();
        return [ e, r, o ].map(n).join("-") + " " + [ a, i, u ].map(n).join(":");
    },
    formatTime1: function(t) {
        t.getFullYear(), t.getMonth(), t.getDate();
        return [ t.getHours(), t.getMinutes(), t.getSeconds() ].map(n).join(":");
    },
    formatDate: function(n) {
        var t = n.replace(/-/g, "/");
        return new Date(t).getTime();
    },
    errImg: function(n, t) {
        var e = {};
        e[n.target.dataset.errImg] = "", t.setData(e);
    },
    getRandomchar: function(n) {
        for (var t = [ "a", "b", "c", "d", "e", "f", "g", "h", "i", "j", "k", "l", "m", "n", "o", "p", "q", "r", "s", "t", "u", "v", "w", "x", "y", "z", "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z", "0", "1", "2", "3", "4", "5", "6", "7", "8", "9" ], e = "", r = t.length, o = 0; o < n; o++) {
            e += t[parseInt(Math.random() * r)];
        }
        return e;
    }
};