// 测试页面JS文件 - 可以放在任意页面中使用
var CryptoTester = require("../../test_crypto.js");
var transmission = require("../../utils/transmission.js").rsaAesJs;

Page({
    data: {
        testResults: []
    },
    
    onLoad: function() {
        console.log("加密解密测试页面加载完成");
    },
    
    // 运行基础测试
    runBasicTest: function() {
        console.log("=== 开始基础测试 ===");
        
        // 测试简单字符串加密解密
        var testString = "测试数据123";
        console.log("测试字符串:", testString);
        
        try {
            // 加密
            var encrypted = transmission.EncryptDataHn(testString);
            console.log("加密结果:", encrypted);
            
            // 模拟服务器响应格式
            var serverResponse = {
                key: encrypted.ksynum,
                content: encrypted.cmcxncxn,
                iv: encrypted.avd
            };
            
            // 解密
            var decrypted = transmission.DecryptKeyHn(serverResponse);
            console.log("解密结果:", decrypted);
            
            // 验证
            var success = testString === decrypted;
            console.log("测试结果:", success ? "✅ 成功" : "❌ 失败");
            
            this.addTestResult("基础字符串测试", testString, decrypted, success);
            
        } catch (error) {
            console.error("基础测试失败:", error);
            this.addTestResult("基础字符串测试", testString, "错误: " + error.message, false);
        }
    },
    
    // 测试JSON对象
    runJsonTest: function() {
        console.log("=== 开始JSON对象测试 ===");
        
        var testData = {
            sCode: "SJS_CODE_TEST",
            areaCode: wx.getStorageSync("cityCode_hn") || "430100",
            timestamp: new Date().getTime(),
            userId: "test_user_123"
        };
        
        var testString = JSON.stringify(testData);
        console.log("测试JSON:", testString);
        
        try {
            // 加密
            var encrypted = transmission.EncryptDataHn(testString);
            console.log("JSON加密结果:", encrypted);
            
            // 模拟服务器响应
            var serverResponse = {
                key: encrypted.ksynum,
                content: encrypted.cmcxncxn,
                iv: encrypted.avd
            };
            
            // 解密
            var decrypted = transmission.DecryptKeyHn(serverResponse);
            console.log("JSON解密结果:", decrypted);
            
            // 验证
            var success = testString === JSON.stringify(decrypted);
            console.log("JSON测试结果:", success ? "✅ 成功" : "❌ 失败");
            
            this.addTestResult("JSON对象测试", testData, decrypted, success);
            
        } catch (error) {
            console.error("JSON测试失败:", error);
            this.addTestResult("JSON对象测试", testData, "错误: " + error.message, false);
        }
    },
    
    // 测试实际业务数据格式
    runBusinessTest: function() {
        console.log("=== 开始业务数据测试 ===");
        
        // 模拟实际业务中的数据格式
        var provinceCode = wx.getStorageSync("provinceCode") || "43";
        var latitude = wx.getStorageSync("latitude") || "28.2282";
        var longitude = wx.getStorageSync("longitude") || "112.9388";
        var token = wx.getStorageSync("token_hn") || "test_token";
        var telephone = wx.getStorageSync("telephone_hn") || "***********";
        var feeAppCode = wx.getStorageSync("feeAppCode_hn") || "APP001";
        
        var businessData = provinceCode + "&" + "business_handling_code" + "&" + 
                          latitude + "&" + longitude + "&" + token + "&" + 
                          telephone + "&" + feeAppCode + "&" + "/pages/index/index" + "&" + "mini";
        
        console.log("业务数据字符串:", businessData);
        
        try {
            // 加密
            var encrypted = transmission.EncryptDataHn(businessData);
            console.log("业务数据加密结果:", encrypted);
            
            // 模拟服务器响应
            var serverResponse = {
                key: encrypted.ksynum,
                content: encrypted.cmcxncxn,
                iv: encrypted.avd
            };
            
            // 解密
            var decrypted = transmission.DecryptKeyHn(serverResponse);
            console.log("业务数据解密结果:", decrypted);
            
            // 验证
            var success = businessData === decrypted;
            console.log("业务数据测试结果:", success ? "✅ 成功" : "❌ 失败");
            
            this.addTestResult("业务数据测试", businessData, decrypted, success);
            
        } catch (error) {
            console.error("业务数据测试失败:", error);
            this.addTestResult("业务数据测试", businessData, "错误: " + error.message, false);
        }
    },
    
    // 运行所有测试
    runAllTests: function() {
        console.log("========== 开始运行所有测试 ==========");
        
        // 清空之前的测试结果
        this.setData({
            testResults: []
        });
        
        // 依次运行各项测试
        this.runBasicTest();
        
        setTimeout(() => {
            this.runJsonTest();
        }, 1000);
        
        setTimeout(() => {
            this.runBusinessTest();
        }, 2000);
        
        setTimeout(() => {
            console.log("========== 所有测试完成 ==========");
            this.showTestSummary();
        }, 3000);
    },
    
    // 添加测试结果
    addTestResult: function(testName, original, decrypted, success) {
        var results = this.data.testResults;
        results.push({
            name: testName,
            original: original,
            decrypted: decrypted,
            success: success,
            time: new Date().toLocaleTimeString()
        });
        
        this.setData({
            testResults: results
        });
    },
    
    // 显示测试摘要
    showTestSummary: function() {
        var results = this.data.testResults;
        var successCount = results.filter(r => r.success).length;
        var totalCount = results.length;
        
        var summary = "测试摘要:\n" +
                     "总测试数: " + totalCount + "\n" +
                     "成功: " + successCount + "\n" +
                     "失败: " + (totalCount - successCount) + "\n" +
                     "成功率: " + Math.round(successCount / totalCount * 100) + "%";
        
        console.log(summary);
        
        wx.showModal({
            title: "测试完成",
            content: summary,
            showCancel: false
        });
    },
    
    // 测试实际网络请求（可选）
    testRealRequest: function() {
        console.log("=== 测试实际网络请求 ===");
        
        var testData = {
            sCode: "SJS_CODE_TEST",
            areaCode: wx.getStorageSync("cityCode_hn") || "430100"
        };
        
        var dataString = JSON.stringify(testData);
        console.log("请求原始数据:", dataString);
        
        // 加密数据
        var encryptedData = transmission.EncryptDataHn(dataString);
        console.log("加密后的请求数据:", encryptedData);
        
        // 这里可以发起实际的网络请求来测试
        // 注意：这会发起真实的网络请求，请谨慎使用
        /*
        var http = require("../../utils/http.js");
        http.POSTHN("/coupon/selectCodeDetailsBySCode", encryptedData).then(function(response) {
            console.log("服务器响应（已自动解密）:", response);
        }).catch(function(error) {
            console.error("网络请求失败:", error);
        });
        */
    }
});
