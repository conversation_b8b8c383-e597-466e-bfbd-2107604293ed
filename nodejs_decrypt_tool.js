/**
 * Node.js版本的湖南解密工具
 * 使用Node.js的crypto模块进行真实的RSA+AES解密
 * 
 * 使用方法：
 * node nodejs_decrypt_tool.js
 */

// 检查是否在Node.js环境中
if (typeof require === 'undefined') {
    console.error("❌ 此工具需要在Node.js环境中运行");
    console.log("💡 请安装Node.js并使用命令: node nodejs_decrypt_tool.js");
} else {
    
    const crypto = require('crypto');
    
    var NodeDecryptTool = {
        
        // 湖南版本的RSA私钥
        HN_PRIVATE_KEY: `**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`,

        // Base64转Hex
        b64tohex: function(b64) {
            try {
                var binary = Buffer.from(b64, 'base64').toString('binary');
                var hex = '';
                for (var i = 0; i < binary.length; i++) {
                    var h = binary.charCodeAt(i).toString(16);
                    hex += h.length === 1 ? '0' + h : h;
                }
                return hex;
            } catch (e) {
                console.error("❌ Base64转Hex失败:", e.message);
                return null;
            }
        },

        // RSA解密
        rsaDecrypt: function(encryptedHex) {
            try {
                console.log("🔓 RSA解密中...");
                console.log("🔑 使用湖南版本RSA私钥");
                console.log("📄 加密数据(hex):", encryptedHex.substring(0, 100) + "...");
                
                // 将hex转换为Buffer
                var encryptedBuffer = Buffer.from(encryptedHex, 'hex');
                
                // RSA解密
                var decrypted = crypto.privateDecrypt(
                    {
                        key: this.HN_PRIVATE_KEY,
                        padding: crypto.constants.RSA_PKCS1_PADDING
                    },
                    encryptedBuffer
                );
                
                var result = decrypted.toString('utf8');
                console.log("✅ RSA解密成功，AES密钥:", result);
                return result;
                
            } catch (e) {
                console.error("❌ RSA解密失败:", e.message);
                return null;
            }
        },

        // AES解密
        aesDecrypt: function(ciphertext, key, iv) {
            try {
                console.log("🔓 AES解密中...");
                console.log("🔑 AES密钥:", key);
                console.log("🎲 IV:", iv);
                console.log("📄 密文:", ciphertext.substring(0, 100) + "...");
                
                // 创建解密器
                var decipher = crypto.createDecipheriv('aes-256-cbc', Buffer.from(key, 'utf8'), Buffer.from(iv, 'utf8'));
                
                // 解密
                var decrypted = decipher.update(ciphertext, 'base64', 'utf8');
                decrypted += decipher.final('utf8');
                
                console.log("✅ AES解密成功");
                return decrypted;
                
            } catch (e) {
                console.error("❌ AES解密失败:", e.message);
                console.log("💡 尝试不同的AES模式...");
                
                // 尝试其他可能的配置
                try {
                    // 尝试截取密钥到合适长度
                    var keyBuffer = Buffer.from(key, 'utf8');
                    if (keyBuffer.length > 32) {
                        keyBuffer = keyBuffer.slice(0, 32);
                    } else if (keyBuffer.length < 32) {
                        var paddedKey = Buffer.alloc(32);
                        keyBuffer.copy(paddedKey);
                        keyBuffer = paddedKey;
                    }
                    
                    var ivBuffer = Buffer.from(iv, 'utf8');
                    if (ivBuffer.length > 16) {
                        ivBuffer = ivBuffer.slice(0, 16);
                    } else if (ivBuffer.length < 16) {
                        var paddedIv = Buffer.alloc(16);
                        ivBuffer.copy(paddedIv);
                        ivBuffer = paddedIv;
                    }
                    
                    var decipher2 = crypto.createDecipheriv('aes-256-cbc', keyBuffer, ivBuffer);
                    var decrypted2 = decipher2.update(ciphertext, 'base64', 'utf8');
                    decrypted2 += decipher2.final('utf8');
                    
                    console.log("✅ AES解密成功（调整后）");
                    return decrypted2;
                    
                } catch (e2) {
                    console.error("❌ AES解密最终失败:", e2.message);
                    return null;
                }
            }
        },

        // 主解密函数
        decryptResponse: function(responseData) {
            console.log("\n========== 湖南版本真实数据解密 ==========");
            console.log("📦 输入数据:", {
                key: responseData.key.substring(0, 50) + "...",
                content: responseData.content.substring(0, 50) + "...",
                iv: responseData.iv
            });

            try {
                // 1. Base64转Hex
                console.log("\n🔧 步骤1: Base64转Hex");
                var hexKey = this.b64tohex(responseData.key);
                if (!hexKey) {
                    throw new Error("密钥格式转换失败");
                }

                // 2. RSA解密AES密钥
                console.log("\n🔓 步骤2: RSA解密AES密钥");
                var aesKey = this.rsaDecrypt(hexKey);
                if (!aesKey) {
                    throw new Error("RSA解密失败");
                }

                // 3. AES解密内容
                console.log("\n🔓 步骤3: AES解密内容");
                var decryptedContent = this.aesDecrypt(responseData.content, aesKey, responseData.iv);
                if (!decryptedContent) {
                    throw new Error("AES解密失败");
                }

                // 4. 解析JSON
                console.log("\n📋 步骤4: 解析JSON");
                try {
                    var result = JSON.parse(decryptedContent);
                    console.log("🎯 最终解密结果:", JSON.stringify(result, null, 2));
                    return result;
                } catch (jsonError) {
                    console.log("⚠️  JSON解析失败，返回原始字符串:");
                    console.log("📄 解密内容:", decryptedContent);
                    return decryptedContent;
                }

            } catch (error) {
                console.error("❌ 解密失败:", error.message);
                return null;
            }
        },

        // 测试你提供的真实数据
        testRealData: function() {
            console.log("🧪 测试你提供的真实响应数据:");
            
            var realResponseData = {
                "content": "Z5KNaR1xKweWGg2UUKPJ1xuppg7NE+MSeduoUrt7OlfofxEG6MA/EmFSVN1PN3TPOk2L9yWmtpwyxCPCrO77GhHZ889e7oTvXRVRM6F5vsQKIOuCY2NxFgqCt996rz1jCjP2nNkexi5UDgC+FYLGtAjsyR0bymB93y2TfMz/JK78PO47882bxE+2odl3BrIUD3QZ299aMMtsfirDcFmdgoS5Y72DGUHul1o6KCtP2AK7JmObE9oJUeodcspplF2PJ4Lg7s/kTgXxruU/fnfwUo5QrzTCmRaHK9QOBMTn4hxGXbNWXjhSB92e30K/E07zyLZBk8yfqSLlM6U94T/128M8i0Lnc9aCKIM7YmeSeHiZh+BlXM4O4gj8bk51PNNKsVeW7owo5TCBUkbyXLb6zA==",
                "key": "S5OUYtFrpKMZUGXPDE7TTT0HvZ0dCQpyA6DNznsWnUTMSFtkAXvRSPMWSve+z8Gsv4xOJJtgPLrAw5qeqly0HHgZpWyjDzOjahjeaJdkA7zoUtayySGX3yfUSCja1NtJOPGCn5OzJ6JJTg5OCfDdH0rj1KB06zHQ0YtsP5MTaMGSBj2ZkU4AkbHF3/MfpSuDnUYIevWg+uv3ywi3h5zBwyL8RjiqoVzGDy6pwLmp8o2Z+Gj6IJ8QYooItYNaQ7SG196vO8xfZd+sRm/RHen9dk4WqAEV8z6/FHkgvoGAVPrJVHLcNBEJee5irNIrQxeNP17UkOytbEvSxAkNXUPK4A==",
                "iv": "hihSPyR1EvP5$*!*"
            };

            return this.decryptResponse(realResponseData);
        }
    };

    // 使用说明
    console.log("🎉 Node.js版湖南解密工具已加载！");
    console.log("📖 使用方法:");
    console.log("1. NodeDecryptTool.testRealData() - 测试你提供的真实数据");
    console.log("2. NodeDecryptTool.decryptResponse(data) - 解密任意响应数据");

    // 自动测试真实数据
    console.log("\n🔥 自动测试你提供的真实数据:");
    var result = NodeDecryptTool.testRealData();

    if (result) {
        console.log("\n🎉 解密成功！");
    } else {
        console.log("\n❌ 解密失败，请检查数据格式或密钥");
    }

    // 导出模块
    module.exports = NodeDecryptTool;
}
