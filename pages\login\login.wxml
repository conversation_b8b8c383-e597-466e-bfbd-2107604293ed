<view class="container bgWhite">
    <view class="swiper_banner">
        <swiper autoplay="{{true}}" circular="{{true}}" duration="{{500}}" indicatorActiveColor="#fff" indicatorColor="rgba(255, 255, 255, .3)" indicatorDots="{{true}}" interval="{{5000}}" style="height:649rpx;">
            <swiper-item wx:for="{{bannerUrls}}" wx:key="*this">
                <view class="swiper-item">
                    <view class="swiper_banner">
                        <image src="{{item.image}}"></image>
                    </view>
                </view>
            </swiper-item>
        </swiper>
    </view>
    <view class="login-box">
        <view class="mobile">
            <image class="icon" mode="widthFix" src="/static/image/phone.png"></image>
            <input clearable bindinput="bindKeyInputTel" cursorSpacing="140" maxlength="11" placeholder="请输入移动手机号码" type="number" value="{{telValue}}"></input>
        </view>
        <view class="txm" wx:if="{{isInputTel}}">
            <image class="icon" mode="widthFix" src="/static/image/code.png"></image>
            <input bindblur="bindblurChange" bindinput="bindKeyInputYzm" cursorSpacing="140" maxlength="4" placeholder="请输入右侧验证码" type="number" value="{{yzmValue}}"></input>
            <image bindtap="changeImg" class="yzm" mode="widthFix" src="{{yzmImgUrl}}"></image>
        </view>
        <view class="dxyzm">
            <image class="icon" mode="widthFix" src="/static/image/code_no.png"></image>
            <input bindinput="bindKeyInputCode" cursorSpacing="140" maxlength="6" placeholder="请输入短信验证码" type="number" value="{{codeValue}}"></input>
            <button bindtap="getCode" class="get-code ft13" disabled="{{disabled}}">{{yzmWord}}</button>
        </view>
    </view>
    <view bindtap="handleLogin" class="login-btn">
        <van-button block color="#DC3232" disabled="{{isDisabled}}">登录</van-button>
    </view>
    <view class="login-tips">
        <van-checkbox bind:change="onCheckboxChange" checkedColor="#DD3232" iconSize="18px" shape="square" value="{{checked}}">
            <text>已阅读</text>
            <text bindtap="viewDetail">《中国移动江苏公司个人信息保护政策》</text>
        </van-checkbox>
    </view>
</view>
<van-toast id="van-toast"></van-toast>
<van-toast id="custom-selector"></van-toast>
<view class="copyright tc ft12">
    <view style="font-size:22rpx;font-family:PingFang SC;color:rgba(157,156,156,1);">©中国移动通信集团江苏有限公司版权所有</view>
    <view style="font-size:22rpx;font-family:PingFang SC;color:rgba(157,156,156,1);">苏 ICP备11070397号-21</view>
    <view style="font-size:22rpx;font-family:PingFang SC;color:rgba(157,156,156,1);">（中国移动通信集团委托江苏有限公司支撑全国12580惠出行全网平台）</view>
</view>
<van-loading class="loading" color="#DD3231" type="spinner" wx:if="{{isLoading}}"></van-loading>
<van-dialog showCancelButton bind:cancel="onClose" bind:confirm="onConFirm" cancelButtonText="取消" confirmButtonText="已阅读并同意" id="van-dialog" message="请阅读并同意《中国移动江苏公司个人信息保护政策》后进行登录" show="{{dialogShow}}" title="提示"></van-dialog>
