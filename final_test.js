/**
 * 最终测试文件
 * 
 * 运行前请先安装依赖：
 * npm install jsrsasign crypto-js
 * 
 * 然后运行：
 * node final_test.js
 */

try {
    var FinalCryptoTool = require('./final_crypto_tool.js');
    console.log("🎯 最终版本12580小程序加密解密测试\n");
} catch (e) {
    console.error("❌ 加载失败:", e.message);
    console.log("💡 请先安装依赖：npm install jsrsasign crypto-js");
    process.exit(1);
}

// ==================== 测试1：基础加密解密 ====================
console.log("=".repeat(60));
console.log("📋 测试1：基础加密解密");

var testData = {
    sCode: "SJS_CODE_12345",
    areaCode: "430100"
};

console.log("🎯 测试数据:", testData);

var encrypted = FinalCryptoTool.EncryptDataHn(testData);
if (encrypted) {
    console.log("🔐 加密成功");
    console.log("  avd:", encrypted.avd);
    console.log("  ksynum:", encrypted.ksynum.substring(0, 50) + "...");
    console.log("  cmcxncxn:", encrypted.cmcxncxn.substring(0, 50) + "...");
    
    // 模拟服务器响应格式
    var serverResponse = {
        key: encrypted.ksynum,
        content: encrypted.cmcxncxn,
        iv: encrypted.avd
    };
    
    // 解密
    var decrypted = FinalCryptoTool.DecryptKeyHn(serverResponse);
    if (decrypted) {
        console.log("🔓 解密成功:", decrypted);
        
        // 验证
        var originalStr = JSON.stringify(testData);
        var decryptedStr = JSON.stringify(decrypted);
        console.log("✅ 验证结果:", originalStr === decryptedStr ? "通过" : "失败");
    } else {
        console.log("❌ 解密失败");
    }
} else {
    console.log("❌ 加密失败");
}

// ==================== 测试2：解密真实数据 ====================
console.log("\n" + "=".repeat(60));
console.log("📋 测试2：解密你提供的真实数据");

var realResult = FinalCryptoTool.testRealData();
if (realResult) {
    console.log("🎉 真实数据解密成功！");
    console.log("🔓 解密结果:", realResult);
} else {
    console.log("❌ 真实数据解密失败");
}

// ==================== 测试3：不同数据类型 ====================
console.log("\n" + "=".repeat(60));
console.log("📋 测试3：不同数据类型测试");

var testCases = [
    "简单字符串",
    "包含中文的字符串测试",
    {name: "张三", age: 25, city: "长沙"},
    "43&business_handling_code&28.2282&112.9388&test_token&***********&APP001&/pages/index/index&mini"
];

testCases.forEach(function(testCase, index) {
    console.log("\n--- 测试用例 " + (index + 1) + " ---");
    console.log("数据:", testCase);
    
    var encrypted = FinalCryptoTool.EncryptDataHn(testCase);
    if (encrypted) {
        var response = {
            key: encrypted.ksynum,
            content: encrypted.cmcxncxn,
            iv: encrypted.avd
        };
        var decrypted = FinalCryptoTool.DecryptKeyHn(response);
        
        var originalStr = typeof testCase === 'object' ? JSON.stringify(testCase) : String(testCase);
        var decryptedStr = typeof decrypted === 'object' ? JSON.stringify(decrypted) : String(decrypted);
        
        console.log("结果:", originalStr === decryptedStr ? "✅ 成功" : "❌ 失败");
        if (originalStr !== decryptedStr) {
            console.log("  原始:", originalStr);
            console.log("  解密:", decryptedStr);
        }
    } else {
        console.log("结果: ❌ 加密失败");
    }
});

console.log("\n🎉 所有测试完成！");
console.log("💡 如果测试通过，说明工具可以正常使用");
console.log("💡 你可以使用 FinalCryptoTool.EncryptDataHn() 和 FinalCryptoTool.DecryptKeyHn() 来处理数据");
