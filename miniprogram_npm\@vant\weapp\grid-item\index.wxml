<view bindtap="onClick" class="custom-class {{utils.bem( 'grid-item',{square:square} )}}" style="{{computed.wrapperStyle( {square:square,gutter:gutter,columnNum:columnNum,index:index} )}}">
    <view class="content-class {{utils.bem( 'grid-item__content',[ direction,{center:center,square:square,reverse:reverse,clickable:clickable,surround:border&&gutter} ] )}} {{border?'van-hairline--surround':''}}" style="{{computed.contentStyle( {square:square,gutter:gutter} )}}">
        <slot wx:if="{{useSlot}}"></slot>
        <block wx:else>
            <view class="van-grid-item__icon icon-class">
                <van-icon classPrefix="{{iconPrefix}}" color="{{iconColor}}" dot="{{dot}}" info="{{badge||info}}" name="{{icon}}" size="{{iconSize}}" wx:if="{{icon}}"></van-icon>
                <slot name="icon" wx:else></slot>
            </view>
            <view class="van-grid-item__text text-class">
                <text wx:if="{{text}}">{{text}}</text>
                <slot name="text" wx:else></slot>
            </view>
        </block>
    </view>
</view>
<wxs module="utils" src="../wxs/utils.wxs" />
<wxs module="computed" src="index.wxs" />