/**
 * RSA密钥对测试
 * 测试RSA加密解密是否正常工作
 */

const crypto = require('crypto');

// 湖南版本的RSA密钥对
var HN_PUBLIC_KEY = `-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAu1l6RjXiJBajEvWqcymhwlBX55m3HFUaGC7F2G51ytNJwsFON9jJqeAwB83+uUVJYM15MrBBMWAul0lGSmoRMsVnT1KR7p7UxnbGgdZT1NJNZgTu1J16MV98EUhImSk8jxrmoRdUGgDE1vvvlcOack8+xDDrF9x6tIl1Kyr7uFz1VXD17LTwJ5ajRtkp5Tszl+xs0fqKhxbXVknqCMGQcW9IGwNrr4/coaP+SmEUkIUQ5jKr69PI2ATjXghC7SUIfVOpplM0AV0PV7qvPYysyAXMjjieQ98jIOOYrEo7ay6qhtgiCiVr7k+bBi3gv9+2vlpMhPjdkAaIPwEnE40FOwIDAQAB
-----E<PERSON> PUBLIC KEY-----`;

var HN_PRIVATE_KEY = `**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`;

console.log("🔍 RSA密钥对测试");

// 测试短字符串
var testMessages = [
    "Hello",
    "12345",
    "test",
    "短密钥",
    "abcdefghijklmnop"  // 16字符
];

testMessages.forEach(function(message, index) {
    console.log("\n--- 测试 " + (index + 1) + ": '" + message + "' ---");
    
    try {
        // PKCS1加密
        var encrypted = crypto.publicEncrypt(
            {
                key: HN_PUBLIC_KEY,
                padding: crypto.constants.RSA_PKCS1_PADDING
            },
            Buffer.from(message, 'utf8')
        );
        
        console.log("✅ PKCS1加密成功，长度:", encrypted.length);
        
        // PKCS1解密
        try {
            var decrypted = crypto.privateDecrypt(
                {
                    key: HN_PRIVATE_KEY,
                    padding: crypto.constants.RSA_PKCS1_PADDING
                },
                encrypted
            );
            
            var result = decrypted.toString('utf8');
            console.log("✅ PKCS1解密成功:", result);
            console.log("✅ 验证:", message === result ? "通过" : "失败");
            
        } catch (decryptError) {
            console.error("❌ PKCS1解密失败:", decryptError.message);
        }
        
    } catch (encryptError) {
        console.error("❌ PKCS1加密失败:", encryptError.message);
    }
});

// 测试你提供的真实数据
console.log("\n=== 测试真实数据解密 ===");

var realData = {
    "key": "S5OUYtFrpKMZUGXPDE7TTT0HvZ0dCQpyA6DNznsWnUTMSFtkAXvRSPMWSve+z8Gsv4xOJJtgPLrAw5qeqly0HHgZpWyjDzOjahjeaJdkA7zoUtayySGX3yfUSCja1NtJOPGCn5OzJ6JJTg5OCfDdH0rj1KB06zHQ0YtsP5MTaMGSBj2ZkU4AkbHF3/MfpSuDnUYIevWg+uv3ywi3h5zBwyL8RjiqoVzGDy6pwLmp8o2Z+Gj6IJ8QYooItYNaQ7SG196vO8xfZd+sRm/RHen9dk4WqAEV8z6/FHkgvoGAVPrJVHLcNBEJee5irNIrQxeNP17UkOytbEvSxAkNXUPK4A=="
};

// Base64转Hex
function b64tohex(b64) {
    var binary = Buffer.from(b64, 'base64').toString('binary');
    var hex = '';
    for (var i = 0; i < binary.length; i++) {
        var h = binary.charCodeAt(i).toString(16);
        hex += h.length === 1 ? '0' + h : h;
    }
    return hex;
}

var hexKey = b64tohex(realData.key);
console.log("Hex密钥长度:", hexKey.length);
console.log("Hex密钥（前100字符）:", hexKey.substring(0, 100));

var encryptedBuffer = Buffer.from(hexKey, 'hex');
console.log("加密数据长度:", encryptedBuffer.length, "字节");

// 尝试用湖南版本私钥解密
try {
    var decrypted = crypto.privateDecrypt(
        {
            key: HN_PRIVATE_KEY,
            padding: crypto.constants.RSA_PKCS1_PADDING
        },
        encryptedBuffer
    );
    
    var result = decrypted.toString('utf8');
    console.log("✅ 湖南版本解密成功:", result);
    
} catch (e1) {
    console.log("❌ 湖南版本PKCS1解密失败:", e1.message);
    
    // 尝试OAEP
    try {
        var decrypted2 = crypto.privateDecrypt(
            {
                key: HN_PRIVATE_KEY,
                padding: crypto.constants.RSA_PKCS1_OAEP_PADDING,
                oaepHash: 'sha1'
            },
            encryptedBuffer
        );
        
        var result2 = decrypted2.toString('utf8');
        console.log("✅ 湖南版本OAEP解密成功:", result2);
        
    } catch (e2) {
        console.log("❌ 湖南版本OAEP解密失败:", e2.message);
    }
}

console.log("\n🎉 RSA测试完成");
