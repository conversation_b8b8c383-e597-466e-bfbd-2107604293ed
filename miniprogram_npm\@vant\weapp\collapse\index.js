var e = require("../common/component"), t = require("../common/relation");

(0, e.VantComponent)({
    relation: (0, t.useChildren)("collapse-item"),
    props: {
        value: {
            type: null,
            observer: "updateExpanded"
        },
        accordion: {
            type: <PERSON><PERSON><PERSON>,
            observer: "updateExpanded"
        },
        border: {
            type: <PERSON>olean,
            value: !0
        }
    },
    methods: {
        updateExpanded: function() {
            this.children.forEach(function(e) {
                e.updateExpanded();
            });
        },
        switch: function(e, t) {
            var n = this.data, o = n.accordion, i = n.value, a = e;
            e = o ? t ? e : "" : t ? (i || []).concat(e) : (i || []).filter(function(t) {
                return t !== e;
            }), t ? this.$emit("open", a) : this.$emit("close", a), this.$emit("change", e), 
            this.$emit("input", e);
        }
    }
});