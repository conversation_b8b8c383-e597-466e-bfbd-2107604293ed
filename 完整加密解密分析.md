# 小程序完整加密解密逻辑分析

## 🎯 项目概述

这是一个12580小程序项目，使用了 **RSA + AES 混合加密** 方案来保护数据传输安全。

## 📁 核心文件结构

```
utils/
├── transmission.js     # 主要的加密解密逻辑
├── aesFunction.js      # AES加密工具函数
├── aes.js             # CryptoJS AES实现
├── wx_rsa.js          # RSA加密实现
└── http.js            # 网络请求封装
```

## 🔐 加密解密架构

### 1. 双版本支持
项目支持两个版本的加密：
- **普通版本**: `EncryptData` / `DecryptKey`
- **湖南版本**: `EncryptDataHn` / `DecryptKeyHn`

### 2. 加密流程

#### 请求加密流程：
```
原始数据 → AES加密 → RSA加密AES密钥 → 发送到服务器
```

#### 响应解密流程：
```
服务器响应 → RSA解密AES密钥 → AES解密数据 → 原始数据
```

## 🔧 技术细节

### RSA密钥对

#### 普通版本：
- **公钥**: 用于加密AES密钥
```
-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA3wzK0OjysbU742VeREaGU9QSf/+T6OVTXKMY1qOOqUdP3yK0HaXbqAXIuIIOoy2vCsQU7KKs5RGbPVskjq8mkPJWxvuDayuATJl8OUdWnwQthcDwuJD1n//Bz9oVSJCIZ2So8OdqgcqELY715nLU5fpEnyk9QgIzjCjaZkOLd5gdzD2rUItCEt4WPGTAWNLMQmvI5Kvma0Ndrh1dGJlFxN5osDA60cSYThaoxL9pSpUv86XIB3QZTEn7wPXUe/roFouixs470k33kfK3UgyqDbVtL1EgYUpzL/xiF/HA5w7mXuB26Tuc1JaQ21BHqlsjbmaoaKtDylxWrZtUmmolGQIDAQAB
-----END PUBLIC KEY-----
```

- **私钥**: 用于解密AES密钥
```
-----BEGIN PRIVATE KEY-----
MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCUphDT9zC0Zen/e9HTtucsGM7YvRUvY8Uhj5jVUy2iH+DTguOf3QcNYRgrQcogplIT6f+WFKfZ/NmjGZIegVgivFp4UpJmqPOaz7LbddkJGDTT9QbWXEaKVUXqCr49wTgpF2h3j8kd4SpNekJE/kbwFIFQBUJG1Va/3+JnrDsfKZ7SiE+7nAO/GRLsfTBRCh6zpATyC9pB3I3ixMyHKkhg/dXeSo/HaYxXso3izv1BuQ2OZSxq1Wq8auuZfXKtki8saoy+1hO8ajtnv0S+CPOzpfJvmuaN5otsyVXlsJJ8Mdppy1v2J6/zqvFb2bSHYhoRotlO9BI/QAxPjBVkYQd5AgMBAAE...
-----END PRIVATE KEY-----
```

#### 湖南版本：
- **公钥**: 
```
-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAu1l6RjXiJBajEvWqcymhwlBX55m3HFUaGC7F2G51ytNJwsFON9jJqeAwB83+uUVJYM15MrBBMWAul0lGSmoRMsVnT1KR7p7UxnbGgdZT1NJNZgTu1J16MV98EUhImSk8jxrmoRdUGgDE1vvvlcOack8+xDDrF9x6tIl1Kyr7uFz1VXD17LTwJ5ajRtkp5Tszl+xs0fqKhxbXVknqCMGQcW9IGwNrr4/coaP+SmEUkIUQ5jKr69PI2ATjXghC7SUIfVOpplM0AV0PV7qvPYysyAXMjjieQ98jIOOYrEo7ay6qhtgiCiVr7k+bBi3gv9+2vlpMhPjdkAaIPwEnE40FOwIDAQAB
-----END PUBLIC KEY-----
```

- **私钥**: 
```
-----BEGIN PRIVATE KEY-----
MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQCAGbO/XDMBn9n56a3YWf47b//8QldG5hBUF0+aUSadgueMXiB78jK8A+dzktR7EPC3O8p5bS9BhY957zlwF+zTNqHkoLdMJIQsSBUXylN0ZKTUEda/iSHLle9rlqVHhabWoVq9TNl6/nQP9iBO0nVZuuf8P626M69DjaSkOkw5R5/1XYlIpyak6DyjfBdqKD6v2lefLry7LAOF893Q/09anyZ3TKrlHah09fbX6XbEfxkxqDAYzT0HIAh7lekML0d4Tekhrxrl+5IxKc6kSHHHP9GaE7GOMFBXWfdUoOj2FizHdpYsVvUvsFEBmq2MM2hHuh78/dSLeYMlPdtaYQfrAgMBAAE...
-----END PRIVATE KEY-----
```

### AES加密参数

- **算法**: AES-256-CBC
- **填充**: PKCS7
- **密钥长度**: 32字符（从UUID生成）
- **IV长度**: 16字符（从UUID生成）
- **编码**: Base64

### 数据格式

#### 请求加密后的数据格式：
```json
{
    "avd": "IV值（明文）",
    "ksynum": "RSA加密的AES密钥（Base64编码）",
    "cmcxncxn": "AES加密的数据（Base64编码）"
}
```

#### 服务器响应的数据格式：
```json
{
    "key": "RSA加密的AES密钥（Base64编码）",
    "content": "AES加密的数据（Base64编码）",
    "iv": "IV值（明文）"
}
```

## 🌐 网络请求流程

### 1. 请求发送
```javascript
// 1. 准备原始数据
var originalData = {sCode: "SJS_CODE_12345", areaCode: "430100"};
var dataString = JSON.stringify(originalData);

// 2. 加密数据
var encryptedData = transmission.EncryptDataHn(dataString);

// 3. 发送请求
http.POSTHN("/coupon/selectCodeDetailsBySCode", encryptedData);
```

### 2. 响应处理
```javascript
// http.js 中自动处理响应解密
success: function(response) {
    if (response.data && response.statusCode === 200) {
        // 自动解密响应数据
        var decryptedData = transmission.DecryptKeyHn(response.data);
        // 处理解密后的数据...
    }
}
```

## 📋 实际使用场景

### 1. 优惠券查询
```javascript
var couponData = {
    sCode: "SJS_CODE_12345",
    areaCode: "430100"
};
var encrypted = transmission.EncryptDataHn(JSON.stringify(couponData));
http.POSTHN("/coupon/selectCodeDetailsBySCode", encrypted);
```

### 2. 业务办理
```javascript
var businessData = provinceCode + "&" + "business_handling_code" + "&" + 
                   latitude + "&" + longitude + "&" + token + "&" + 
                   telephone + "&" + feeAppCode + "&" + "/pages/index/index" + "&" + "mini";
var encrypted = transmission.EncryptDataHn(businessData);
http.POSTHN("/activityRulesInfo/selectTextContentByCode", encrypted);
```

### 3. 用户登录验证
```javascript
var loginData = yzmValue + "&" + telValue + "&" + "mini";
var encrypted = transmission.EncryptDataHn(loginData);
http.POSTHN("/newUser/getVerificationCodeForLogin", encrypted);
```

## 🔍 关键函数分析

### EncryptDataHn (请求加密)
```javascript
EncryptDataHn: function(data) {
    var aesKey = AESUtil.getAesKey();        // 生成32字符AES密钥
    var iv = AESUtil.getIv();                // 生成16字符IV
    var encryptedData = AESUtil.encrypt(data, aesKey, iv);  // AES加密数据
    var encryptedKey = RSA_PUBLIC_KEY.encrypt(aesKey);      // RSA加密AES密钥
    
    return {
        avd: iv,                             // IV明文传输
        ksynum: hex2b64(encryptedKey),       // RSA加密的密钥
        cmcxncxn: encryptedData              // AES加密的数据
    };
}
```

### DecryptKeyHn (响应解密)
```javascript
DecryptKeyHn: function(responseData) {
    var encryptedKeyHex = b64tohex(responseData.key);       // Base64转Hex
    var aesKey = RSA_PRIVATE_KEY.decrypt(encryptedKeyHex);  // RSA解密AES密钥
    var decryptedData = AESUtil.decrypt(responseData.content, aesKey, responseData.iv);  // AES解密数据
    return JSON.parse(decryptedData);                       // 解析JSON
}
```

## 🛡️ 安全特性

1. **双重加密**: AES加密数据 + RSA加密密钥
2. **随机性**: 每次请求生成新的AES密钥和IV
3. **密钥保护**: AES密钥通过RSA加密传输
4. **标准算法**: 使用标准的AES-CBC和RSA-OAEP
5. **版本隔离**: 普通版本和湖南版本使用不同的RSA密钥对

## 📊 数据流向图

```
客户端                           服务器
  |                               |
  | 1. 生成AES密钥和IV              |
  | 2. AES加密原始数据              |
  | 3. RSA加密AES密钥               |
  | 4. 发送{avd,ksynum,cmcxncxn}   |
  |----------------------------->|
  |                               | 5. RSA解密AES密钥
  |                               | 6. AES解密数据
  |                               | 7. 处理业务逻辑
  |                               | 8. AES加密响应数据
  |                               | 9. RSA加密新AES密钥
  | 10. 接收{key,content,iv}       |
  |<-----------------------------|
  | 11. RSA解密AES密钥              |
  | 12. AES解密响应数据             |
  | 13. 处理响应结果                |
```

## 🎯 总结

这个小程序使用了成熟的混合加密方案，既保证了数据传输的安全性，又通过随机密钥避免了重放攻击。加密逻辑清晰，实现完整，是一个典型的企业级加密解决方案。
