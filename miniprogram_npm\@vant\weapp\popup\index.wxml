<van-overlay bind:click="onClickOverlay" customStyle="{{overlayStyle}}" duration="{{duration}}" lockScroll="{{lockScroll}}" show="{{show}}" zIndex="{{zIndex}}" wx:if="{{overlay}}"></van-overlay>
<view bind:transitionend="onTransitionEnd" class="custom-class {{classes}} {{utils.bem( 'popup',[ position,{round:round,safe:safeAreaInsetBottom,safeTop:safeAreaInsetTop} ] )}}" style="{{computed.popupStyle( {zIndex:zIndex,currentDuration:currentDuration,display:display,customStyle:customStyle} )}}" wx:if="{{inited}}">
    <slot></slot>
    <van-icon bind:tap="onClickCloseIcon" class="close-icon-class van-popup__close-icon van-popup__close-icon--{{closeIconPosition}}" name="{{closeIcon}}" wx:if="{{closeable}}"></van-icon>
</view>
<wxs module="utils" src="../wxs/utils.wxs" />
<wxs module="computed" src="index.wxs" />