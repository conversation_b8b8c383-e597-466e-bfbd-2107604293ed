<van-button appParameter="{{appParameter}}" bind:click="onClick" bindcontact="onContact" binderror="onError" bindgetphonenumber="onGetPhoneNumber" bindgetuserinfo="onGetUserInfo" bindlaunchapp="onLaunchApp" bindopensetting="onOpenSetting" businessId="{{businessId}}" class="{{utils.bem( 'goods-action-button',[ type,{first:isFirst,last:isLast,plain:plain} ] )}}" color="{{color}}" customClass="van-goods-action-button__inner" disabled="{{disabled}}" id="{{id}}" lang="{{lang}}" loading="{{loading}}" openType="{{openType}}" plain="{{plain}}" sendMessageImg="{{sendMessageImg}}" sendMessagePath="{{sendMessagePath}}" sendMessageTitle="{{sendMessageTitle}}" sessionFrom="{{sessionFrom}}" showMessageCard="{{showMessageCard}}" type="{{type}}">{{text}}<slot></slot>
</van-button>
<wxs module="utils" src="../wxs/utils.wxs" />