/**
 * 12580小程序完整加密解密工具
 *
 * 功能说明：
 * ✅ 请求数据加密 - 可以生成与小程序一致的加密请求
 * ✅ 响应数据解密 - 可以解密服务器返回的所有数据
 * ❌ 请求数据解密 - 无法解密（需要服务器私钥）
 *
 * 运行方法：
 * npm install jsrsasign crypto-js
 * node complete_crypto_tool.js
 */

try {
	var KJUR = require("jsrsasign");
	var CryptoJS = require("crypto-js");
	console.log("✅ 依赖库加载成功");
} catch (e) {
	console.error("❌ 缺少依赖库，请运行：npm install jsrsasign crypto-js");
	process.exit(1);
}

var CompleteCryptoTool = {
	// ==================== RSA密钥配置 ====================

	// 普通版本密钥对
	NORMAL_KEYS: {
		PUBLIC_KEY: `-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA3wzK0OjysbU742VeREaGU9QSf/+T6OVTXKMY1qOOqUdP3yK0HaXbqAXIuIIOoy2vCsQU7KKs5RGbPVskjq8mkPJWxvuDayuATJl8OUdWnwQthcDwuJD1n//Bz9oVSJCIZ2So8OdqgcqELY715nLU5fpEnyk9QgIzjCjaZkOLd5gdzD2rUItCEt4WPGTAWNLMQmvI5Kvma0Ndrh1dGJlFxN5osDA60cSYThaoxL9pSpUv86XIB3QZTEn7wPXUe/roFouixs470k33kfK3UgyqDbVtL1EgYUpzL/xiF/HA5w7mXuB26Tuc1JaQ21BHqlsjbmaoaKtDylxWrZtUmmolGQIDAQAB
-----END PUBLIC KEY-----`,
		PRIVATE_KEY: `**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`,
	},

	// 湖南版本密钥对
	HN_KEYS: {
		PUBLIC_KEY: `-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAu1l6RjXiJBajEvWqcymhwlBX55m3HFUaGC7F2G51ytNJwsFON9jJqeAwB83+uUVJYM15MrBBMWAul0lGSmoRMsVnT1KR7p7UxnbGgdZT1NJNZgTu1J16MV98EUhImSk8jxrmoRdUGgDE1vvvlcOack8+xDDrF9x6tIl1Kyr7uFz1VXD17LTwJ5ajRtkp5Tszl+xs0fqKhxbXVknqCMGQcW9IGwNrr4/coaP+SmEUkIUQ5jKr69PI2ATjXghC7SUIfVOpplM0AV0PV7qvPYysyAXMjjieQ98jIOOYrEo7ay6qhtgiCiVr7k+bBi3gv9+2vlpMhPjdkAaIPwEnE40FOwIDAQAB
-----END PUBLIC KEY-----`,
		PRIVATE_KEY: `**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`,
	},

	// ==================== 工具函数 ====================

	// 生成UUID
	uuid: function () {
		var chars = "0123456789abcdef";
		var uuid = [];
		for (var i = 0; i < 36; i++) {
			uuid[i] = chars.substr(Math.floor(Math.random() * 16), 1);
		}
		uuid[14] = "4";
		uuid[19] = chars.substr((uuid[19] & 0x3) | 0x8, 1);
		uuid[8] = uuid[13] = uuid[18] = uuid[23] = "-";
		return uuid.join("");
	},

	// 生成AES密钥
	getAesKey: function () {
		var uuid = this.uuid();
		var utf8 = CryptoJS.enc.Utf8.parse(uuid);
		return CryptoJS.enc.Base64.stringify(utf8).substring(2, 34);
	},

	// 生成IV
	getIv: function () {
		var uuid = this.uuid();
		var utf8 = CryptoJS.enc.Utf8.parse(uuid);
		return CryptoJS.enc.Base64.stringify(utf8).substring(2, 18);
	},

	// Base64转Hex
	b64tohex: function (b64) {
		try {
			var binary = Buffer.from(b64, "base64").toString("binary");
			var hex = "";
			for (var i = 0; i < binary.length; i++) {
				var h = binary.charCodeAt(i).toString(16);
				hex += h.length === 1 ? "0" + h : h;
			}
			return hex;
		} catch (e) {
			console.error("❌ Base64转Hex失败:", e.message);
			return null;
		}
	},

	// Hex转Base64
	hex2b64: function (hex) {
		try {
			var binary = "";
			for (var i = 0; i < hex.length; i += 2) {
				binary += String.fromCharCode(parseInt(hex.substr(i, 2), 16));
			}
			return Buffer.from(binary, "binary").toString("base64");
		} catch (e) {
			console.error("❌ Hex转Base64失败:", e.message);
			return null;
		}
	},

	// ==================== AES加密解密 ====================

	// AES加密
	encrypt: function (plaintext, key, iv) {
		try {
			var message = CryptoJS.enc.Utf8.parse(plaintext);
			var keyParsed = CryptoJS.enc.Utf8.parse(key);
			var ivParsed = CryptoJS.enc.Utf8.parse(iv);

			var encrypted = CryptoJS.AES.encrypt(message, keyParsed, {
				iv: ivParsed,
				mode: CryptoJS.mode.CBC,
				padding: CryptoJS.pad.Pkcs7,
			});

			return CryptoJS.enc.Base64.stringify(encrypted.ciphertext);
		} catch (e) {
			console.error("❌ AES加密失败:", e.message);
			return null;
		}
	},

	// AES解密
	decrypt: function (ciphertext, key, iv) {
		try {
			var keyParsed = CryptoJS.enc.Utf8.parse(key);
			var ivParsed = CryptoJS.enc.Utf8.parse(iv);

			var decrypted = CryptoJS.AES.decrypt(ciphertext, keyParsed, {
				iv: ivParsed,
				mode: CryptoJS.mode.CBC,
				padding: CryptoJS.pad.Pkcs7,
			});

			return CryptoJS.enc.Utf8.stringify(decrypted);
		} catch (e) {
			console.error("❌ AES解密失败:", e.message);
			return null;
		}
	},

	// ==================== RSA加密解密 ====================

	// RSA加密（用于请求）
	rsaEncrypt: function (plaintext, publicKeyPem) {
		try {
			var publicKey = KJUR.KEYUTIL.getKey(publicKeyPem);
			var encrypted = publicKey.encrypt(plaintext);
			return encrypted;
		} catch (e) {
			console.error("❌ RSA加密失败:", e.message);
			return null;
		}
	},

	// RSA解密（用于响应）
	rsaDecrypt: function (encryptedHex, privateKeyPem) {
		try {
			var privateKey = KJUR.KEYUTIL.getKey(privateKeyPem);

			// 尝试使用doPrivate方法
			if (privateKey.doPrivate) {
				var bigInt = new KJUR.BigInteger(encryptedHex, 16);
				var decryptedBigInt = privateKey.doPrivate(bigInt);
				if (decryptedBigInt) {
					var decryptedHex = decryptedBigInt.toString(16);
					return this.removePKCS1Padding(decryptedHex);
				}
			}

			return null;
		} catch (e) {
			console.error("❌ RSA解密失败:", e.message);
			return null;
		}
	},

	// 移除PKCS1填充
	removePKCS1Padding: function (hexStr) {
		try {
			if (hexStr.length % 2 !== 0) {
				hexStr = "0" + hexStr;
			}

			var bytes = [];
			for (var i = 0; i < hexStr.length; i += 2) {
				bytes.push(parseInt(hexStr.substr(i, 2), 16));
			}

			// 查找第二个0x00（数据开始位置）
			var startIndex = -1;
			for (var j = 2; j < bytes.length; j++) {
				if (bytes[j] === 0x00) {
					startIndex = j + 1;
					break;
				}
			}

			if (startIndex === -1 || startIndex >= bytes.length) {
				throw new Error("无法找到有效的数据起始位置");
			}

			var actualData = bytes.slice(startIndex);
			var result = "";
			for (var k = 0; k < actualData.length; k++) {
				result += String.fromCharCode(actualData[k]);
			}

			return result;
		} catch (e) {
			console.error("❌ 移除PKCS1填充失败:", e.message);
			return null;
		}
	},

	// ==================== 主要功能函数 ====================

	// ✅ 湖南版本请求加密（生成加密请求数据）
	EncryptDataHn: function (data) {
		console.log("🔐 湖南版本请求加密");
		console.log("📝 原始数据:", data);

		try {
			var dataString =
				typeof data === "object" ? JSON.stringify(data) : String(data);

			// 1. 生成AES密钥和IV
			var aesKey = this.getAesKey();
			var iv = this.getIv();
			console.log("🔑 AES密钥:", aesKey);
			console.log("🎲 IV:", iv);

			// 2. AES加密数据
			var encryptedData = this.encrypt(dataString, aesKey, iv);
			if (!encryptedData) throw new Error("AES加密失败");

			// 3. RSA加密AES密钥
			var encryptedKey = this.rsaEncrypt(aesKey, this.HN_KEYS.PUBLIC_KEY);
			if (!encryptedKey) throw new Error("RSA加密失败");

			// 4. 构造结果
			var result = {
				avd: iv,
				ksynum: this.hex2b64(encryptedKey),
				cmcxncxn: encryptedData,
			};

			console.log("✅ 湖南版本请求加密完成");
			return result;
		} catch (error) {
			console.error("❌ 湖南版本请求加密失败:", error.message);
			return null;
		}
	},

	// ✅ 普通版本请求加密（生成加密请求数据）
	EncryptData: function (data) {
		console.log("🔐 普通版本请求加密");
		console.log("📝 原始数据:", data);

		try {
			var dataString =
				typeof data === "object" ? JSON.stringify(data) : String(data);

			// 1. 生成AES密钥和IV
			var aesKey = this.getAesKey();
			var iv = this.getIv();
			console.log("🔑 AES密钥:", aesKey);
			console.log("🎲 IV:", iv);

			// 2. AES加密数据
			var encryptedData = this.encrypt(dataString, aesKey, iv);
			if (!encryptedData) throw new Error("AES加密失败");

			// 3. RSA加密AES密钥
			var encryptedKey = this.rsaEncrypt(aesKey, this.NORMAL_KEYS.PUBLIC_KEY);
			if (!encryptedKey) throw new Error("RSA加密失败");

			// 4. 构造结果
			var result = {
				avd: iv,
				ksynum: this.hex2b64(encryptedKey),
				cmcxncxn: encryptedData,
			};

			console.log("✅ 普通版本请求加密完成");
			return result;
		} catch (error) {
			console.error("❌ 普通版本请求加密失败:", error.message);
			return null;
		}
	},

	// ✅ 湖南版本响应解密（解密服务器响应）
	DecryptKeyHn: function (responseData) {
		console.log("🔓 湖南版本响应解密");

		try {
			// 1. Base64转Hex
			var hexKey = this.b64tohex(responseData.key);
			if (!hexKey) throw new Error("密钥格式转换失败");

			// 2. RSA解密AES密钥
			var aesKey = this.rsaDecrypt(hexKey, this.HN_KEYS.PRIVATE_KEY);
			if (!aesKey) throw new Error("RSA解密失败");

			console.log("🔑 解密得到的AES密钥:", aesKey);

			// 3. AES解密内容
			var decryptedContent = this.decrypt(
				responseData.content,
				aesKey,
				responseData.iv
			);
			if (!decryptedContent) throw new Error("AES解密失败");

			// 4. 解析JSON
			try {
				var result = JSON.parse(decryptedContent);
				console.log("✅ 湖南版本响应解密完成");
				return result;
			} catch (jsonError) {
				console.log("⚠️  JSON解析失败，返回原始字符串");
				return decryptedContent;
			}
		} catch (error) {
			console.error("❌ 湖南版本响应解密失败:", error.message);
			return null;
		}
	},

	// ✅ 普通版本响应解密（解密服务器响应）
	DecryptKey: function (responseData) {
		console.log("🔓 普通版本响应解密");

		try {
			// 1. Base64转Hex
			var hexKey = this.b64tohex(responseData.key);
			if (!hexKey) throw new Error("密钥格式转换失败");

			// 2. RSA解密AES密钥
			var aesKey = this.rsaDecrypt(hexKey, this.NORMAL_KEYS.PRIVATE_KEY);
			if (!aesKey) throw new Error("RSA解密失败");

			console.log("🔑 解密得到的AES密钥:", aesKey);

			// 3. AES解密内容
			var decryptedContent = this.decrypt(
				responseData.content,
				aesKey,
				responseData.iv
			);
			if (!decryptedContent) throw new Error("AES解密失败");

			// 4. 解析JSON
			try {
				var result = JSON.parse(decryptedContent);
				console.log("✅ 普通版本响应解密完成");
				return result;
			} catch (jsonError) {
				console.log("⚠️  JSON解析失败，返回原始字符串");
				return decryptedContent;
			}
		} catch (error) {
			console.error("❌ 普通版本响应解密失败:", error.message);
			return null;
		}
	},

	// 🔍 智能响应解密（自动尝试两个版本）
	smartDecryptResponse: function (responseData) {
		console.log("🔍 智能响应解密（自动检测版本）");

		// 先尝试湖南版本
		var hnResult = this.DecryptKeyHn(responseData);
		if (hnResult) {
			return { version: "湖南版本", data: hnResult };
		}

		// 再尝试普通版本
		var normalResult = this.DecryptKey(responseData);
		if (normalResult) {
			return { version: "普通版本", data: normalResult };
		}

		console.error("❌ 所有版本解密都失败");
		return null;
	},
};

// 使用说明
console.log("🎉 12580小程序完整加密解密工具已加载！");
console.log("\n📖 功能说明:");
console.log("✅ 请求数据加密:");
console.log("  - CompleteCryptoTool.EncryptDataHn(data) - 湖南版本请求加密");
console.log("  - CompleteCryptoTool.EncryptData(data) - 普通版本请求加密");
console.log("\n✅ 响应数据解密:");
console.log(
	"  - CompleteCryptoTool.DecryptKeyHn(responseData) - 湖南版本响应解密"
);
console.log(
	"  - CompleteCryptoTool.DecryptKey(responseData) - 普通版本响应解密"
);
console.log(
	"  - CompleteCryptoTool.smartDecryptResponse(responseData) - 智能解密"
);
console.log("\n❌ 请求数据解密:");
console.log("  - 无法解密请求数据（需要服务器私钥）");

console.log("\n💡 使用示例:");
console.log("// 加密请求数据");
console.log("var requestData = {sCode: 'SJS_CODE_12345', areaCode: '430100'};");
console.log("var encrypted = CompleteCryptoTool.EncryptDataHn(requestData);");
console.log("");
console.log("// 解密响应数据");
console.log("var responseData = {key: '...', content: '...', iv: '...'};");
console.log("var decrypted = CompleteCryptoTool.DecryptKeyHn(responseData);");

// 导出模块
module.exports = CompleteCryptoTool;
