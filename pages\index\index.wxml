<import src="storeInfo-list-template/storeInfo-list-template.wxml"></import>
<view class="container">
    <view class="bannerHeight">
        <image mode="widthFix" src="{{currentCardBackgroundUrl}}" wx:if="{{topIsShow}}">
            <view class="notlogin">
                <view class="login-card">
                    <view class="card-image">
                        <van-image round bindtap="getUserInfoImg" height="60rpx" src="{{avatarUrl==''?'/static/imagehn/login-png-hn.png':avatarUrl}}" width="60rpx" wx:if="{{loginStatus==1}}"></van-image>
                        <van-image round bindtap="handleLogin" height="60rpx" src="{{avatarUrl==''?'/static/imagehn/login-png-hn.png':avatarUrl}}" width="60rpx" wx:if="{{loginStatus==0}}"></van-image>
                    </view>
                    <view class="login">
                        <view bindtap="handleLogin" class="ft13" wx:if="{{loginStatus==0}}">做个有身份的人,请登录享权益吧。</view>
                        <view class="select_box" wx:if="{{loginStatus==1&&selectDatas.length>1}}">
                            <view catchtap="selectTaps" class="select">
                                <text class="select_text">欢迎您,{{selectDatas[indexs].serviceName}}</text>
                                <image class="select_img {{shows&&'select_img_rotate'}}" src="/static/imagehn/selecthn.png"></image>
                            </view>
                            <view class="option_box" style="height:{{shows?selectDatas.length>5?300:selectDatas.length*80:0}}rpx;">
                                <text catchtap="optionTaps" class="option" data-index="{{index}}" style="{{indexs==selectDatas.length-1&&'border:0;'}}" wx:for="{{selectDatas}}" wx:key="index">{{item.serviceName}}</text>
                            </view>
                        </view>
                        <view class="select_box" wx:if="{{loginStatus==1&&selectDatas.length==1}}">
                            <view catchtap="selectTaps" class="select">
                                <text class="select_text">欢迎您{{selectDatas[0].serviceName!=null?'，'+selectDatas[0].serviceName:''}}</text>
                            </view>
                        </view>
                        <view class="select_box" wx:if="{{loginStatus==1&&selectDatas.length==0}}">
                            <view catchtap="selectTaps" class="select">
                                <text class="select_text">欢迎您{{selectDatas[0].serviceName!=null?'，'+selectDatas[0].serviceName:''}}</text>
                            </view>
                        </view>
                    </view>
                    <view class="notlogin-location">
                        <view bindtap="bindtapCity" class="location-weather">
                            <image class="location-weather_image" src="{{xuecheLocation}}"></image>
                            <text class="ft10">{{weatherMap.city}}</text>
                            <image class="select_img" src="/static/imagehn/selecthn.png" wx:if="{{loginStatus==1&&isSuperVip==true||loginStatus==1&&isSuperVip=='true'}}"></image>
                        </view>
                        <view class="location-weather">
                            <image class="location-weather_image" src="{{xuecheImg}}"></image>
                            <text class="ft10">{{xuecheText}}</text>
                        </view>
                    </view>
                </view>
                <view class="page-section page-section-spacing swiper">
                    <swiper autoplay="{{true}}" circular="{{true}}" class="swiper-banner" duration="{{500}}" indicatorActiveColor="#fff" indicatorColor="rgba(255, 255, 255, .3)" indicatorDots="{{true}}" interval="{{5000}}" wx:if="{{bannerUrls.length!==0&&bannerNotShow===false}}">
                        <swiper-item wx:if="{{item.adImgUrl!=''}}" wx:for="{{bannerUrls}}" wx:key="index">
                            <view class="swiper-item item">
                                <image bindtap="toUrl" class="swiper-banner" data-index="{{index}}" data-item="{{item}}" src="{{pathImg+item.adImgUrl}}"></image>
                            </view>
                        </swiper-item>
                    </swiper>
                </view>
            </view>
        </image>
    </view>
    <view class="seckill-prefecture" wx:if="{{itemObj.activitySCodeList.length!=0}}" wx:for="{{seckillprefectureList}}" wx:for-item="itemObj" wx:key="index">
        <view class="seckill-prefecture-logo">
            <image mode="widthFix" src="{{pathImg+itemObj.iconUrl}}"></image>
        </view>
        <view class="page-section page-section-spacing swiper">
            <swiper class="seckill-prefecture-swiper" duration="500" indicatorActiveColor="#F94545" indicatorColor="#EDEDEF" indicatorDots="true" interval="2000">
                <swiper-item class="seckill-prefecture-purchase" wx:for="{{itemObj.activitySCodeList}}" wx:for-index="outIndex" wx:for-item="outItem" wx:key="outIndex">
                    <view bindtap="handleSkipCoupon" class="seckill-prefecture-purchase-l" data-item="{{item}}" data-title="{{itemObj}}" wx:if="{{item}}" wx:for="{{outItem}}" wx:key="index">
                        <view class="seckill-prefecture-purchase-y">
                            <view class="seckill-prefecture-purchase-y-img">
                                <image binderror="errorImg" data-index="{{index}}" data-outindex="{{outIndex}}" src="{{pathImg+item.couponIconUrl}}"></image>
                            </view>
                            <image class="rob-hn" src="/static/imagehn/rob-hn.png" wx:if="{{item.robhn==0}}"></image>
                            <text class="seckill-prefecture-swiper-text">{{item.sCodeNameMini}}</text>
                            <text class="seckill-prefecture-swiper-textmoney" wx:if="{{item.homeType=='product'}}">¥{{item.sValue}}</text>
                            <view class="progress-box" wx:if="{{item.buyRate!=1&&item.homeType!='noBasic'}}">
                                <progress activeColor="#DD3231" backgroundColor="#FFA6A9" borderRadius="20" class="progress" duration="10" percent="{{item.buyRate*100}}" strokeWidth="20rpx"></progress>
                                <text>已抢{{item.buyRate*100}}%</text>
                            </view>
                            <view class="progress-box" wx:if="{{item.buyRate==1&&item.homeType!='noBasic'}}">
                                <progress activeColor="#DD3231" backgroundColor="#DD3231" borderRadius="20" class="progress" duration="10" percent="{{item.buyRate*100}}" strokeWidth="20rpx"></progress>
                                <text>已抢光</text>
                            </view>
                        </view>
                    </view>
                </swiper-item>
            </swiper>
        </view>
    </view>
    <view class="notice" wx:if="{{noticeList.length!==0}}">
        <view class="left">
            <text class="swiper-notice">公告</text>
            <van-icon color="#fa6016" name="volume"></van-icon>
        </view>
        <swiper autoplay="true" circular="true" class="swiper-container" interval="5000" vertical="true">
            <swiper-item bindtap="handleTapSingleNotice" class="item" data-id="{{item.noticeId}}" wx:for="{{noticeList}}" wx:key="index">
                <view class="swiper-item">{{item.title}}</view>
            </swiper-item>
        </swiper>
        <view bindtap="navigatorToNotice" class="right">查看更多>></view>
    </view>
    <view class="br" wx:if="{{noticeList.length!==0}}"></view>
    <view class="vip-basics" wx:if="{{gridUrls.length!=0&&feeAppCode!='HN9'&&feeAppCode!='HN10'&&feeAppCode!='GENERAL_WZCX_5'&&provinceCode!='43'&&provinceCode!='32'}}">
        <swiper class="vip-basics-swiper" duration="500" indicatorActiveColor="#F94545" indicatorColor="#EDEDEF" indicatorDots="true" interval="2000">
            <swiper-item class="rush-purchase" wx:for="{{gridUrls}}" wx:for-index="outIndex" wx:for-item="outItem" wx:key="outIndex">
                <view bindtap="handNav" class="rush-purchase-l" data-item="{{item}}" wx:if="{{item}}" wx:for="{{outItem}}" wx:key="index">
                    <view class="rush-purchase-y">
                        <view class="rush-purchase-y-img">
                            <image class="rush-purchase-y-img-image1" src="{{item.smallIcon==''?'':pathImg+item.smallIcon}}"></image>
                            <image binderror="errorImg" class="rush-purchase-y-img-image" data-index="{{index}}" data-outindex="{{outIndex}}" src="{{pathImg+item.iconUrl}}"></image>
                        </view>
                        <text class="rush-text">{{item.privilegeNameMini}}</text>
                    </view>
                </view>
            </swiper-item>
        </swiper>
        <text class="view-more">左右滑动 查看更多</text>
    </view>
    <view class="linenoticeList" wx:if="{{!noticeList.length&&bannerBox.imgUrl}}"></view>
    <view class="nx-tel" style="margin-top:30rpx;" wx:if="{{bannerBox.imgUrl}}">
        <button bindcontact="handleContact" class="kefubottom" openType="contact" sessionFrom="sessionFrom" wx:if="{{typeJump==4}}">
            <image bindtap="makePhoneCall" class="nx-tel-img" src="{{pathImg+bannerBox.imgUrl}}"></image>
        </button>
        <view wx:else>
            <image bindtap="makePhoneCall" class="nx-tel-img" src="{{pathImg+bannerBox.imgUrl}}"></image>
        </view>
    </view>
    <view class="carser" wx:if="{{provinceCode=='43'&&feeAppCode=='GENERAL_WZCX_5'}}">
        <view class="carsNew" style="margin-top:16rpx;">
            <view class="titleBox">
                <image src="/static/image/carstitle.png"></image>
            </view>
            <view>
                <view class="carsbody">
                    <view class="carsBoxRockone">
                        <view bindtap="handNav" data-item="{{item}}" wx:if="{{index<2}}" wx:for="{{basicPrivilegeSettingList}}" wx:key="index">
                            <view>
                                <image src="{{pathImg+item.iconUrl}}"></image>
                            </view>
                        </view>
                    </view>
                    <view class="carsBoxRocktwo">
                        <view bindtap="handNav" data-item="{{item}}" wx:if="{{index>1&&index<5}}" wx:for="{{basicPrivilegeSettingList}}" wx:key="index">
                            <view>
                                <image src="{{pathImg+item.iconUrl}}"></image>
                            </view>
                        </view>
                    </view>
                </view>
            </view>
        </view>
    </view>
    <view class="enjoy-rights">
        <block wx:if="{{isShowOil==1}}">
            <view class="enjoy-rights-logo" wx:if="{{indexCount!=0}}">
                <image class="enjoy-rights-image" src="/static/imagehn/enjoy-rights-logo.png"></image>
            </view>
            <view class="residue-text" wx:if="{{loginStatus=='1'&&indexCount!=0&&currentOilShow&&showRestOilDrop=='yes'}}">{{provinceCityAndMemberMap.memberGradeCodeName}}权益<view class="residue-text1">
                    <view class="residue-text2">当前剩余:{{' '+currentOil}}个<image mode="widthFix" src="/static/imagehn/oil-hn-white.png" style="margin-right:4rpx;margin-left:4rpx;"></image>
                    </view>
                </view>可凭油滴兑换权益</view>
            <view class="residue-text" wx:if="{{loginStatus=='0'&&indexCount!=0&&currentOilShow&&showRestOilDrop=='yes'}}">当前显示的是{{provinceCityAndMemberMap.provinceName}}{{provinceCityAndMemberMap.cityName}}{{provinceCityAndMemberMap.memberGradeCodeName}}权益</view>
            <view class="residue-text" wx:if="{{currentOilShow==false}}">当前显示的是{{provinceCityAndMemberMap.provinceName}}{{provinceCityAndMemberMap.cityName}}{{provinceCityAndMemberMap.memberGradeCodeName}}权益</view>
        </block>
        <view class="containerShop" wx:if="{{productPage&&productPage[0].promotion.productOverviews}}">
            <view wx:for="{{productPage}}" wx:key="index">
                <view class="titleBox">
                    <image src="{{imgUrl+item.promotion.nameImage}}"></image>
                    <view bindtap="gomustShop" data-id="{{item.promotion.appPromotionId}}" data-type="{{item.promotion.promotionType}}">查看更多<image src="/static/image/right.png"></image>
                    </view>
                </view>
                <view class="container-shop">
                    <shopList bind:storeshoplist="storeshoplist" tui="{{item.promotion.productOverviews}}"></shopList>
                </view>
            </view>
        </view>
        <view class="today-rush" wx:if="{{itemObj.activitySCodeList.length!=0}}" wx:for="{{modelBurstZoneList}}" wx:for-item="itemObj" wx:key="index">
            <image class="today-rush-image" mode="widthFix" src="{{pathImg+itemObj.iconUrl}}"></image>
            <view class="today-rush-li">
                <view bindtap="couponRule" class="today-rush-text" data-item="{{itemObj}}">点击查看领券规则</view>
                <view class="page-section page-section-spacing swiper">
                    <swiper class="today-rush-swiper" duration="500" indicatorActiveColor="#fff" indicatorColor="rgba(255, 255, 255, .3)" indicatorDots="true" interval="2000">
                        <swiper-item class="today-rush-purchase" wx:for="{{itemObj.activitySCodeList}}" wx:for-index="outIndex" wx:for-item="outItem" wx:key="outIndex">
                            <view bindtap="handleSkipCoupon" class="today-rush-purchase-l" data-item="{{item}}" data-title="{{itemObj}}" wx:if="{{item}}" wx:for="{{outItem}}" wx:key="index">
                                <view class="today-rush_purchase-y">
                                    <view class="today-rush-purchase-y-img">
                                        <image class="today-rush_smallIcon" src="{{item.smallIcon==''?'':pathImg+item.smallIcon}}"></image>
                                        <image binderror="errorImg" class="today-rush-image" data-index="{{index}}" data-outindex="{{outIndex}}" src="{{pathImg+item.couponIconUrl}}"></image>
                                    </view>
                                    <image class="critical-fire-hn" src="/static/imagehn/critical-fire-hn.png" wx:if="{{item.criticalfire==0}}"></image>
                                    <text class="today-rush-swiper-text">{{item.sCodeNameHtml}}</text>
                                    <view class="ticket_oil" wx:if="{{item.couponDetailsType!=126&&item.couponDetailsType!=127}}">{{item.oilDrop!=0?'消耗'+item.oilDrop+'个':item.receiveCouponName==undefined?'会员专享':item.receiveCouponName}}<image src="/static/imagehn/oil-hn.png" wx:if="{{item.oilDrop!=0}}"></image>
                                    </view>
                                    <view class="ticket_oil" wx:else>{{item.receiveCouponName}}</view>
                                </view>
                            </view>
                        </swiper-item>
                    </swiper>
                    <text class="view-more" wx:if="{{itemObj.specialZoneNameCount!=3}}">左右滑动 查看更多</text>
                </view>
            </view>
        </view>
        <view class="jf-fixed">
            <view class="jf-lucky" wx:if="{{LuckyDraw==true}}">
                <image bindtap="jfgoinDraw" class="jf-big" src="https://czfw.12580.com/file/M00/00/DD/wGQDC2VEn0uAKpsxAAC6GZVtKio487.png"></image>
                <image bindtap="jfhidden" class="jf-sm" src="/static/image/jf-error.png"></image>
            </view>
        </view>
        <view class="poice" style="background:#F7F7F7;" wx:if="{{modelSettingBasicList.length&&(feeAppCode=='HN10'||feeAppCode=='HN9'||feeAppCode=='JS20'||feeAppCode=='CZFW15'||feeAppCode=='CZFW25')}}">
            <view class="titleBox" style="margin:0 30rpx ">
                <image src="/static/image/baotitle.png"></image>
            </view>
        </view>
        <view class="newcouponList" wx:if="{{provinceCode=='43'&&modelSettingBasicList.length&&(feeAppCode=='HN10'||feeAppCode=='HN9'||feeAppCode=='JS20'||feeAppCode=='CZFW15'||feeAppCode=='CZFW25')||provinceCode=='32'&&modelSettingBasicList.length&&(feeAppCode=='JS10'||feeAppCode=='JS20')}}">
            <view wx:if="{{itemObj.activitySCodeList.length!=0&&itemObj.specialZoneTypeCode!='notice_extend'&&itemObj.specialZoneTypeCode!='zhongshiyouhuodong'}}" wx:for="{{modelSettingBasicList}}" wx:for-item="itemObj" wx:key="index">
                <image bindtap="goSpecialArea" class="newcouponListimg" data-codeid="{{itemObj.specialZoneTypeCode}}" data-miniurl="{{itemObj.miniUrl}}" data-name="{{itemObj.specialZoneName}}" data-style="{{itemObj.isHotStyle}}" data-styleType="{{itemObj.styleType}}" src="{{pathImg+itemObj.iconUrl}}" wx:if="{{index==0}}"></image>
            </view>
            <view class="newcouponList-heng">
                <view wx:if="{{itemObj.activitySCodeList.length!=0&&itemObj.specialZoneTypeCode!='notice_extend'&&itemObj.specialZoneTypeCode!='zhongshiyouhuodong'}}" wx:for="{{modelSettingBasicList}}" wx:for-item="itemObj" wx:key="index">
                    <image bindtap="goSpecialArea" class="newcouponListImg" data-codeid="{{itemObj.specialZoneTypeCode}}" data-miniUrl="{{itemObj.miniUrl}}" data-name="{{itemObj.specialZoneName}}" data-style="{{itemObj.isHotStyle}}" data-styleType="{{itemObj.styleType}}" src="{{pathImg+itemObj.iconUrl}}" wx:if="{{index>0&&index<3}}"></image>
                </view>
            </view>
        </view>
        <block wx:if="{{provinceCode=='43'&&modelSettingBasicList.length&&(feeAppCode=='HN10'||feeAppCode=='HN9'||feeAppCode=='JS20'||feeAppCode=='CZFW15'||feeAppCode=='CZFW25')||provinceCode=='32'&&modelSettingBasicList.length&&(feeAppCode=='JS10'||feeAppCode=='JS20')}}" wx:for="{{modelSettingBasicList}}" wx:for-item="itemObj" wx:key="index">
            <view class="coupon-group" wx:if="{{itemObj.activitySCodeList.length!=0&&itemObj.specialZoneTypeCode!='notice_extend'&&index>2&&itemObj.specialZoneTypeCode!='zhongshiyouhuodong'}}">
                <view class="z_enjoy">
                    <image class="z_enjoy_image" src="{{pathImg+itemObj.iconUrl}}"></image>
                </view>
                <view class="z_ticket_l_mian">
                    <view bindtap="couponRule" class="z_enjoy_title" data-item="{{itemObj}}">
                        <view class="z_enjoy_view">
                            <view class="z_enjoy_view_text"></view>
                            <view class="z_enjoy_view_image"></view>
                        </view>
                    </view>
                    <view class="z_ticket_l">
                        <scroll-view class="scroll-header" scrollX="true" upperThreshold="10">
                            <view class="z_ticket">
                                <view class="z_enjoy_li" wx:for="{{itemObj.activitySCodeList}}" wx:for-index="outIndexList" wx:for-item="outItemList" wx:key="outIndexList">
                                    <block wx:for="{{outItemList}}" wx:for-index="itemIndex" wx:key="itemIndex">
                                        <view bindtap="handleSkipCoupon" class="scroll-view-item bc_green z_enjoy_ticket_li" data-item="{{item}}" data-title="{{itemObj}}" id="green" wx:if="{{item}}">
                                            <view bindtap="handleSkipCoupon" class="ticket_z" data-item="{{item}}" data-title="{{itemObj}}">
                                                <image class="ticket_z_smallIcon" src="{{item.smallIcon==''?'':pathImg+item.smallIcon}}"></image>
                                                <image binderror="errorImg1" class="ticket_z_img" data-index="{{index}}" data-outindex="{{itemIndex}}" src="{{pathImg+item.couponIconUrl}}"></image>
                                                <view class="ticket_name">{{item.sCodeNameHtml}}</view>
                                                <view wx:if="{{isShowOil==1}}">
                                                    <view class="ticket_oil" wx:if="{{item.couponDetailsType!=126&&item.couponDetailsType!=127}}">{{item.oilDrop!=0?'消耗'+item.oilDrop+'个':item.receiveCouponName==undefined?'会员专享':item.receiveCouponName}}<image src="/static/imagehn/oil-hn.png" wx:if="{{item.oilDrop!=0}}"></image>
                                                    </view>
                                                    <view class="ticket_oil" wx:else>{{item.receiveCouponName}}</view>
                                                </view>
                                            </view>
                                        </view>
                                        <view class="scroll-view-item bc_green z_enjoy_ticket_li" id="green" wx:if="{{item==null&&itemIndex<3}}"></view>
                                    </block>
                                </view>
                            </view>
                        </scroll-view>
                    </view>
                </view>
                <view class="left-right" wx:if="{{itemObj.activitySCodeList.length!=3}}">
                    <text class="view-more">左右滑动 查看更多</text>
                </view>
            </view>
        </block>
        <block wx:if="{{provinceCode=='32'&&feeAppCode!='JS20'&&feeAppCode!='JS10'||provinceCode=='43'&&feeAppCode!='HN9'&&feeAppCode!='HN10'&&feeAppCode!='JS20'&&feeAppCode!='CZFW15'&&feeAppCode!='CZFW25'||provinceCode=='64'||provinceCode=='63'||provinceCode=='34'}}" wx:for="{{modelSettingBasicList}}" wx:for-item="itemObj" wx:key="index">
            <view class="coupon-group" wx:if="{{itemObj.activitySCodeList.length!=0&&itemObj.specialZoneTypeCode!='notice_extend'&&itemObj.specialZoneTypeCode!='zhongshiyouhuodong'}}">
                <view class="z_enjoy">
                    <image class="z_enjoy_image" src="{{pathImg+itemObj.iconUrl}}"></image>
                </view>
                <view class="z_ticket_l_mian">
                    <view bindtap="couponRule" class="z_enjoy_title" data-item="{{itemObj}}">
                        <view class="z_enjoy_view">
                            <view class="z_enjoy_view_text"></view>
                            <view class="z_enjoy_view_image"></view>
                        </view>
                    </view>
                    <view class="z_ticket_l">
                        <scroll-view class="scroll-header" scrollX="true" upperThreshold="10">
                            <view class="z_ticket">
                                <view class="z_enjoy_li" wx:for="{{itemObj.activitySCodeList}}" wx:for-index="outIndexList" wx:for-item="outItemList" wx:key="outIndexList">
                                    <block wx:for="{{outItemList}}" wx:for-index="itemIndex" wx:key="itemIndex">
                                        <view bindtap="handleSkipCoupon" class="scroll-view-item bc_green z_enjoy_ticket_li" data-item="{{item}}" data-title="{{itemObj}}" id="green" wx:if="{{item}}">
                                            <view bindtap="handleSkipCoupon" class="ticket_z" data-item="{{item}}" data-title="{{itemObj}}">
                                                <image class="ticket_z_smallIcon" src="{{item.smallIcon==''?'':pathImg+item.smallIcon}}"></image>
                                                <image binderror="errorImg1" class="ticket_z_img" data-index="{{index}}" data-outindex="{{itemIndex}}" src="{{pathImg+item.couponIconUrl}}"></image>
                                                <view class="ticket_name">{{item.sCodeNameHtml}}</view>
                                                <view wx:if="{{isShowOil==1}}">
                                                    <view class="ticket_oil" wx:if="{{item.couponDetailsType!=126&&item.couponDetailsType!=127}}">{{item.oilDrop!=0?'消耗'+item.oilDrop+'个':item.receiveCouponName==undefined?'会员专享':item.receiveCouponName}}<image src="/static/imagehn/oil-hn.png" wx:if="{{item.oilDrop!=0}}"></image>
                                                    </view>
                                                    <view class="ticket_oil" wx:else>{{item.receiveCouponName}}</view>
                                                </view>
                                            </view>
                                        </view>
                                        <view class="scroll-view-item bc_green z_enjoy_ticket_li" id="green" wx:if="{{item==null&&itemIndex<3}}"></view>
                                    </block>
                                </view>
                            </view>
                        </scroll-view>
                    </view>
                </view>
                <view class="left-right" wx:if="{{itemObj.specialZoneNameCount!=3}}">
                    <text class="view-more">左右滑动 查看更多</text>
                </view>
            </view>
        </block>
    </view>
    <view class="carser" wx:if="{{provinceCode=='43'&&(feeAppCode=='JS20'||feeAppCode=='CZFW15'||feeAppCode=='CZFW25')&&basicPrivilegeSettingList.length}}">
        <view wx:if="{{basicPrivilegeSettingList.length}}">
            <view class="titleBox">
                <image src="/static/image/qcbd.png"></image>
            </view>
            <view class="qcbdbody">
                <image bindtap="onJumpGuaZi" src="/static/image/ersc.png"></image>
                <image bindtap="onJumpWeiBao" src="/static/image/bx.png"></image>
                <image bindtap="onJump4S" src="/static/image/md.png"></image>
            </view>
        </view>
    </view>
    <view class="carser" wx:if="{{provinceCode=='32'&&feeAppCode!='HCX20'||provinceCode=='43'&&feeAppCode!='HCX20'&&feeAppCode!='GENERAL_WZCX_5'&&basicPrivilegeSettingList.length}}">
        <view class="carsNew" wx:if="{{basicPrivilegeSettingList.length}}">
            <view class="titleBox">
                <image src="/static/image/carstitle.png"></image>
            </view>
            <view>
                <view class="carsbody">
                    <view class="carsBoxRockone">
                        <view bindtap="handNav" data-item="{{item}}" wx:if="{{index<2}}" wx:for="{{basicPrivilegeSettingList}}" wx:key="index">
                            <view>
                                <image src="{{pathImg+item.iconUrl}}"></image>
                            </view>
                        </view>
                    </view>
                    <view class="carsBoxRocktwo">
                        <view bindtap="handNav" data-item="{{item}}" wx:if="{{index>1&&index<5}}" wx:for="{{basicPrivilegeSettingList}}" wx:key="index">
                            <view>
                                <image src="{{pathImg+item.iconUrl}}"></image>
                            </view>
                        </view>
                    </view>
                </view>
            </view>
        </view>
    </view>
</view>
<view bindtap="picker" style="width:100vw;height:100vh;" wx:if="{{openPicker}}">
    <view style="position:absolute;width:100%;height:100%;background:rgba(0, 0, 0, 0.7);left:0px;top:0px;z-index:99">
        <view style="width:100vw;background:white;height:60px;bottom:45%;position:absolute;display:flex;justify-content:center;align-items:center;">
            <view class="picker_left">
                <view bindtap="pickerCancel" style="width:131rpx;height:65rpx;background:#EFEFEF;border-radius:10rpx;color:#525252;display:flex;justify-content:center;align-items:center;">取消</view>
            </view>
            <view class="picker_left">
                <view bindtap="pickerConfirm" style="width:131rpx;height:65rpx;background:#DD3231;border-radius:10rpx;color:white;display:flex;justify-content:center;align-items:center;">确定</view>
            </view>
        </view>
        <picker-view bindchange="bindMultiPickerChange" indicatorStyle="height: 50px;" style="width:100%;height:45%;position:absolute;bottom:0px;text-align:center;background:white" value="{{multiIndex}}">
            <picker-view-column>
                <view style="line-height:50px;text-align:center;" wx:for="{{multiArrayP}}">{{item.provinceName}}</view>
            </picker-view-column>
            <picker-view-column>
                <view style="line-height:50px;text-align:center;" wx:for="{{multiArrayC}}">{{item.areaName}}</view>
            </picker-view-column>
        </picker-view>
    </view>
</view>
<van-loading class="loading" color="#DD3231" type="spinner" wx:if="{{isLoading}}"></van-loading>
<view class="container3">
    <van-overlay show="{{isOpenDialog1}}">
        <view class="container2" wx:if="{{isOpenDialog1}}">
            <view class="van-coupon-dialog">
                <view class="dialog-title">
                    <image bindtap="closeOverlay" class="close-dialog" src="/static/imagehn/close-icon-white1.png"></image>
                </view>
                <view class="dialog-content">
                    <image bindtap="goToRecharge1" class="dialog-content-img" src="{{imgUrl1==undefined?'':pathImg+imgUrl1}}"></image>
                </view>
            </view>
        </view>
    </van-overlay>
</view>
<v-kefu></v-kefu>
<view class="copyright1 tc ft12" style="background-color:#f5f5f5;padding-top:10px;">
    <view style="font-size:22rpx;font-family:PingFang SC;color:rgba(157,156,156,1);">©中国移动通信集团江苏有限公司版权所有</view>
    <view style="font-size:22rpx;font-family:PingFang SC;color:rgba(157,156,156,1);">苏ICP备11070397号-21</view>
    <view style="font-size:22rpx;font-family:PingFang SC;color:rgba(157,156,156,1);">（中国移动通信集团委托江苏有限公司支撑12580惠出行平台）</view>
</view>
<wxs module="substr" src="../../wxs/handleStr.wxs" />