var array = require('p_./miniprogram_npm/@vant/weapp/wxs/array.wxs')();
var object = require('p_./miniprogram_npm/@vant/weapp/wxs/object.wxs')();
var PREFIX = 'van-';

function join(name, mods) {
    name = PREFIX + name;
    mods = mods.map((function(mod) {
        return (name + '--' + mod)
    }));
    mods.unshift(name);
    return (mods.join(' '))
};

function traversing(mods, conf) {
    if (!conf) {
        return
    };
    if (typeof conf === 'string' || typeof conf === 'number') {
        mods.push(conf)
    } else if (array.isArray(conf)) {
        conf.forEach((function(item) {
            traversing(mods, item)
        }))
    } else if (typeof conf === 'object') {
        object.keys(conf).forEach((function(key) {
            conf[((nt_0 = (key), null == nt_0 ? undefined : 'number' === typeof nt_0 ? nt_0 : "" + nt_0))] && mods.push(key)
        }))
    }
};

function bem(name, conf) {
    var mods = [];
    traversing(mods, conf);
    return (join(name, mods))
};
module.exports = bem;