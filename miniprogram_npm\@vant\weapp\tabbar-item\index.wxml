<view bindtap="onClick" class="{{utils.bem( 'tabbar-item',{active:active} )}} custom-class" style="color:{{active?activeColor:inactiveColor}}">
    <view class="van-tabbar-item__icon">
        <van-icon classPrefix="{{iconPrefix}}" customClass="van-tabbar-item__icon__inner" name="{{icon}}" wx:if="{{icon}}"></van-icon>
        <block wx:else>
            <slot name="icon-active" wx:if="{{active}}"></slot>
            <slot name="icon" wx:else></slot>
        </block>
        <van-info customClass="van-tabbar-item__info" dot="{{dot}}" info="{{info}}"></van-info>
    </view>
    <view class="van-tabbar-item__text">
        <slot></slot>
    </view>
</view>
<wxs module="utils" src="../wxs/utils.wxs" />