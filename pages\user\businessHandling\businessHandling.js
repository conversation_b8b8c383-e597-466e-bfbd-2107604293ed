var n = require("../../../utils/http.js");

require("../../../utils/transmission.js").rsaAesJs;

Page({
    data: {
        url: ""
    },
    onLoad: function(o) {
        var e = wx.getStorageSync("telephone_hn"), t = n.baseUrlScenic + "personal/rechargeRecord?phone=".concat(e);
        this.setData({
            url: t
        });
    },
    onReady: function() {},
    onShow: function() {},
    onHide: function() {},
    onUnload: function() {},
    onPullDownRefresh: function() {},
    onReachBottom: function() {},
    onShareAppMessage: function() {}
});