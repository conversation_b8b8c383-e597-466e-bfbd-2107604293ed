Object.defineProperty(exports, "__esModule", {
    value: !0
}), exports.addUnit = function(e) {
    if (!(0, t.isDef)(e)) return;
    return e = String(e), (0, t.isNumber)(e) ? "".concat(e, "px") : e;
}, exports.getAllRect = function(e, t) {
    return new Promise(function(n) {
        wx.createSelectorQuery().in(e).selectAll(t).boundingClientRect().exec(function() {
            var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : [];
            return n(e[0]);
        });
    });
}, exports.getCurrentPage = function() {
    var e = getCurrentPages();
    return e[e.length - 1];
}, exports.getRect = function(e, t) {
    return new Promise(function(n) {
        wx.createSelectorQuery().in(e).select(t).boundingClientRect().exec(function() {
            var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : [];
            return n(e[0]);
        });
    });
}, exports.getSystemInfoSync = r, exports.groupSetData = function(e, t) {
    (0, n.canIUseGroupSetData)() ? e.groupSetData(t) : t();
}, Object.defineProperty(exports, "isDef", {
    enumerable: !0,
    get: function() {
        return t.isDef;
    }
}), exports.nextTick = function(e) {
    (0, n.canIUseNextTick)() ? wx.nextTick(e) : setTimeout(function() {
        e();
    }, 1e3 / 30);
}, exports.pickExclude = function(e, n) {
    if (!(0, t.isPlainObject)(e)) return {};
    return Object.keys(e).reduce(function(t, r) {
        return n.includes(r) || (t[r] = e[r]), t;
    }, {});
}, exports.range = function(e, t, n) {
    return Math.min(Math.max(e, t), n);
}, exports.requestAnimationFrame = function(e) {
    if ("devtools" === r().platform) return setTimeout(function() {
        e();
    }, 1e3 / 30);
    return wx.createSelectorQuery().selectViewport().boundingClientRect().exec(function() {
        e();
    });
}, exports.toPromise = function(e) {
    if ((0, t.isPromise)(e)) return e;
    return Promise.resolve(e);
}, require("../../../../@babel/runtime/helpers/Arrayincludes");

var e, t = require("./validator"), n = require("./version");

function r() {
    return null == e && (e = wx.getSystemInfoSync()), e;
}