var style = require('p_./miniprogram_npm/@vant/weapp/wxs/style.wxs')();
var addUnit = require('p_./miniprogram_npm/@vant/weapp/wxs/add-unit.wxs')();

function rootStyle(data) {
    return (style([({
        width: addUnit(data.width),
        height: addUnit(data.height),
        'border-radius': addUnit(data.radius),
    }), data.radius ? 'overflow: hidden' : null]))
};
var FIT_MODE_MAP = ({
    none: 'center',
    fill: 'scaleToFill',
    cover: 'aspectFill',
    contain: 'aspectFit',
    widthFix: 'widthFix',
    heightFix: 'heightFix',
});

function mode(fit) {
    return (FIT_MODE_MAP[((nt_0 = (fit), null == nt_0 ? undefined : 'number' === typeof nt_0 ? nt_0 : "" + nt_0))])
};
module.exports = ({
    rootStyle: rootStyle,
    mode: mode,
});