<textarea adjustPosition="{{adjustPosition}}" autoFocus="{{autoFocus}}" autoHeight="{{!!autosize}}" bindblur="onBlur" bindconfirm="onConfirm" bindfocus="onFocus" bindinput="onInput" bindkeyboardheightchange="onKeyboardHeightChange" bindlinechange="onLineChange" bindtap="onClickInput" class="{{utils.bem( 'field__control',[ inputAlign,type,{disabled:disabled,error:error} ] )}} input-class" cursor="{{cursor}}" cursorSpacing="{{cursorSpacing}}" disableDefaultPadding="{{disableDefaultPadding}}" disabled="{{disabled||readonly}}" fixed="{{fixed}}" focus="{{focus}}" holdKeyboard="{{holdKeyboard}}" maxlength="{{maxlength}}" placeholder="{{placeholder}}" placeholderClass="{{utils.bem( 'field__placeholder',{error:error,disabled:disabled} )}}" placeholderStyle="{{placeholderStyle}}" selectionEnd="{{selectionEnd}}" selectionStart="{{selectionStart}}" showConfirmBar="{{showConfirmBar}}" style="{{computed.inputStyle(autosize)}}" value="{{innerValue}}"></textarea>
