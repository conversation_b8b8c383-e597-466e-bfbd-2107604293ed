var e, t = require("./utils/http.js"), n = (e = require("./utils/tracker.js")) && e.__esModule ? e : {
    default: e
};

n.default.setPara({
    name: "hcx",
    server_url: "https://sdc.12580.com/datagateway/api/accept/applet",
    show_log: !0
}), n.default.setProfile({
    user_type: "1",
    business_type: "HCZH",
    platform_type: "client_2",
    cp_name: "wz"
}), n.default.init();

var a = require("./utils/transmission.js").rsaAesJs;

App({
    onLaunch: function() {
        this.autoUpdate();
    },
    globalData: {
        nowTime: null,
        isWorkTime: !0,
        nowTimeCompleted: !1,
        isFullScreen: !1,
        publicKey: "-----BEGIN PUBLIC KEY-----MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA3wzK0OjysbU742VeREaGU9QSf/+T6OVTXKMY1qOOqUdP3yK0HaXbqAXIuIIOoy2vCsQU7KKs5RGbPVskjq8mkPJWxvuDayuATJl8OUdWnwQthcDwuJD1n//Bz9oVSJCIZ2So8OdqgcqELY715nLU5fpEnyk9QgIzjCjaZkOLd5gdzD2rUItCEt4WPGTAWNLMQmvI5Kvma0Ndrh1dGJlFxN5osDA60cSYThaoxL9pSpUv86XIB3QZTEn7wPXUe/roFouixs470k33kfK3UgyqDbVtL1EgYUpzL/xiF/HA5w7mXuB26Tuc1JaQ21BHqlsjbmaoaKtDylxWrZtUmmolGQIDAQAB-----END PUBLIC KEY-----"
    },
    onShow: function() {
        var e = this;
        e.getServeTime(), wx.getSystemInfo({
            success: function(t) {
                -1 != t.model.search("iPhone X") && (e.globalData.isFullScreen = !0);
            }
        });
    },
    verificationTokenHN: function() {
        var e = new Promise(function(e, n) {
            var o = wx.getStorageSync("token_hn"), s = wx.getStorageSync("provinceCode"), i = wx.getStorageSync("cityCode_hn"), r = "".concat(o, "&").concat(s, "&").concat(i), c = a.EncryptDataHn(r);
            (0, t.POSTHN)("/newUser/selectUserInfoByToken", c).then(function(t) {
                if (console.log("app-verificationTokenHN-验证token，每次更新至最新--", t), t) if (t.successful) {
                    if (t.successful) {
                        wx.setStorageSync("isSuperVip", t.data.isSuperVip), wx.setStorageSync("provinceListVip", t.data.provinceList), 
                        "false" == t.data.isSuperVip && (wx.setStorageSync("provinceName", t.data.provinceName), 
                        wx.setStorageSync("provinceCode", t.data.provinceCode));
                        var n = wx.getStorageSync("userTypeIndex");
                        n >= t.data.userCodeList.length && (wx.setStorageSync("userTypeIndex", 0), n = 0), 
                        wx.setStorageSync("memberTypeList_hn", t.data.userCodeList), 0 == t.data.userCodeList.length ? (wx.setStorageSync("vipType_hn", t.data.defaultVipType), 
                        wx.setStorageSync("vipId_hn", "*"), wx.setStorageSync("feeAppCode_hn", t.data.defaultMemberGrade), 
                        wx.setStorageSync("memberTypeList_hn", []), wx.setStorageSync("isActivate", "0")) : (wx.setStorageSync("memberTypeList_hn", t.data.userCodeList), 
                        wx.setStorageSync("vipType_hn", t.data.userCodeList[n].vipType), wx.setStorageSync("vipId_hn", t.data.userCodeList[n].codeId), 
                        wx.setStorageSync("feeAppCode_hn", t.data.userCodeList[n].feeAppCode), wx.setStorageSync("isActivate", t.data.userCodeList[n].isActivate)), 
                        wx.setStorageSync("token_hn", t.data.token), wx.setStorageSync("telephone_hn", t.data.telephone), 
                        "" != t.data.cityCode && null != t.data.cityCode && (wx.setStorageSync("cityCode_hn", t.data.cityCode), 
                        wx.setStorageSync("cityName_hn", t.data.cityName)), wx.setStorageSync("provinceCenter_hn", t.data.provinceCenter), 
                        wx.setStorageSync("loginStatus_hn", "1"), e(t.data);
                    }
                } else wx.setStorageSync("loginStatus_hn", "0"), wx.setStorageSync("token_hn", ""), 
                wx.setStorageSync("userTypeIndex", 0), wx.setStorageSync("provinceCity_hn", ""), 
                wx.setStorageSync("isSuperVip", !1), wx.setStorageSync("provinceListVip", []), e(t.data); else e(t);
            });
        });
        return e;
    },
    onNetworkStatusChange: function() {
        return new Promise(function(e, t) {
            wx.getNetworkType({
                success: function(t) {
                    console.log("-resres----", t), "none" != t.networkType ? (e(!0), wx.onNetworkStatusChange(function(t) {
                        t.isConnected ? e(!0) : e(!1);
                    })) : (wx.onNetworkStatusChange(function(t) {
                        t.isConnected ? e(!0) : e(!1);
                    }), setTimeout(function() {
                        e(!1);
                    }, 3e3));
                }
            });
        });
    },
    getSystemTimeFromService: function() {
        return new Promise(function(e, n) {
            wx.request({
                url: t.baseUrlHN + "/getTime/getSystemTimeFromCta",
                method: "get",
                header: {
                    contentType: "application/json",
                    authorization: "FzPv3whtd4oz8C7fADvocu4E77xdRtA9"
                },
                success: function(t) {
                    if (t.data && 200 === t.statusCode) try {
                        var o = a.DecryptKeyHn(t.data);
                        o && o.successful ? e(o.data) : n();
                    } catch (e) {
                        e = VM2_INTERNAL_STATE_DO_NOT_USE_OR_PROGRAM_WILL_FAIL.handleException(e);
                        console.log("getSystemTimeFromCta--err--", e), n();
                    } else n();
                },
                fail: function() {
                    n();
                }
            });
        });
    },
    getSystemTimeFromCta: function() {
        var e = this;
        return new Promise(function(t, n) {
            e.getSystemTimeFromService().then(function(e) {
                t(e);
            }).catch(function() {
                wx.request({
                    url: baseUrlIp + "/getTime/getSystemTimeFromService",
                    method: "get",
                    header: {
                        contentType: "application/json",
                        authorization: "FzPv3whtd4oz8C7fADvocu4E77xdRtA9"
                    },
                    success: function(e) {
                        e && e.data && e.data.successful ? (console.log(baseUrlIp + "/getTime/getSystemTimeFromService--", e.data), 
                        t(e.data.data)) : n();
                    },
                    fail: function() {
                        n();
                    }
                });
            });
        });
    },
    getServeTime: function() {
        var e = new Date(), t = e.getHours();
        if (wx.setStorageSync("nowTimeStr", e), wx.setStorageSync("yearsApp", e.getFullYear()), 
        wx.setStorageSync("monthApp", e.getMonth() + 1), wx.setStorageSync("dayApp", e.getDay()), 
        console.log(t), 0 == t || t < 8) return wx.reLaunch({
            url: "/pages/maintain/maintain"
        }), this.globalData.isWorkTime = !1, void (this.globalData.nowTimeCompleted = !0);
        this.globalData.nowTimeCompleted = !0;
    },
    autoUpdate: function() {
        console.log(new Date());
        var e = this;
        if (wx.canIUse("getUpdateManager")) {
            var t = wx.getUpdateManager();
            t.onCheckForUpdate(function(n) {
                n.hasUpdate && (t.onUpdateReady(function() {
                    console.log(new Date()), wx.showModal({
                        title: "更新提示",
                        content: "新版本已经准备好，是否重启应用？",
                        success: function(n) {
                            n.confirm ? t.applyUpdate() : n.cancel && wx.showModal({
                                title: "温馨提示~",
                                content: "本次版本更新涉及到新的功能添加，旧版本无法正常访问的哦~",
                                success: function(t) {
                                    e.autoUpdate();
                                }
                            });
                        }
                    });
                }), t.onUpdateFailed(function() {
                    wx.showModal({
                        title: "已经有新版本了哟~",
                        content: "新版本已经上线啦~，请您删除当前小程序，重新搜索打开哟~"
                    });
                }));
            });
        } else wx.showModal({
            title: "提示",
            content: "当前微信版本过低，无法使用该功能，请升级到最新微信版本后重试。"
        });
    }
});