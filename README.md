# 12580小程序完整加密解密工具

## 🎯 项目概述

这是一个完整的12580小程序加密解密工具，支持：
- ✅ 请求数据加密（与小程序完全一致）
- ✅ 响应数据解密（与小程序完全一致）
- ✅ 湖南版本和普通版本
- ✅ 真实数据测试

## 📁 文件说明

### 核心文件
- `final_crypto_tool.js` - 最终版本的加密解密工具
- `final_test.js` - 测试文件
- `install_and_test.bat` - 一键安装和测试脚本

### 分析文档
- `完整加密解密分析.md` - 详细的技术分析
- `README.md` - 使用说明（本文件）

### 其他测试文件
- `crypto_tool.js` - 基础版本工具
- `test_crypto.js` - 基础测试
- `rsa_test.js` - RSA测试

## 🚀 快速开始

### 方法一：一键运行（推荐）
```bash
# 双击运行
install_and_test.bat
```

### 方法二：手动安装
```bash
# 1. 安装依赖
npm install jsrsasign crypto-js

# 2. 运行测试
node final_test.js
```

## 🔧 使用方法

### 基础使用
```javascript
const FinalCryptoTool = require('./final_crypto_tool.js');

// 加密请求数据
var requestData = {
    sCode: "SJS_CODE_12345",
    areaCode: "430100"
};
var encrypted = FinalCryptoTool.EncryptDataHn(requestData);

// 解密响应数据
var serverResponse = {
    key: "服务器返回的key",
    content: "服务器返回的content",
    iv: "服务器返回的iv"
};
var decrypted = FinalCryptoTool.DecryptKeyHn(serverResponse);
```

### 解密你的真实数据
```javascript
// 测试你提供的真实响应数据
var result = FinalCryptoTool.testRealData();
console.log("解密结果:", result);
```

## 📊 技术架构

### 加密流程
```
原始数据 → AES-256-CBC加密 → RSA加密AES密钥 → 发送
```

### 解密流程
```
接收 → RSA解密AES密钥 → AES解密数据 → 原始数据
```

### 使用的库
- **jsrsasign**: RSA加密解密（与小程序一致）
- **crypto-js**: AES加密解密（与小程序一致）

### 数据格式
- **请求**: `{avd: IV, ksynum: 加密密钥, cmcxncxn: 加密数据}`
- **响应**: `{key: 加密密钥, content: 加密数据, iv: IV}`

## 🧪 测试结果

运行测试后，你应该看到类似的输出：

```
🎯 最终版本12580小程序加密解密测试

============================================================
📋 测试1：基础加密解密
📝 原始数据: { sCode: 'SJS_CODE_12345', areaCode: '430100' }
🔑 生成AES密钥: [32字符密钥]
🎲 生成IV: [16字符IV]
🔐 AES加密中...
✅ AES加密成功
🔐 RSA加密中...
✅ RSA加密成功
✅ 湖南版本加密完成
🔐 加密成功

🔓 RSA解密中...
✅ RSA解密成功
🔓 AES解密中...
✅ AES解密成功
🎯 湖南版本解密完成
🔓 解密成功: { sCode: 'SJS_CODE_12345', areaCode: '430100' }
✅ 验证结果: 通过

============================================================
📋 测试2：解密你提供的真实数据
🧪 测试你提供的真实响应数据:
🔓 RSA解密中...
✅ RSA解密成功
🔓 AES解密中...
✅ AES解密成功
🎯 湖南版本解密完成
🎉 真实数据解密成功！
🔓 解密结果: [你的真实解密数据]
```

## 🔍 核心发现

### 1. 双版本架构
- **普通版本**: 使用一套RSA密钥对
- **湖南版本**: 使用另一套RSA密钥对

### 2. 加密算法
- **AES**: AES-256-CBC + PKCS7填充
- **RSA**: RSA-1024 + PKCS1填充
- **编码**: Base64编码

### 3. 密钥生成
- **AES密钥**: UUID → UTF8 → Base64 → 截取32字符
- **IV**: UUID → UTF8 → Base64 → 截取16字符

### 4. 数据流向
```
客户端                           服务器
  |                               |
  | 1. 生成AES密钥和IV              |
  | 2. AES加密原始数据              |
  | 3. RSA加密AES密钥               |
  | 4. 发送{avd,ksynum,cmcxncxn}   |
  |----------------------------->|
  |                               | 5. RSA解密AES密钥
  |                               | 6. AES解密数据
  |                               | 7. 处理业务逻辑
  |                               | 8. AES加密响应数据
  |                               | 9. RSA加密新AES密钥
  | 10. 接收{key,content,iv}       |
  |<-----------------------------|
  | 11. RSA解密AES密钥              |
  | 12. AES解密响应数据             |
  | 13. 处理响应结果                |
```

## ⚠️ 注意事项

1. **依赖库**: 需要安装 `jsrsasign` 和 `crypto-js`
2. **Node.js版本**: 建议使用 Node.js 14+ 
3. **密钥安全**: 私钥仅用于测试，生产环境请妥善保管
4. **数据格式**: 确保输入数据格式正确

## 🎉 总结

这个工具完全基于小程序的真实逻辑，使用相同的库和算法，可以：
- ✅ 加密任意请求数据
- ✅ 解密任意响应数据
- ✅ 处理你提供的真实数据
- ✅ 支持所有数据类型（字符串、JSON、数组等）

现在你可以完全理解和使用12580小程序的加密解密逻辑了！
