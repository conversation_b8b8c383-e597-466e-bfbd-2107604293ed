	<style> </style> 	<page></page> 	<script> 	var __setCssStartTime__ = Date.now();	setCssToHead([],undefined,{path:"./pages/index/storeInfo-list-template/storeInfo-list-template.wxss"})()
	var __setCssEndTime__ = Date.now();	(function() { 		var gf = $gwx( './pages/index/storeInfo-list-template/storeInfo-list-template.wxml' ); 		if (window.__wxAppCodeReadyCallback__) { 			window.__wxAppCodeReadyCallback__(gf); 		} else { 			document.dispatchEvent(new CustomEvent( "generateFuncReady", { 				detail: { 					generateFunc: gf 			}})); 		} 	})(); 	</script> 	 