/**
 * 最终版本的12580小程序加密解密工具
 * 使用与小程序相同的逻辑和库
 * 
 * 运行方法：
 * npm install jsrsasign crypto-js
 * node final_crypto_tool.js
 */

// 检查是否安装了必要的依赖
try {
    var KJUR = require('jsrsasign');
    var CryptoJS = require('crypto-js');
    console.log("✅ 依赖库加载成功");
} catch (e) {
    console.error("❌ 缺少依赖库，请运行：npm install jsrsasign crypto-js");
    process.exit(1);
}

var FinalCryptoTool = {
    
    // ==================== RSA密钥配置 ====================
    
    // 湖南版本的RSA密钥对
    HN_KEYS: {
        PUBLIC_KEY: `-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAu1l6RjXiJBajEvWqcymhwlBX55m3HFUaGC7F2G51ytNJwsFON9jJqeAwB83+uUVJYM15MrBBMWAul0lGSmoRMsVnT1KR7p7UxnbGgdZT1NJNZgTu1J16MV98EUhImSk8jxrmoRdUGgDE1vvvlcOack8+xDDrF9x6tIl1Kyr7uFz1VXD17LTwJ5ajRtkp5Tszl+xs0fqKhxbXVknqCMGQcW9IGwNrr4/coaP+SmEUkIUQ5jKr69PI2ATjXghC7SUIfVOpplM0AV0PV7qvPYysyAXMjjieQ98jIOOYrEo7ay6qhtgiCiVr7k+bBi3gv9+2vlpMhPjdkAaIPwEnE40FOwIDAQAB
-----END PUBLIC KEY-----`,
        PRIVATE_KEY: `**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`
    },
    
    // ==================== 工具函数 ====================
    
    // 生成UUID（与小程序一致）
    uuid: function() {
        var chars = "0123456789abcdef";
        var uuid = [];
        for (var i = 0; i < 36; i++) {
            uuid[i] = chars.substr(Math.floor(Math.random() * 16), 1);
        }
        uuid[14] = "4";
        uuid[19] = chars.substr((uuid[19] & 0x3) | 0x8, 1);
        uuid[8] = uuid[13] = uuid[18] = uuid[23] = "-";
        return uuid.join("");
    },
    
    // 生成AES密钥（与小程序一致）
    getAesKey: function() {
        var uuid = this.uuid();
        var utf8 = CryptoJS.enc.Utf8.parse(uuid);
        return CryptoJS.enc.Base64.stringify(utf8).substring(2, 34);
    },
    
    // 生成IV（与小程序一致）
    getIv: function() {
        var uuid = this.uuid();
        var utf8 = CryptoJS.enc.Utf8.parse(uuid);
        return CryptoJS.enc.Base64.stringify(utf8).substring(2, 18);
    },
    
    // Base64转Hex
    b64tohex: function(b64) {
        try {
            var binary = Buffer.from(b64, 'base64').toString('binary');
            var hex = '';
            for (var i = 0; i < binary.length; i++) {
                var h = binary.charCodeAt(i).toString(16);
                hex += h.length === 1 ? '0' + h : h;
            }
            return hex;
        } catch (e) {
            console.error("❌ Base64转Hex失败:", e.message);
            return null;
        }
    },
    
    // Hex转Base64
    hex2b64: function(hex) {
        try {
            var binary = '';
            for (var i = 0; i < hex.length; i += 2) {
                binary += String.fromCharCode(parseInt(hex.substr(i, 2), 16));
            }
            return Buffer.from(binary, 'binary').toString('base64');
        } catch (e) {
            console.error("❌ Hex转Base64失败:", e.message);
            return null;
        }
    },
    
    // ==================== AES加密解密（使用CryptoJS） ====================
    
    // AES加密（与小程序一致）
    encrypt: function(plaintext, key, iv) {
        try {
            console.log("🔐 AES加密中...");
            
            var message = CryptoJS.enc.Utf8.parse(plaintext);
            var keyParsed = CryptoJS.enc.Utf8.parse(key);
            var ivParsed = CryptoJS.enc.Utf8.parse(iv);
            
            var encrypted = CryptoJS.AES.encrypt(message, keyParsed, {
                iv: ivParsed,
                mode: CryptoJS.mode.CBC,
                padding: CryptoJS.pad.Pkcs7
            });
            
            var result = CryptoJS.enc.Base64.stringify(encrypted.ciphertext);
            console.log("✅ AES加密成功");
            return result;
            
        } catch (e) {
            console.error("❌ AES加密失败:", e.message);
            return null;
        }
    },
    
    // AES解密（与小程序一致）
    decrypt: function(ciphertext, key, iv) {
        try {
            console.log("🔓 AES解密中...");
            
            var keyParsed = CryptoJS.enc.Utf8.parse(key);
            var ivParsed = CryptoJS.enc.Utf8.parse(iv);
            
            var decrypted = CryptoJS.AES.decrypt(ciphertext, keyParsed, {
                iv: ivParsed,
                mode: CryptoJS.mode.CBC,
                padding: CryptoJS.pad.Pkcs7
            });
            
            var result = CryptoJS.enc.Utf8.stringify(decrypted);
            console.log("✅ AES解密成功");
            return result;
            
        } catch (e) {
            console.error("❌ AES解密失败:", e.message);
            return null;
        }
    },
    
    // ==================== RSA加密解密（使用jsrsasign） ====================
    
    // RSA加密（与小程序一致）
    rsaEncrypt: function(plaintext, publicKeyPem) {
        try {
            console.log("🔐 RSA加密中...");
            
            var publicKey = KJUR.KEYUTIL.getKey(publicKeyPem);
            var encrypted = publicKey.encrypt(plaintext);
            
            console.log("✅ RSA加密成功");
            return encrypted;
            
        } catch (e) {
            console.error("❌ RSA加密失败:", e.message);
            return null;
        }
    },
    
    // RSA解密（与小程序一致）
    rsaDecrypt: function(encryptedHex, privateKeyPem) {
        try {
            console.log("🔓 RSA解密中...");
            
            var privateKey = KJUR.KEYUTIL.getKey(privateKeyPem);
            var decrypted = privateKey.decrypt(encryptedHex);
            
            console.log("✅ RSA解密成功");
            return decrypted;
            
        } catch (e) {
            console.error("❌ RSA解密失败:", e.message);
            return null;
        }
    },
    
    // ==================== 主要加密解密函数 ====================
    
    // 湖南版本请求加密（与小程序一致）
    EncryptDataHn: function(data) {
        console.log("\n========== 湖南版本请求加密 ==========");
        console.log("📝 原始数据:", data);
        
        try {
            var dataString = typeof data === 'object' ? JSON.stringify(data) : String(data);
            
            // 1. 生成AES密钥和IV
            var aesKey = this.getAesKey();
            var iv = this.getIv();
            console.log("🔑 生成AES密钥:", aesKey);
            console.log("🎲 生成IV:", iv);
            
            // 2. AES加密数据
            var encryptedData = this.encrypt(dataString, aesKey, iv);
            if (!encryptedData) throw new Error("AES加密失败");
            
            // 3. RSA加密AES密钥
            var encryptedKey = this.rsaEncrypt(aesKey, this.HN_KEYS.PUBLIC_KEY);
            if (!encryptedKey) throw new Error("RSA加密失败");
            
            // 4. 构造结果
            var result = {
                avd: iv,
                ksynum: this.hex2b64(encryptedKey),
                cmcxncxn: encryptedData
            };
            
            console.log("✅ 湖南版本加密完成");
            return result;
            
        } catch (error) {
            console.error("❌ 湖南版本加密失败:", error.message);
            return null;
        }
    },
    
    // 湖南版本响应解密（与小程序一致）
    DecryptKeyHn: function(responseData) {
        console.log("\n========== 湖南版本响应解密 ==========");
        
        try {
            // 1. Base64转Hex
            var hexKey = this.b64tohex(responseData.key);
            if (!hexKey) throw new Error("密钥格式转换失败");
            
            // 2. RSA解密AES密钥
            var aesKey = this.rsaDecrypt(hexKey, this.HN_KEYS.PRIVATE_KEY);
            if (!aesKey) throw new Error("RSA解密失败");
            
            // 3. AES解密内容
            var decryptedContent = this.decrypt(responseData.content, aesKey, responseData.iv);
            if (!decryptedContent) throw new Error("AES解密失败");
            
            // 4. 解析JSON
            try {
                var result = JSON.parse(decryptedContent);
                console.log("🎯 湖南版本解密完成");
                return result;
            } catch (jsonError) {
                console.log("⚠️  JSON解析失败，返回原始字符串");
                return decryptedContent;
            }
            
        } catch (error) {
            console.error("❌ 湖南版本解密失败:", error.message);
            return null;
        }
    },
    
    // 测试你提供的真实数据
    testRealData: function() {
        console.log("🧪 测试你提供的真实响应数据:");
        
        var realResponseData = {
            "content": "Z5KNaR1xKweWGg2UUKPJ1xuppg7NE+MSeduoUrt7OlfofxEG6MA/EmFSVN1PN3TPOk2L9yWmtpwyxCPCrO77GhHZ889e7oTvXRVRM6F5vsQKIOuCY2NxFgqCt996rz1jCjP2nNkexi5UDgC+FYLGtAjsyR0bymB93y2TfMz/JK78PO47882bxE+2odl3BrIUD3QZ299aMMtsfirDcFmdgoS5Y72DGUHul1o6KCtP2AK7JmObE9oJUeodcspplF2PJ4Lg7s/kTgXxruU/fnfwUo5QrzTCmRaHK9QOBMTn4hxGXbNWXjhSB92e30K/E07zyLZBk8yfqSLlM6U94T/128M8i0Lnc9aCKIM7YmeSeHiZh+BlXM4O4gj8bk51PNNKsVeW7owo5TCBUkbyXLb6zA==",
            "key": "S5OUYtFrpKMZUGXPDE7TTT0HvZ0dCQpyA6DNznsWnUTMSFtkAXvRSPMWSve+z8Gsv4xOJJtgPLrAw5qeqly0HHgZpWyjDzOjahjeaJdkA7zoUtayySGX3yfUSCja1NtJOPGCn5OzJ6JJTg5OCfDdH0rj1KB06zHQ0YtsP5MTaMGSBj2ZkU4AkbHF3/MfpSuDnUYIevWg+uv3ywi3h5zBwyL8RjiqoVzGDy6pwLmp8o2Z+Gj6IJ8QYooItYNaQ7SG196vO8xfZd+sRm/RHen9dk4WqAEV8z6/FHkgvoGAVPrJVHLcNBEJee5irNIrQxeNP17UkOytbEvSxAkNXUPK4A==",
            "iv": "hihSPyR1EvP5$*!*"
        };

        return this.DecryptKeyHn(realResponseData);
    }
};

// 导出模块
module.exports = FinalCryptoTool;
