/**
 * 12580小程序完整加密解密工具
 * 支持普通版本和湖南版本的加密解密
 * 包含完整的RSA+AES混合加密逻辑
 * 
 * 使用方法：
 * node 完整加密解密工具.js
 */

// 检查Node.js环境
if (typeof require === 'undefined') {
    console.error("❌ 此工具需要在Node.js环境中运行");
    console.log("💡 请安装Node.js并使用命令: node 完整加密解密工具.js");
} else {
    const crypto = require('crypto');
    
    var CryptoTool = {
        
        // ==================== RSA密钥配置 ====================
        
        // 普通版本密钥对
        NORMAL_KEYS: {
            PUBLIC_KEY: `-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA3wzK0OjysbU742VeREaGU9QSf/+T6OVTXKMY1qOOqUdP3yK0HaXbqAXIuIIOoy2vCsQU7KKs5RGbPVskjq8mkPJWxvuDayuATJl8OUdWnwQthcDwuJD1n//Bz9oVSJCIZ2So8OdqgcqELY715nLU5fpEnyk9QgIzjCjaZkOLd5gdzD2rUItCEt4WPGTAWNLMQmvI5Kvma0Ndrh1dGJlFxN5osDA60cSYThaoxL9pSpUv86XIB3QZTEn7wPXUe/roFouixs470k33kfK3UgyqDbVtL1EgYUpzL/xiF/HA5w7mXuB26Tuc1JaQ21BHqlsjbmaoaKtDylxWrZtUmmolGQIDAQAB
-----END PUBLIC KEY-----`,
            PRIVATE_KEY: `**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`
        },
        
        // 湖南版本密钥对
        HN_KEYS: {
            PUBLIC_KEY: `-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAu1l6RjXiJBajEvWqcymhwlBX55m3HFUaGC7F2G51ytNJwsFON9jJqeAwB83+uUVJYM15MrBBMWAul0lGSmoRMsVnT1KR7p7UxnbGgdZT1NJNZgTu1J16MV98EUhImSk8jxrmoRdUGgDE1vvvlcOack8+xDDrF9x6tIl1Kyr7uFz1VXD17LTwJ5ajRtkp5Tszl+xs0fqKhxbXVknqCMGQcW9IGwNrr4/coaP+SmEUkIUQ5jKr69PI2ATjXghC7SUIfVOpplM0AV0PV7qvPYysyAXMjjieQ98jIOOYrEo7ay6qhtgiCiVr7k+bBi3gv9+2vlpMhPjdkAaIPwEnE40FOwIDAQAB
-----END PUBLIC KEY-----`,
            PRIVATE_KEY: `**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`
        },
        
        // ==================== 工具函数 ====================
        
        // 生成UUID
        generateUUID: function() {
            var chars = "0123456789abcdef";
            var uuid = [];
            for (var i = 0; i < 36; i++) {
                uuid[i] = chars.substr(Math.floor(Math.random() * 16), 1);
            }
            uuid[14] = "4";
            uuid[19] = chars.substr((uuid[19] & 0x3) | 0x8, 1);
            uuid[8] = uuid[13] = uuid[18] = uuid[23] = "-";
            return uuid.join("");
        },
        
        // 生成AES密钥（32字符）
        getAesKey: function() {
            var uuid = this.generateUUID();
            var base64 = Buffer.from(uuid, 'utf8').toString('base64');
            return base64.substring(2, 34);
        },
        
        // 生成IV（16字符）
        getIv: function() {
            var uuid = this.generateUUID();
            var base64 = Buffer.from(uuid, 'utf8').toString('base64');
            return base64.substring(2, 18);
        },
        
        // Base64转Hex
        b64tohex: function(b64) {
            try {
                var binary = Buffer.from(b64, 'base64').toString('binary');
                var hex = '';
                for (var i = 0; i < binary.length; i++) {
                    var h = binary.charCodeAt(i).toString(16);
                    hex += h.length === 1 ? '0' + h : h;
                }
                return hex;
            } catch (e) {
                console.error("❌ Base64转Hex失败:", e.message);
                return null;
            }
        },
        
        // Hex转Base64
        hex2b64: function(hex) {
            try {
                var binary = '';
                for (var i = 0; i < hex.length; i += 2) {
                    binary += String.fromCharCode(parseInt(hex.substr(i, 2), 16));
                }
                return Buffer.from(binary, 'binary').toString('base64');
            } catch (e) {
                console.error("❌ Hex转Base64失败:", e.message);
                return null;
            }
        },
        
        // ==================== AES加密解密 ====================
        
        // AES加密
        aesEncrypt: function(plaintext, key, iv) {
            try {
                console.log("🔐 AES加密中...");
                console.log("  明文:", plaintext.substring(0, 100) + (plaintext.length > 100 ? "..." : ""));
                console.log("  密钥:", key);
                console.log("  IV:", iv);
                
                // 确保密钥和IV长度正确
                var keyBuffer = Buffer.from(key, 'utf8');
                var ivBuffer = Buffer.from(iv, 'utf8');
                
                // 调整密钥长度到32字节
                if (keyBuffer.length > 32) {
                    keyBuffer = keyBuffer.subarray(0, 32);
                } else if (keyBuffer.length < 32) {
                    var paddedKey = Buffer.alloc(32);
                    keyBuffer.copy(paddedKey);
                    keyBuffer = paddedKey;
                }
                
                // 调整IV长度到16字节
                if (ivBuffer.length > 16) {
                    ivBuffer = ivBuffer.subarray(0, 16);
                } else if (ivBuffer.length < 16) {
                    var paddedIv = Buffer.alloc(16);
                    ivBuffer.copy(paddedIv);
                    ivBuffer = paddedIv;
                }
                
                // 创建加密器
                var cipher = crypto.createCipheriv('aes-256-cbc', keyBuffer, ivBuffer);
                
                // 加密
                var encrypted = cipher.update(plaintext, 'utf8', 'base64');
                encrypted += cipher.final('base64');
                
                console.log("✅ AES加密成功");
                return encrypted;
                
            } catch (e) {
                console.error("❌ AES加密失败:", e.message);
                return null;
            }
        },
        
        // AES解密
        aesDecrypt: function(ciphertext, key, iv) {
            try {
                console.log("🔓 AES解密中...");
                console.log("  密文:", ciphertext.substring(0, 100) + (ciphertext.length > 100 ? "..." : ""));
                console.log("  密钥:", key);
                console.log("  IV:", iv);
                
                // 确保密钥和IV长度正确
                var keyBuffer = Buffer.from(key, 'utf8');
                var ivBuffer = Buffer.from(iv, 'utf8');
                
                // 调整密钥长度到32字节
                if (keyBuffer.length > 32) {
                    keyBuffer = keyBuffer.subarray(0, 32);
                } else if (keyBuffer.length < 32) {
                    var paddedKey = Buffer.alloc(32);
                    keyBuffer.copy(paddedKey);
                    keyBuffer = paddedKey;
                }
                
                // 调整IV长度到16字节
                if (ivBuffer.length > 16) {
                    ivBuffer = ivBuffer.subarray(0, 16);
                } else if (ivBuffer.length < 16) {
                    var paddedIv = Buffer.alloc(16);
                    ivBuffer.copy(paddedIv);
                    ivBuffer = paddedIv;
                }
                
                // 创建解密器
                var decipher = crypto.createDecipheriv('aes-256-cbc', keyBuffer, ivBuffer);
                
                // 解密
                var decrypted = decipher.update(ciphertext, 'base64', 'utf8');
                decrypted += decipher.final('utf8');
                
                console.log("✅ AES解密成功");
                return decrypted;
                
            } catch (e) {
                console.error("❌ AES解密失败:", e.message);
                return null;
            }
        },
        
        // ==================== RSA加密解密 ====================
        
        // RSA加密
        rsaEncrypt: function(plaintext, publicKey) {
            try {
                console.log("🔐 RSA加密中...");
                console.log("  明文:", plaintext);
                
                var encrypted = crypto.publicEncrypt(
                    {
                        key: publicKey,
                        padding: crypto.constants.RSA_PKCS1_OAEP_PADDING,
                        oaepHash: 'sha1'
                    },
                    Buffer.from(plaintext, 'utf8')
                );
                
                var result = encrypted.toString('hex');
                console.log("✅ RSA加密成功");
                return result;
                
            } catch (e) {
                console.error("❌ RSA加密失败:", e.message);
                return null;
            }
        },
        
        // RSA解密
        rsaDecrypt: function(encryptedHex, privateKey) {
            try {
                console.log("🔓 RSA解密中...");
                console.log("  密文(hex):", encryptedHex.substring(0, 100) + "...");
                
                var encryptedBuffer = Buffer.from(encryptedHex, 'hex');
                
                var decrypted = crypto.privateDecrypt(
                    {
                        key: privateKey,
                        padding: crypto.constants.RSA_PKCS1_OAEP_PADDING,
                        oaepHash: 'sha1'
                    },
                    encryptedBuffer
                );
                
                var result = decrypted.toString('utf8');
                console.log("✅ RSA解密成功，AES密钥:", result);
                return result;
                
            } catch (e) {
                console.error("❌ RSA解密失败:", e.message);
                return null;
            }
        }
    };
    
        // ==================== 主要加密解密函数 ====================

        // 普通版本请求加密
        EncryptData: function(data) {
            console.log("\n========== 普通版本请求加密 ==========");
            console.log("📝 原始数据:", data);

            try {
                var dataString = typeof data === 'object' ? JSON.stringify(data) : String(data);

                // 1. 生成AES密钥和IV
                var aesKey = this.getAesKey();
                var iv = this.getIv();
                console.log("🔑 生成AES密钥:", aesKey);
                console.log("🎲 生成IV:", iv);

                // 2. AES加密数据
                var encryptedData = this.aesEncrypt(dataString, aesKey, iv);
                if (!encryptedData) throw new Error("AES加密失败");

                // 3. RSA加密AES密钥
                var encryptedKey = this.rsaEncrypt(aesKey, this.NORMAL_KEYS.PUBLIC_KEY);
                if (!encryptedKey) throw new Error("RSA加密失败");

                // 4. 构造结果
                var result = {
                    avd: iv,
                    ksynum: this.hex2b64(encryptedKey),
                    cmcxncxn: encryptedData
                };

                console.log("✅ 普通版本加密完成");
                return result;

            } catch (error) {
                console.error("❌ 普通版本加密失败:", error.message);
                return null;
            }
        },

        // 湖南版本请求加密
        EncryptDataHn: function(data) {
            console.log("\n========== 湖南版本请求加密 ==========");
            console.log("📝 原始数据:", data);

            try {
                var dataString = typeof data === 'object' ? JSON.stringify(data) : String(data);

                // 1. 生成AES密钥和IV
                var aesKey = this.getAesKey();
                var iv = this.getIv();
                console.log("🔑 生成AES密钥:", aesKey);
                console.log("🎲 生成IV:", iv);

                // 2. AES加密数据
                var encryptedData = this.aesEncrypt(dataString, aesKey, iv);
                if (!encryptedData) throw new Error("AES加密失败");

                // 3. RSA加密AES密钥
                var encryptedKey = this.rsaEncrypt(aesKey, this.HN_KEYS.PUBLIC_KEY);
                if (!encryptedKey) throw new Error("RSA加密失败");

                // 4. 构造结果
                var result = {
                    avd: iv,
                    ksynum: this.hex2b64(encryptedKey),
                    cmcxncxn: encryptedData
                };

                console.log("✅ 湖南版本加密完成");
                return result;

            } catch (error) {
                console.error("❌ 湖南版本加密失败:", error.message);
                return null;
            }
        },

        // 普通版本响应解密
        DecryptKey: function(responseData) {
            console.log("\n========== 普通版本响应解密 ==========");
            console.log("📦 响应数据:", {
                key: responseData.key ? responseData.key.substring(0, 50) + "..." : "无",
                content: responseData.content ? responseData.content.substring(0, 50) + "..." : "无",
                iv: responseData.iv || "无"
            });

            try {
                // 1. Base64转Hex
                var hexKey = this.b64tohex(responseData.key);
                if (!hexKey) throw new Error("密钥格式转换失败");

                // 2. RSA解密AES密钥
                var aesKey = this.rsaDecrypt(hexKey, this.NORMAL_KEYS.PRIVATE_KEY);
                if (!aesKey) throw new Error("RSA解密失败");

                // 3. AES解密内容
                var decryptedContent = this.aesDecrypt(responseData.content, aesKey, responseData.iv);
                if (!decryptedContent) throw new Error("AES解密失败");

                // 4. 解析JSON
                try {
                    var result = JSON.parse(decryptedContent);
                    console.log("🎯 普通版本解密完成");
                    return result;
                } catch (jsonError) {
                    console.log("⚠️  JSON解析失败，返回原始字符串");
                    return decryptedContent;
                }

            } catch (error) {
                console.error("❌ 普通版本解密失败:", error.message);
                return null;
            }
        },

        // 湖南版本响应解密
        DecryptKeyHn: function(responseData) {
            console.log("\n========== 湖南版本响应解密 ==========");
            console.log("📦 响应数据:", {
                key: responseData.key ? responseData.key.substring(0, 50) + "..." : "无",
                content: responseData.content ? responseData.content.substring(0, 50) + "..." : "无",
                iv: responseData.iv || "无"
            });

            try {
                // 1. Base64转Hex
                var hexKey = this.b64tohex(responseData.key);
                if (!hexKey) throw new Error("密钥格式转换失败");

                // 2. RSA解密AES密钥
                var aesKey = this.rsaDecrypt(hexKey, this.HN_KEYS.PRIVATE_KEY);
                if (!aesKey) throw new Error("RSA解密失败");

                // 3. AES解密内容
                var decryptedContent = this.aesDecrypt(responseData.content, aesKey, responseData.iv);
                if (!decryptedContent) throw new Error("AES解密失败");

                // 4. 解析JSON
                try {
                    var result = JSON.parse(decryptedContent);
                    console.log("🎯 湖南版本解密完成");
                    return result;
                } catch (jsonError) {
                    console.log("⚠️  JSON解析失败，返回原始字符串");
                    return decryptedContent;
                }

            } catch (error) {
                console.error("❌ 湖南版本解密失败:", error.message);
                return null;
            }
        },

        // 智能解密（自动尝试两个版本）
        smartDecrypt: function(responseData) {
            console.log("\n========== 智能解密（自动检测版本） ==========");

            // 先尝试湖南版本
            console.log("🔍 尝试湖南版本解密...");
            var hnResult = this.DecryptKeyHn(responseData);
            if (hnResult) {
                console.log("✅ 湖南版本解密成功");
                return { version: "湖南版本", data: hnResult };
            }

            // 再尝试普通版本
            console.log("🔍 尝试普通版本解密...");
            var normalResult = this.DecryptKey(responseData);
            if (normalResult) {
                console.log("✅ 普通版本解密成功");
                return { version: "普通版本", data: normalResult };
            }

            console.error("❌ 所有版本解密都失败");
            return null;
        },

        // ==================== 测试函数 ====================

        // 测试完整的加密解密流程
        testFullCycle: function(testData, version) {
            version = version || "湖南版本";
            console.log("\n🧪 ========== 完整加密解密测试 (" + version + ") ==========");
            console.log("🎯 测试数据:", testData);

            // 1. 加密
            var encrypted;
            if (version === "湖南版本") {
                encrypted = this.EncryptDataHn(testData);
            } else {
                encrypted = this.EncryptData(testData);
            }

            if (!encrypted) {
                console.error("❌ 加密失败");
                return false;
            }

            // 2. 模拟服务器响应格式
            var serverResponse = {
                key: encrypted.ksynum,
                content: encrypted.cmcxncxn,
                iv: encrypted.avd
            };

            // 3. 解密
            var decrypted;
            if (version === "湖南版本") {
                decrypted = this.DecryptKeyHn(serverResponse);
            } else {
                decrypted = this.DecryptKey(serverResponse);
            }

            if (!decrypted) {
                console.error("❌ 解密失败");
                return false;
            }

            // 4. 验证
            var originalStr = typeof testData === 'object' ? JSON.stringify(testData) : String(testData);
            var decryptedStr = typeof decrypted === 'object' ? JSON.stringify(decrypted) : String(decrypted);

            var success = originalStr === decryptedStr;
            console.log("\n🔍 ========== 验证结果 ==========");
            console.log("📝 原始数据:", originalStr);
            console.log("🔓 解密数据:", decryptedStr);
            console.log("✅ 数据一致性:", success ? "通过 ✅" : "失败 ❌");

            return success;
        },

        // 测试你提供的真实数据
        testRealData: function() {
            console.log("🧪 测试你提供的真实响应数据:");

            var realResponseData = {
                "content": "Z5KNaR1xKweWGg2UUKPJ1xuppg7NE+MSeduoUrt7OlfofxEG6MA/EmFSVN1PN3TPOk2L9yWmtpwyxCPCrO77GhHZ889e7oTvXRVRM6F5vsQKIOuCY2NxFgqCt996rz1jCjP2nNkexi5UDgC+FYLGtAjsyR0bymB93y2TfMz/JK78PO47882bxE+2odl3BrIUD3QZ299aMMtsfirDcFmdgoS5Y72DGUHul1o6KCtP2AK7JmObE9oJUeodcspplF2PJ4Lg7s/kTgXxruU/fnfwUo5QrzTCmRaHK9QOBMTn4hxGXbNWXjhSB92e30K/E07zyLZBk8yfqSLlM6U94T/128M8i0Lnc9aCKIM7YmeSeHiZh+BlXM4O4gj8bk51PNNKsVeW7owo5TCBUkbyXLb6zA==",
                "key": "S5OUYtFrpKMZUGXPDE7TTT0HvZ0dCQpyA6DNznsWnUTMSFtkAXvRSPMWSve+z8Gsv4xOJJtgPLrAw5qeqly0HHgZpWyjDzOjahjeaJdkA7zoUtayySGX3yfUSCja1NtJOPGCn5OzJ6JJTg5OCfDdH0rj1KB06zHQ0YtsP5MTaMGSBj2ZkU4AkbHF3/MfpSuDnUYIevWg+uv3ywi3h5zBwyL8RjiqoVzGDy6pwLmp8o2Z+Gj6IJ8QYooItYNaQ7SG196vO8xfZd+sRm/RHen9dk4WqAEV8z6/FHkgvoGAVPrJVHLcNBEJee5irNIrQxeNP17UkOytbEvSxAkNXUPK4A==",
                "iv": "hihSPyR1EvP5$*!*"
            };

            return this.smartDecrypt(realResponseData);
        },

        // 运行所有测试
        runAllTests: function() {
            console.log("🚀 开始运行所有测试...\n");

            var testCases = [
                { data: "Hello World", version: "湖南版本" },
                { data: "测试中文字符", version: "湖南版本" },
                { data: {sCode: "SJS_CODE_12345", areaCode: "430100"}, version: "湖南版本" },
                { data: "43&business_handling_code&28.2282&112.9388&test_token&***********&APP001&/pages/index/index&mini", version: "湖南版本" },
                { data: "Hello World", version: "普通版本" },
                { data: {name: "张三", age: 25}, version: "普通版本" }
            ];

            var passCount = 0;
            var totalCount = testCases.length;

            for (var i = 0; i < testCases.length; i++) {
                console.log("\n" + "=".repeat(60));
                console.log("📋 测试用例 " + (i + 1) + "/" + totalCount + " (" + testCases[i].version + ")");

                if (this.testFullCycle(testCases[i].data, testCases[i].version)) {
                    passCount++;
                }
            }

            // 测试真实数据
            console.log("\n" + "=".repeat(60));
            console.log("📋 测试真实响应数据");
            var realResult = this.testRealData();
            if (realResult) {
                passCount++;
                totalCount++;
            } else {
                totalCount++;
            }

            console.log("\n" + "=".repeat(60));
            console.log("📊 测试总结:");
            console.log("✅ 通过: " + passCount + "/" + totalCount);
            console.log("❌ 失败: " + (totalCount - passCount) + "/" + totalCount);
            console.log("📈 成功率: " + Math.round(passCount / totalCount * 100) + "%");

            return passCount === totalCount;
        }
    };

    // 使用说明
    console.log("🎉 12580小程序完整加密解密工具已加载！");
    console.log("\n📖 使用方法:");
    console.log("1. CryptoTool.EncryptDataHn(data) - 湖南版本请求加密");
    console.log("2. CryptoTool.DecryptKeyHn(responseData) - 湖南版本响应解密");
    console.log("3. CryptoTool.EncryptData(data) - 普通版本请求加密");
    console.log("4. CryptoTool.DecryptKey(responseData) - 普通版本响应解密");
    console.log("5. CryptoTool.smartDecrypt(responseData) - 智能解密（自动检测版本）");
    console.log("6. CryptoTool.testRealData() - 测试你提供的真实数据");
    console.log("7. CryptoTool.runAllTests() - 运行所有测试用例");

    // 自动运行测试
    console.log("\n🔥 自动运行所有测试:");
    CryptoTool.runAllTests();

    // 导出模块
    module.exports = CryptoTool;
}
