<view bind:touchcancel="onTouchEnd" bind:touchend="onTouchEnd" bind:touchstart="onTouchStart" catch:touchmove="onTouchMove" class="van-picker-column custom-class" style="{{computed.rootStyle( {itemHeight:itemHeight,visibleItemCount:visibleItemCount} )}}">
    <view style="{{computed.wrapperStyle( {offset:offset,itemHeight:itemHeight,visibleItemCount:visibleItemCount,duration:duration} )}}">
        <view bindtap="onClickItem" class="van-ellipsis {{utils.bem( 'picker-column__item',{disabled:option&&option.disabled,selected:index===currentIndex} )}} {{index===currentIndex?'active-class':''}}" data-index="{{index}}" style="height:{{itemHeight}}px" wx:for="{{options}}" wx:for-item="option" wx:key="index">{{computed.optionText(option,valueKey)}}</view>
    </view>
</view>
<wxs module="utils" src="../wxs/utils.wxs" />
<wxs module="computed" src="index.wxs" />