var e, t = require("../../@babel/runtime/helpers/readOnlyError"), a = require("../../@babel/runtime/helpers/defineProperty"), n = require("../../utils/http.js"), o = require("../../utils/date"), i = require("../../utils/util.js"), c = i.unction, s = i.showModel, r = require("../../utils/transmission.js").rsaAesJs, l = getApp();

Page({
    data: (e = {
        bannerNotShow: !1,
        carHmList: {},
        currentOil: "",
        areaCode: "",
        currentTel: "",
        latitude: "",
        longitude: "",
        bannerUrls: [],
        currentMemberType: "2",
        isLoading: !1,
        closeScode: "",
        closeCouponType: "",
        memberTypeList: [],
        currentCardBackgroundUrl: "/static/imagehn/login-curve.png",
        loginStatus: "0",
        currentIndex: 0,
        tabActive: 0,
        couponList: {},
        secondZoneImg: "",
        seckillprefectureList: [],
        gridUrls: [],
        todayList: []
    }, a(a(a(a(a(a(a(a(a(a(e, "currentOil", "20"), "noticeList", []), "weatherMap", {}), "homeActivity", {}), "xuecheText", ""), "xuecheImg", ""), "xuecheLocation", ""), "indicatorDots", !0), "vertical", !1), "autoplay", !1), 
    a(a(a(a(a(a(a(a(a(a(e, "interval", 2e3), "duration", 500), "gxImgInfo", []), "couponList", []), "modelBurstZoneList", []), "modelSettingBasicList", []), "modelSettingBasicListTwo", []), "shows", !1), "selectDatas", []), "indexs", 0), 
    a(a(a(a(a(a(a(a(a(a(e, "indexCount", 0), "currentOilShow", !0), "showsSeckill", !1), "openPicker", !1), "multiIndex", [ 0, 0 ]), "multiIndexInit", [ 0, 0 ]), "multiArrayP", []), "multiArrayC", []), "isSuperVip", !1), "provinceVipList", []), 
    a(a(a(a(a(a(a(a(a(a(e, "isLoginTo", !1), "showRestOilDrop", "yes"), "provinceCityAndMemberMap", {}), "isOpenDialog1", !1), "provinceCodeStr1", ""), "settingZonListSize", 0), "topIsShow", !1), "avatarUrl", ""), "pathImg", n.baseUrlImg), "storeList", []), 
    a(a(a(a(e, "isShowOil", 0), "basicPrivilegeSettingList", []), "typeJump", null), "onlyOne", !1)),
    selectTaps: function() {
        this.setData({
            shows: !this.data.shows
        });
    },
    optionTaps: function(e) {
        var t = wx.getStorageSync("memberTypeList_hn"), a = e.currentTarget.dataset.index;
        wx.setStorageSync("userTypeIndex", a), t.length > 0 && (wx.setStorageSync("vipType_hn", t[a].vipType), 
        wx.setStorageSync("vipId_hn", t[a].codeId), wx.setStorageSync("feeAppCode_hn", t[a].feeAppCode), 
        this.setData({
            indexs: a,
            isShowOil: t[a].isShowOil,
            currentOil: t[a].restOilDrop,
            showRestOilDrop: t[a].isShowRestOilDrop,
            shows: !this.data.shows,
            isLoginTo: !0
        })), this.getTop(), this.getBotton();
    },
    optionTaps1: function() {
        var e = wx.getStorageSync("memberTypeList_hn");
        wx.setStorageSync("userTypeIndex", 0), e.length > 0 && (wx.setStorageSync("vipType_hn", e[0].vipType), 
        wx.setStorageSync("vipId_hn", e[0].codeId), wx.setStorageSync("feeAppCode_hn", e[0].feeAppCode), 
        console.log("*-*-*-*", e[0].isShowOil), this.setData({
            indexs: 0,
            isShowOil: e[0].isShowOil,
            currentOil: e[0].restOilDrop,
            showRestOilDrop: e[0].isShowRestOilDrop,
            isLoginTo: !0
        })), this.getBotton(), this.queryShop(), this.query();
    },
    onShareAppMessage: function() {},
    changeIndicatorDots: function() {
        this.setData({
            indicatorDots: !this.data.indicatorDots
        });
    },
    changeAutoplay: function() {
        this.setData({
            autoplay: !this.data.autoplay
        });
    },
    intervalChange: function(e) {
        this.setData({
            interval: e.detail.value
        });
    },
    durationChange: function(e) {
        this.setData({
            duration: e.detail.value
        });
    },
    onLoad: function() {
        console.log("onLoad-----"), wx.hideHomeButton(), wx.showShareMenu({
            withShareTicket: !0
        });
        var e = wx.getStorageSync("userTypeIndex");
        console.log("userTypeIndex", e);
        var t = wx.getStorageSync("latitude"), a = wx.getStorageSync("longitude"), n = wx.getStorageSync("isSuperVip"), o = wx.getStorageSync("provinceListVip"), i = wx.getStorageSync("isLoginTo"), c = wx.getStorageSync("avatarUrl");
        this.setData({
            indexs: e,
            latitude: t,
            longitude: a,
            isSuperVip: n,
            provinceVipList: o,
            isLoginTo: i,
            avatarUrl: c
        }), wx.setStorageSync("isLoginTo", !1);
        var s = this;
        wx.getStorage({
            key: "loginStatus_hn",
            success: function(e) {
                "0" == e.data || s.setData({
                    loginStatus: e.data
                });
            },
            fail: function() {
                "0" == s.data.loginStatus && (wx.setStorageSync("loginStatus_hn", "0"), s.setData({
                    currentCardBackgroundUrl: "/static/imagehn/login-curve.png"
                }));
            }
        }), s.initData(), "0" === wx.getStorageSync("loginStatus_hn") ? l.hcx.setProfile({
            user_type: "1",
            business_type: "HCZH",
            platform_type: "client_2",
            cp_name: "wz",
            page_type: "首页",
            province: wx.getStorageSync("provinceName"),
            city: wx.getStorageSync("cityName_hn")
        }) : "1" === wx.getStorageSync("loginStatus_hn") && void 0 !== wx.getStorageSync("memberTypeList_hn") && wx.getStorageSync("memberTypeList_hn").length > 0 ? l.hcx.setProfile({
            user_type: "3",
            phoneNumber: wx.getStorageSync("telephone_hn"),
            business_type: "HCZH",
            platform_type: "client_2",
            cp_name: "wz",
            page_type: "首页",
            province: wx.getStorageSync("provinceName"),
            city: wx.getStorageSync("cityName_hn")
        }) : l.hcx.setProfile({
            user_type: "2",
            phoneNumber: wx.getStorageSync("telephone_hn"),
            business_type: "HCZH",
            platform_type: "client_2",
            cp_name: "wz",
            page_type: "首页",
            province: wx.getStorageSync("provinceName"),
            city: wx.getStorageSync("cityName_hn")
        });
    },
    initData: function() {
        this.getTop(), this.getBotton();
        var e = wx.getStorageSync("urlStr");
        null != e && null != e && "" != e || this.getUrlStr("1");
    },
    onHide: function() {
        wx.setStorageSync("onHide", 1);
    },
    onShow: function() {
        this.onShowList(), this.getRestOilDrop();
        var e = wx.getStorageSync("avatarUrl"), t = wx.getStorageSync("feeAppCode_hn"), a = wx.getStorageSync("provinceCode");
        this.setData({
            avatarUrl: e,
            feeAppCode: t,
            provinceCode: a
        });
    },
    openActivityImg: function(e) {
        var t = wx.getStorageSync("loginStatus_hn"), a = wx.getStorageSync("feeAppCode_hn"), n = 0 === this.data.selectDatas.length || "1" !== t ? "nonmember_nowDataTime" : a + "_nowDataTime", i = wx.getStorageSync(n), c = (0, 
        o.getDayEndTimeAxis)(), s = new Date().getTime();
        (i && i > 0 && i < c || !i) && (wx.setStorageSync(n, s), this.setData({
            isOpenDialog1: !0,
            imgUrl1: e.adImgUrl,
            strUrl: e.miniUrl
        }));
    },
    openreal: function() {
        var e = wx.getStorageSync("latitude"), t = wx.getStorageSync("longitude"), a = wx.getStorageSync("token_hn"), o = wx.getStorageSync("provinceCode");
        "" == a && (a = ""), "" != o && null != o || (o = "");
        var i = "".concat(o, "&").concat("home_activity_code", "&").concat(e, "&").concat(t, "&").concat(a), c = r.EncryptDataHn(i);
        return new Promise(function(e, t) {
            (0, n.POSTHN)("/activityRulesInfo/selectTextContentByCode", c).then(function(a) {
                console.log("返回结果——————————————————————————", a), a.successful && a.data && a.data.url ? e(a.data) : t();
            }).catch(function() {
                t();
            });
        });
    },
    goToRecharge1: function() {
        var e = this.data.homeActivity, t = (e.adId, e.positionCode, e.miniUrl);
        t && (this.setData({
            isOpenDialog1: !1
        }), wx.navigateTo({
            url: t
        }));
    },
    closeOverlay: function() {
        this.setData({
            isOpenDialog1: !1
        });
    },
    onShowList: function() {
        var e = wx.getStorageSync("provinceCode");
        "43" == e && this.data.settingZonListSize > 0 && this.setData({
            LuckyDraw: !0
        }), this.setData({
            provinceCodeStr1: e
        }), l.getServeTime();
        var t = wx.getStorageSync("memberTypeList_hn"), a = wx.getStorageSync("loginStatus_hn"), n = wx.getStorageSync("telephone_hn"), o = wx.getStorageSync("cityCode_hn"), i = wx.getStorageSync("vipType_hn"), c = wx.getStorageSync("userTypeIndex"), s = wx.getStorageSync("isSuperVip"), r = wx.getStorageSync("provinceListVip");
        console.log("onShow-----", r), "1" == a ? (t.length > 0 ? this.setData({
            currentMemberType: i,
            isShowOil: t[c].isShowOil,
            currentOil: t[c].restOilDrop,
            showRestOilDrop: t[c].isShowRestOilDrop,
            selectDatas: t,
            currentOilShow: !0,
            indexs: c
        }) : this.setData({
            currentOilShow: !1,
            selectDatas: t,
            indexs: c
        }), this.setData({
            isSuperVip: s,
            provinceVipList: r,
            loginStatus: a,
            currentTel: n,
            areaCode: o
        })) : this.setData({
            loginStatus: "0",
            currentMemberType: "2",
            isSuperVip: s,
            provinceVipList: r,
            areaCode: o
        });
        var g = wx.getStorageSync("isLoginTo");
        this.setData({
            isLoginTo: g
        }), wx.setStorageSync("isLoginTo", !1), g && (console.log("isLoginToStr--getBotton---"), 
        this.getTop(), this.getBotton(), this.queryShop(), this.query());
    },
    toUrl: c(function(e) {
        var t = e.currentTarget.dataset.item, a = (t.adId, t.positionCode, t.miniUrl);
        if (null != a) if (-1 != a.indexOf("SJS_CODE")) {
            var o = wx.getStorageSync("cityCode_hn"), i = {
                sCode: a,
                areaCode: o
            }, c = JSON.stringify(i);
            console.log("-toUrl: unction--", c);
            var s = r.EncryptDataHn(c);
            (0, n.POSTHN)("/coupon/selectCodeDetailsBySCode", s).then(function(e) {
                console.log("劵详情", e);
                var t = e.data, n = t.couponCategory, o = t.couponDetailsType, i = t.isCar, c = t.sCodeName, s = t.isVipCoupon;
                1 == n ? wx.navigateTo({
                    url: "/packagehn/pages/couponDetailhn/couponDetail?sCode=".concat(a, "&couponType=").concat(o, "&isCar=").concat(i, "&isVipCoupon=").concat(s)
                }) : [ 2, 7, 9 ].indexOf(n) > -1 ? wx.navigateTo({
                    url: "/packagehn/pages/storeInfohn/storeInfo?sCode=".concat(a, "&couponType=").concat(o, "&codeName=").concat(c, "&isCar=").concat(i, "&couponCategory=").concat(n)
                }) : 3 == n ? wx.navigateTo({
                    url: "/packagehn/pages/learnDrivinghn/learnDriving?sCode=".concat(a, "&isNeedCar=").concat(i)
                }) : 4 == n ? wx.navigateTo({
                    url: "/packagehn/pages/storeInfohn/storeInfo?sCode=".concat(a, "&couponType=").concat(o, "&codeName=").concat(c, "&isCar=").concat(i, "&couponCategory=").concat(n)
                }) : 5 == n && wx.navigateTo({
                    url: "/packagehn/pages/couponDetailhn/couponDetail?sCode=".concat(a, "&couponType=").concat(o, "&isCar=").concat(i, "&isVipCoupon=").concat(s)
                });
            });
        } else {
            if ("1" == this.data.loginStatus && -1 != a.indexOf("/login")) return;
            if (-1 != a.indexOf("/businessHandlinghn/businessHandling")) {
                var l = wx.getStorageSync("latitude"), g = wx.getStorageSync("longitude"), p = wx.getStorageSync("token_hn"), u = wx.getStorageSync("telephone_hn"), d = wx.getStorageSync("feeAppCode_hn"), S = wx.getStorageSync("provinceCode");
                "" != p && null != p || (p = "*"), "" != u && null != u || (u = "*"), "" != d && null != d || (d = "*"), 
                "" != S && null != S || (S = "*");
                var h = "".concat(S, "&").concat("business_handling_code", "&").concat(l, "&").concat(g, "&").concat(p, "&").concat(u, "&").concat(d, "&", "/pages/index/index", "&", "mini");
                console.log(h);
                var y = r.EncryptDataHn(h);
                return void (0, n.POSTHN)("/activityRulesInfo/selectTextContentByCode", y).then(function(e) {
                    if (e.successful) {
                        wx.setStorageSync("selectTextContentByCode" + S, e);
                        var t = e.data.url, a = e.data.textContent;
                        wx.setStorageSync("urlStr", t), wx.setStorageSync("urlStrContent", a), null != a && "" != a && wx.showToast({
                            icon: "none",
                            title: a
                        });
                        setTimeout(function() {
                            "http" == t.substr(0, 4) && wx.navigateTo({
                                url: "/packagehn/pages/index/businessHandlinghn/businessHandling"
                            });
                        }, 1e3);
                    } else wx.setStorageSync("urlStr", ""), wx.setStorageSync("urlStrContent", ""), 
                    wx.setStorageSync("selectTextContentByCode" + S, "");
                });
            }
            wx.navigateTo({
                url: a
            });
        }
    }),
    handleSkipCoupon: c(function(e) {
        console.log("跳转至领券详情", e.currentTarget.dataset.item);
        var a = e.currentTarget.dataset.item, n = a.sCode, o = a.couponDetailsType, i = a.sCodeNameMini, c = a.isCar, r = a.isGiftBag, l = a.isVipCoupon, g = a.homeType, p = a.couponCategory, u = a.specialZoneTypeCode;
        if (this.setData({
            closeScode: n,
            closeCouponType: o
        }), this.setData({
            isLoading: !0
        }), "coupon" == g) 1 == p || 8 == p ? (null == c && t("isCar"), wx.navigateTo({
            url: "/packagehn/pages/couponDetailhn/couponDetail?sCode=".concat(n, "&couponType=").concat(o, "&isCar=").concat(c, "&isVipCoupon=").concat(l, "&couponCategoryType=").concat(p, "&isGiftBag=").concat(r)
        })) : [ 2, 7, 9 ].indexOf(p) > -1 ? wx.navigateTo({
            url: "/packagehn/pages/storeInfohn/storeInfo?sCode=".concat(n, "&couponType=").concat(o, "&codeName=").concat(i, "&isCar=").concat(c, "&couponCategory=").concat(p)
        }) : 3 == p ? wx.navigateTo({
            url: "/packagehn/pages/learnDrivinghn/learnDriving?sCode=".concat(n, "&isNeedCar=").concat(c)
        }) : 4 == p ? wx.navigateTo({
            url: "/packagehn/pages/storeInfohn/storeInfo?sCode=".concat(n, "&couponType=").concat(o, "&codeName=").concat(i, "&isCar=").concat(c, "&couponCategory=").concat(p)
        }) : 5 == p && (null == c && t("isCar"), wx.navigateTo({
            url: "/packagehn/pages/couponDetailhn/couponDetail?sCode=".concat(n, "&couponType=").concat(o, "&isCar=").concat(c, "&isVipCoupon=").concat(l, "&couponCategoryType=").concat(p, "&isGiftBag=").concat(r)
        })); else if ("product" == g) wx.navigateTo({
            url: "/packagehn/pages/LuckyDraw/shopList?zoneId=".concat(u, "&goodsCode=").concat(n)
        }); else if ("noBasic" == g) {
            if ("travel_privilege_code" == n) wx.navigateTo({
                url: "/packagehn/pages/sceniSpot/sceniSpot"
            }); else if ("go_out_insurance_code" == n) wx.navigateTo({
                url: "/packagehn/pages/index/travelInsurance/travelInsurance?isNeedCar=".concat(c)
            }); else if ("add_oil_discount_code" == n) wx.navigateTo({
                url: "/packagehn/pages/index/comeOnSpecial/comeOnSpecial"
            }); else if ("add_oil_discount_privately_code" == n) wx.navigateTo({
                url: "/package/pages/comeOnSpecialOld/comeOnSpecialOld"
            }); else if ("apply_nuoche_paste_code" == n) wx.navigateTo({
                url: "/packagehn/pages/index/carStickers/carStickers?isNeedCar=".concat(c)
            }); else if ("car_check_year_code" == n) wx.navigateTo({
                url: "/packagehn/pages/index/yearlyInspection/yearlyInspection?isNeedCar=".concat(c, "&serviceTel=").concat("4000125806")
            }); else if ("car_check_year_code_six" == n) wx.navigateTo({
                url: "/packagehn/pages/index/yearlyInspection/yearlyInspectionSix/yearlyInspectionSix?isNeedCar=".concat(c, "&serviceTel=").concat("4000125806")
            }); else if ("help_driver_car_code" == n) wx.navigateTo({
                url: "/packagehn/pages/index/scooter/scooter?isNeedCar=".concat(c, "&serviceTel=").concat("4000125806")
            }); else if ("law_assistance_code" == n) wx.navigateTo({
                url: "/packagehn/pages/index/legalAid/legalAid?isNeedCar=".concat(c, "&serviceTel=").concat("4000125806")
            }); else if ("road_rescue_code" == n) wx.navigateTo({
                url: "/packagehn/pages/index/roadRescue/roadRescue?isNeedCar=".concat(c, "&serviceTel=").concat("4000125806")
            }); else if ("business_handling_code" == n) this.getUrlStr("0"); else if ("weizhang_inquire_code" == n) {
                if ("0" == this.data.loginStatus) {
                    this.setData({
                        isLoading: !1
                    }), wx.showToast({
                        icon: "none",
                        title: "请您先登录再来领取权益吧！"
                    });
                    setTimeout(function() {
                        wx.navigateTo({
                            url: "/pages/login/login"
                        });
                    }, 1e3);
                    return;
                }
                if (wx.getStorageSync("memberTypeList_hn").length < 1) {
                    var d = wx.getStorageSync("urlStr");
                    if (null == d || null == d || "" == d) return this.setData({
                        isLoading: !1
                    }), !1;
                    var S = wx.getStorageSync("provinceCode");
                    return "32" != S && "43" != S && "64" != S && "63" != S || (this.setData({
                        isLoading: !1
                    }), s()), !1;
                }
                wx.navigateTo({
                    url: "/packagehn/pages/index/breakRuleshn/breakRules"
                });
            }
        } else if ("app_serve_merge" == g) {
            var h = e.currentTarget.dataset.item.accessAppParentId;
            wx.navigateTo({
                url: "/packagehn/pages/storeInfohn/storeInfo?sCode=".concat(n, "&couponType=").concat(o, "&codeName=").concat(i, "&isCar=").concat(c, "&accessAppParentId=").concat(h, "&couponCategory=").concat(p)
            });
        }
        this.setData({
            isLoading: !1
        });
    }),
    shop: function() {
        wx.navigateTo({
            url: "/packagehn/pages/LuckyDraw/shopList"
        });
    },
    navigatorToNotice: c(function() {
        wx.navigateTo({
            url: "/packagehn/pages/index/notice/notice"
        });
    }),
    getUrlStr: function(e) {
        var t = wx.getStorageSync("provinceCode"), a = wx.getStorageSync("selectTextContentByCode" + t);
        if (this.data.isLoginTo || null == a || "" == a) {
            var o = wx.getStorageSync("latitude"), i = wx.getStorageSync("longitude"), c = wx.getStorageSync("token_hn"), s = wx.getStorageSync("telephone_hn"), l = wx.getStorageSync("feeAppCode_hn");
            "" != c && null != c || (c = "*"), "" != s && null != s || (s = "*"), "" != l && null != l || (l = "*"), 
            "" != t && null != t || (t = "*");
            var g = "".concat(t, "&").concat("business_handling_code", "&").concat(o, "&").concat(i, "&").concat(c, "&").concat(s, "&").concat(l, "&", "/pages/index/index", "&", "mini");
            console.log(g);
            var p = r.EncryptDataHn(g);
            (0, n.POSTHN)("/activityRulesInfo/selectTextContentByCode", p).then(function(a) {
                if (a.successful) {
                    wx.setStorageSync("selectTextContentByCode" + t, a);
                    var n = a.data.url, o = a.data.textContent;
                    if (wx.setStorageSync("urlStr", n), wx.setStorageSync("urlStrContent", o), null != o && "" != o && wx.showToast({
                        icon: "none",
                        title: o
                    }), "0" == e) setTimeout(function() {
                        "http" == n.substr(0, 4) ? wx.navigateTo({
                            url: "/packagehn/pages/index/businessHandlinghn/businessHandling"
                        }) : wx.switchTab({
                            url: "/pages/cardRules/cardRules"
                        });
                    }, 1e3);
                } else wx.setStorageSync("urlStr", ""), wx.setStorageSync("urlStrContent", ""), 
                wx.setStorageSync("selectTextContentByCode" + t, "");
            });
        } else {
            console.log("onLoad-6666---");
            var u = a.data.url, d = a.data.textContent;
            null != d && "" != d && wx.showToast({
                icon: "none",
                title: d
            });
            setTimeout(function() {
                "http" == u.substr(0, 4) ? wx.navigateTo({
                    url: "/packagehn/pages/index/businessHandlinghn/businessHandling"
                }) : wx.switchTab({
                    url: "/pages/cardRules/cardRules"
                });
            }, 1e3);
        }
    },
    getRestOilDrop: function() {
        var e = this, t = wx.getStorageSync("provinceCode"), a = wx.getStorageSync("token_hn"), o = wx.getStorageSync("memberTypeList_hn"), i = wx.getStorageSync("feeAppCode_hn"), c = {
            token: a,
            provinceId: t
        }, s = JSON.stringify(c), l = r.EncryptDataHn(s);
        (0, n.POSTHN)("/newUser/getRestOilDrop", l).then(function(t) {
            if (t.successful) {
                console.log("---res--", t), wx.setStorageSync("getRestOilDrop", "0");
                var a = t.data;
                if (null != a && a.length > 0) {
                    for (var n = 0, c = 0; c < a.length; c++) a[c].feeAppCode == i && (n = a[c].restOilDrop, 
                    e.setData({
                        currentOil: a[c].restOilDrop
                    }));
                    for (var s = 0; s < o.length; s++) o[s].feeAppCode == i && (o[s].restOilDrop = n, 
                    wx.setStorageSync("memberTypeList_hn", o));
                }
            }
        });
    },
    getTop: function() {
        var e = this, t = wx.getStorageSync("provinceCode"), a = wx.getStorageSync("getTopList" + t);
        if (this.data.isLoginTo || null == a || "" == a) {
            var o = wx.getStorageSync("cityCode_hn"), i = wx.getStorageSync("feeAppCode_hn"), c = wx.getStorageSync("token_hn");
            "" != c && null != c || (c = "*"), "" != t && null != t || (t = "*"), "" != o && null != o || (o = "*"), 
            "" != i && null != i || (i = "*"), this.setData({
                isLoading: !0
            });
            var s = {
                token: c,
                latitude: this.data.latitude,
                longitude: this.data.longitude,
                provinceId: t,
                memberGradeCode: i,
                cityId: o,
                pageNo: 1,
                pageSize: 20
            };
            console.log("noticeBannerWeatherLocation-query--", s);
            var l = JSON.stringify(s), g = r.EncryptDataHn(l);
            (0, n.POSTHN)("/homeApi/noticeBannerWeatherLocation", g).then(function(a) {
                if (e.setData({
                    topIsShow: !0
                }), a.successful) {
                    var n = a.data, o = n.bannerList, i = n.homeActivity, c = n.noticeWeatherMap;
                    console.log("noticeBannerWeatherLocation---", a), wx.setStorageSync("getTopList" + t, a), 
                    i && (e.setData({
                        homeActivity: i
                    }), e.openActivityImg(i)), Array.isArray(o) && o.length > 0 ? e.setData({
                        bannerUrls: o
                    }) : e.setData({
                        bannerNotShow: !0,
                        bannerUrls: []
                    }), c ? (e.setData({
                        noticeList: c.list,
                        weatherMap: c.weatherMap
                    }), e.getStr(c.weatherMap)) : e.setData({
                        bannerNotShow: !0,
                        bannerUrls: []
                    });
                } else e.setData({
                    bannerNotShow: !0,
                    bannerUrls: []
                });
            }).catch(function(t) {
                e.setData({
                    isLoading: !1
                });
            });
        } else {
            var p = a.data.noticeWeatherMap, u = a.data.bannerList;
            null != u && null != u && (0 != u.length ? e.setData({
                bannerUrls: u
            }) : e.setData({
                bannerNotShow: !0,
                bannerUrls: []
            })), null != p && null != p && (e.setData({
                noticeList: p.list,
                weatherMap: p.weatherMap
            }), e.getStr(p.weatherMap)), e.setData({
                topIsShow: !0
            });
        }
    },
    getBotton: function() {
        var e = wx.getStorageSync("feeAppCode_hn"), t = wx.getStorageSync("provinceCode");
        this.setData({
            modelSettingBasicListTwo: [],
            modelSettingBasicList: [],
            feeAppCode: e,
            provinceCode: t,
            storeList: []
        });
        var a = this, o = wx.getStorageSync("provinceCode"), i = wx.getStorageSync("getBotton" + o);
        if (this.data.isLoginTo || null == i || "" == i) {
            var c = wx.getStorageSync("cityCode_hn"), s = wx.getStorageSync("feeAppCode_hn"), l = wx.getStorageSync("token_hn");
            "" != l && null != l || (l = "*"), "" != o && null != o || (o = "*"), "" != c && null != c || (c = "*"), 
            "" != s && null != s || (s = "*");
            var g = {
                token: l,
                latitude: this.data.latitude,
                longitude: this.data.longitude,
                provinceId: o,
                memberGradeCode: s,
                source: "mini",
                cityId: c
            };
            console.log("couponSpecialZoneAndPrivilegeSetting---query", g), a.setData({
                isLoading: !0
            });
            var p = JSON.stringify(g), u = r.EncryptDataHn(p);
            console.log("couponSpecialZoneAndPrivilegeSetting---str券专区", g), (0, n.POSTHN)("/homeApi/couponSpecialZoneAndPrivilegeSetting", u).then(function(e) {
                if (a.setData({
                    isLoading: !1
                }), e.successful) {
                    console.log("couponSpecialZoneAndPrivilegeSetting---券专区", e), wx.setStorageSync("getBotton" + o, 1);
                    var t = e.data.homePrivilegeModelList, n = e.data.basicPrivilegeSettingList, i = e.data.homeSecondZoneGoodsList, c = e.data.couponSpecialZoneMap, s = e.data.popularize;
                    if (null != s && null != s && s != {} ? (wx.setStorageSync("privilegeSettingImg" + o, s), 
                    a.setData({
                        bannerBox: s,
                        typeJump: s.jumpMode
                    })) : (wx.setStorageSync("privilegeSettingImg" + o, void 0), a.setData({
                        bannerBox: {}
                    })), null != t && null != t && t.length > 0) wx.setStorageSync("privilegeSetting" + o, t), 
                    t.forEach(function(e, t) {
                        "model_setting_activity_zone" == e.modelCode ? (a.setData({
                            settingZonListSize: e.settingZonList.length
                        }), e.settingZonList.length > 0 ? e.settingZonList.forEach(function(e, t) {
                            "lucky_turntable_code" == e.activityTypeCode && "43" == o ? a.setData({
                                LuckyDraw: !0
                            }) : a.setData({
                                LuckyDraw: !1
                            });
                        }) : a.setData({
                            LuckyDraw: !1
                        })) : a.setData({
                            LuckyDraw: !1
                        });
                    }); else a.setData({
                        LuckyDraw: !1
                    });
                    if (null != n && null != n) {
                        wx.setStorageSync("selectBasicPrivilegeSetting" + o, n);
                        var r = [];
                        n.forEach(function(e, t) {
                            var a = Math.floor(t / 4);
                            r[a] || (r[a] = []), r[a].push(e);
                        }), a.setData({
                            gridUrls: r
                        });
                    }
                    if (null != i && null != i) {
                        wx.setStorageSync("selectHomeSecondZoneGoodsList" + o, i);
                        var l = i, g = [];
                        if (null != l && null != l && "" != l && l.length > 0) {
                            for (var p = function() {
                                var e = {}, t = [], n = l[u].activitySCodeList;
                                a.setData({
                                    showsSeckill: n.length > 0
                                }), n.forEach(function(e, a) {
                                    var n = Math.floor(a / 3);
                                    t[n] || (t[n] = []), t[n].push(e);
                                }), e.iconUrl = l[u].iconUrl, e.activitySCodeList = t, g.push(e);
                            }, u = 0; u < l.length; u++) p();
                            a.setData({
                                seckillprefectureList: g,
                                secondZoneImg: e.data.secondZoneImg
                            });
                        }
                    }
                    if (null != c && null != c) {
                        wx.setStorageSync("selectCouponSpeicalZone" + o, c);
                        var d = c.modelSettingBasicList, S = [], h = 0;
                        if (a.setData({
                            provinceCityAndMemberMap: c.provinceCityAndMemberMap
                        }), null != d) for (var y = 0; y < d.length; y++) {
                            var x = d[y].specialZoneName;
                            h += d[y].activitySCodeList.length, d[y].specialZoneName1 = x.split("_")[0], d[y].specialZoneName2 = x.split("_")[1], 
                            d[y].activitySCodeList.length > 0 && (d[y].specialZoneNameCount = d[y].activitySCodeList[0].length), 
                            "notice_extend" == d[y].specialZoneTypeCode && (S = d[y], console.log("---树叶---", S), 
                            d[y].activitySCodeList.length > 0 && d[y].activitySCodeList[0][0].couponDetailsType);
                        }
                        var v = c.modelBurstZoneList;
                        if (null != v) for (var w = 0; w < v.length; w++) {
                            v[w].specialZoneName;
                            h += v[w].activitySCodeList.length, v[w].specialZoneNameCount = v[w].activitySCodeList.length;
                        } else v = [];
                        a.setData({
                            modelSettingBasicListTwo: S,
                            modelBurstZoneList: v,
                            modelSettingBasicList: d,
                            indexCount: h,
                            basicPrivilegeSettingList: n
                        });
                    }
                }
            }).catch(function(e) {
                a.setData({
                    isLoading: !1
                });
            });
        } else {
            var d = wx.getStorageSync("privilegeSetting" + o), S = wx.getStorageSync("selectBasicPrivilegeSetting" + o), h = wx.getStorageSync("selectHomeSecondZoneGoodsList" + o), y = wx.getStorageSync("selectCouponSpeicalZone" + o), x = wx.getStorageSync("privilegeSettingImg" + o);
            if (null != d && null != d && d.length > 0) d.forEach(function(e, t) {
                "model_setting_activity_zone" == e.modelCode ? (a.setData({
                    settingZonListSize: e.settingZonList.length
                }), e.settingZonList.length > 0 ? e.settingZonList.forEach(function(e, t) {
                    "lucky_turntable_code" == e.activityTypeCode && "43" == o ? a.setData({
                        LuckyDraw: !0
                    }) : a.setData({
                        LuckyDraw: !1
                    });
                }) : a.setData({
                    LuckyDraw: !1
                })) : a.setData({
                    LuckyDraw: !1
                });
            });
            if (null != S && null != S) {
                var v = [];
                S.forEach(function(e, t) {
                    var a = Math.floor(t / 4);
                    v[a] || (v[a] = []), v[a].push(e);
                }), a.setData({
                    gridUrls: v
                });
            }
            if (null != h && null != h) {
                var w = h, C = [];
                if (null != w && null != w && "" != w && w.length > 0) {
                    for (var f = function() {
                        var e = {}, t = [], n = w[T].activitySCodeList;
                        a.setData({
                            showsSeckill: n.length > 0
                        }), n.forEach(function(e, a) {
                            var n = Math.floor(a / 3);
                            t[n] || (t[n] = []), t[n].push(e);
                        }), e.iconUrl = w[T].iconUrl, e.activitySCodeList = t, C.push(e);
                    }, T = 0; T < w.length; T++) f();
                    a.setData({
                        seckillprefectureList: C,
                        secondZoneImg: w.secondZoneImg
                    });
                }
            }
            if (null != y && null != y) {
                var m = y.modelSettingBasicList, _ = [];
                a.setData({
                    provinceCityAndMemberMap: y.provinceCityAndMemberMap
                });
                var D = 0;
                if (null != m) for (var L = 0; L < m.length; L++) {
                    var k = m[L].specialZoneName;
                    D += m[L].activitySCodeList.length, m[L].specialZoneName1 = k.split("_")[0], m[L].specialZoneName2 = k.split("_")[1], 
                    m[L].activitySCodeList.length > 0 && (m[L].specialZoneNameCount = m[L].activitySCodeList[0].length), 
                    "notice_extend" == m[L].specialZoneTypeCode && (_ = m[L], m[L].activitySCodeList.length > 0 && m[L].activitySCodeList[0][0].couponDetailsType);
                }
                var I = y.modelBurstZoneList;
                if (null != I) for (var b = 0; b < I.length; b++) {
                    I[b].specialZoneName;
                    D += I[b].activitySCodeList.length, I[b].specialZoneNameCount = I[b].activitySCodeList.length;
                } else I = [];
                console.log("我是缓存进这里,", S), a.setData({
                    modelSettingBasicListTwo: _,
                    modelBurstZoneList: I,
                    modelSettingBasicList: m,
                    indexCount: D,
                    bannerBox: x,
                    basicPrivilegeSettingList: S,
                    typeJump: S
                });
            }
        }
    },
    getStr: function(e) {
        1 == e.washIndex ? this.setData({
            xuecheText: "适宜洗车",
            xuecheLocation: "/static/imagehn/location-hn.png",
            xuecheImg: "/static/image/icon_xiche_1.png"
        }) : 2 == e.washIndex ? this.setData({
            xuecheText: "较适宜洗车",
            xuecheLocation: "/static/imagehn/location-hn.png",
            xuecheImg: "/static/image/icon_xiche_2.png"
        }) : 3 == e.washIndex ? this.setData({
            xuecheText: "较不适宜洗车",
            xuecheLocation: "/static/imagehn/location-hn.png",
            xuecheImg: "/static/image/icon_xiche_3.png"
        }) : 4 == e.washIndex && this.setData({
            xuecheText: "不适宜洗车",
            xuecheLocation: "/static/imagehn/location-hn.png",
            xuecheImg: "/static/image/icon_xiche_4.png"
        });
    },
    handleTapSingleNotice: function(e) {
        var t = e.currentTarget.dataset.id;
        wx.navigateTo({
            url: "/packagehn/pages/index/notice/notice?id=item".concat(t)
        });
    },
    makePhoneCall: function() {
        console.log(this.data.bannerBox);
        var e = this.data.bannerBox.jumpMode;
        if (0 == e) wx.navigateTo({
            url: "/packagehn/pages/index/businessHandlinghn/businessHandling"
        }); else if (1 == e) wx.makePhoneCall({
            phoneNumber: this.data.bannerBox.jumpUrl
        }); else if (2 == e) {
            var t = this.data.bannerBox.jumpUrl;
            if (null != t) if (-1 != t.indexOf("SJS_CODE")) {
                var a = wx.getStorageSync("cityCode_hn"), o = {
                    sCode: t,
                    areaCode: a
                }, i = JSON.stringify(o);
                console.log("-toUrl: unction--", i);
                var c = r.EncryptDataHn(i);
                (0, n.POSTHN)("/coupon/selectCodeDetailsBySCode", c).then(function(e) {
                    if (console.log("劵详情", e), e.successful) {
                        var a = e.data, n = a.couponCategory, o = a.couponDetailsType, i = a.isCar, c = a.sCodeName, s = a.isVipCoupon;
                        1 == n ? wx.navigateTo({
                            url: "/packagehn/pages/couponDetailhn/couponDetail?sCode=".concat(t, "&couponType=").concat(o, "&isCar=").concat(i, "&isVipCoupon=").concat(s)
                        }) : 2 == n || 9 == n ? wx.navigateTo({
                            url: "/packagehn/pages/storeInfohn/storeInfo?sCode=".concat(t, "&couponType=").concat(o, "&codeName=").concat(c, "&isCar=").concat(i, "&couponCategory=").concat(n)
                        }) : 3 == n ? wx.navigateTo({
                            url: "/packagehn/pages/learnDrivinghn/learnDriving?sCode=".concat(t, "&isNeedCar=").concat(i)
                        }) : 4 == n ? wx.navigateTo({
                            url: "/packagehn/pages/storeInfohn/storeInfo?sCode=".concat(t, "&couponType=").concat(o, "&codeName=").concat(c, "&isCar=").concat(i, "&couponCategory=").concat(n)
                        }) : 5 == n && wx.navigateTo({
                            url: "/packagehn/pages/couponDetailhn/couponDetail?sCode=".concat(t, "&couponType=").concat(o, "&isCar=").concat(i, "&isVipCoupon=").concat(s)
                        });
                    } else wx.showToast({
                        icon: "none",
                        title: e.resultMsg
                    });
                });
            } else {
                var s = t;
                wx.navigateTo({
                    url: s
                });
            }
        } else if (3 == e) {
            var l = this.data.bannerBox.jumpUrl.toString();
            wx.navigateTo({
                url: l
            });
        } else 4 == e || 5 == e && wx.navigateToMiniProgram({
            appId: "wx1f1ea04b716771be",
            path: "pages/index/index",
            envVersion: "release",
            success: function(e) {}
        });
    },
    handNav: c(function(e) {
        var t = e.currentTarget.dataset.item, a = t.isNeedCar, n = t.serviceTel;
        if (1 != t.isLookDetails || "0" != this.data.loginStatus) if ("weizhang_inquire_code" == t.privilegeCode) {
            if (wx.getStorageSync("memberTypeList_hn").length < 1) {
                var o = wx.getStorageSync("urlStr");
                if (null == o || null == o || "" == o) return !1;
                var i = wx.getStorageSync("provinceCode");
                return "32" != i && "43" != i && "64" != i && "63" != i || s(), !1;
            }
            wx.navigateTo({
                url: "/packagehn/pages/index/breakRuleshn/breakRules"
            });
        } else "add_oil_discount_code" == t.privilegeCode ? wx.navigateTo({
            url: "/packagehn/pages/index/comeOnSpecial/comeOnSpecial"
        }) : "add_oil_discount_privately_code" == t.privilegeCode ? wx.navigateTo({
            url: "/package/pages/comeOnSpecialOld/comeOnSpecialOld"
        }) : "business_handling_code_details" == t.privilegeCode ? wx.switchTab({
            url: "/pages/cardRules/cardRules"
        }) : "car_check_year_code" == t.privilegeCode ? wx.navigateTo({
            url: "/packagehn/pages/index/yearlyInspection/yearlyInspection?isNeedCar=".concat(a, "&serviceTel=").concat(n)
        }) : "car_check_year_code_six" == t.privilegeCode ? wx.navigateTo({
            url: "/packagehn/pages/index/yearlyInspection/yearlyInspectionSix/yearlyInspectionSix?isNeedCar=".concat(a, "&serviceTel=").concat(n)
        }) : "apply_nuoche_paste_code" == t.privilegeCode ? wx.navigateTo({
            url: "/packagehn/pages/index/carStickers/carStickers?isNeedCar=".concat(a)
        }) : "go_out_insurance_code" == t.privilegeCode ? wx.navigateTo({
            url: "/packagehn/pages/index/travelInsurance/travelInsurance?isNeedCar=".concat(a)
        }) : "road_rescue_code" == t.privilegeCode ? wx.navigateTo({
            url: "/packagehn/pages/index/roadRescue/roadRescue?isNeedCar=".concat(a, "&serviceTel=").concat(n)
        }) : "law_assistance_code" == t.privilegeCode ? wx.navigateTo({
            url: "/packagehn/pages/index/legalAid/legalAid?isNeedCar=".concat(a, "&serviceTel=").concat(n)
        }) : "help_driver_car_code" == t.privilegeCode ? wx.navigateTo({
            url: "/packagehn/pages/index/scooter/scooter?isNeedCar=".concat(a, "&serviceTel=").concat(n)
        }) : "travel_privilege_code" == t.privilegeCode ? wx.navigateTo({
            url: "/packagehn/pages/sceniSpot/sceniSpot?isNeedCar=".concat(a)
        }) : "business_handling_code" == t.privilegeCode ? this.getUrlStr("0") : "repair_tyre_code" == t.privilegeCode && wx.navigateTo({
            url: "/packagehn/pages/index/repairTyre/repairTyre?isNeedCar=".concat(a, "&serviceTel=").concat(n)
        }); else {
            wx.showToast({
                icon: "none",
                title: "请您先登录再来领取权益吧！"
            });
            setTimeout(function() {
                wx.navigateTo({
                    url: "/pages/login/login"
                });
            }, 1e3);
        }
    }),
    couponRule: function(e) {
        var t = e.currentTarget.dataset.item.specialZoneTypeCode;
        wx.navigateTo({
            url: "/packagehn/pages/businessDetails/businessDetails?code=" + t
        });
    },
    handleLogin: c(function() {
        wx.navigateTo({
            url: "/pages/login/login"
        });
    }),
    handleGetCardRules: function() {
        wx.navigateTo({
            url: "./cardRules/cardRules"
        });
    },
    handleDefaultBusiness: c(function() {
        wx.navigateTo({
            url: "./cardRules/cardRules"
        });
    }),
    viewRules: c(function() {
        wx.navigateTo({
            url: "/pages/rules/rules"
        });
    }),
    errorImg: function(e) {
        var t = e.currentTarget.dataset.outindex, n = e.currentTarget.dataset.index, o = "couponList.ActivityCouponList[".concat(t, "][").concat(n, "].couponIconUrl");
        this.setData(a({}, o, "/static/img/coupon-err.png"));
    },
    errorImg1: function(e) {
        var t = e.currentTarget.dataset.outindex, n = e.currentTarget.dataset.index, o = "couponList.vipCouponList[".concat(t, "][").concat(n, "].couponIconUrl");
        this.setData(a({}, o, "/static/img/coupon-err.png"));
    },
    jfhidden: function() {
        this.setData({
            LuckyDraw: !1
        });
    },
    jfgoinDraw: function() {
        wx.navigateTo({
            url: "/packagehn/pages/LuckyDraw/LuckyDraw"
        });
    },
    bindMultiPickerChange: function(e) {
        var t = this.data.provinceVipList;
        this.setData({
            multiIndex: e.detail.value,
            multiArrayC: t[e.detail.value[0]].cityList
        });
    },
    bindtapCity: function() {
        for (var e = this.data.provinceVipList, t = wx.getStorageSync("provinceCode"), a = wx.getStorageSync("cityCode_hn"), n = 0, o = 0, i = 0; i < e.length; i++) if (t == e[i].provinceId) {
            n = i;
            for (var c = e[i].cityList, s = 0; s < c.length; s++) a == c[s].areaCode && (o = s);
        }
        this.setData({
            multiIndex: [ n, o ],
            multiIndexInit: [ n, o ]
        }), wx.pageScrollTo({
            duration: 800,
            scrollTop: 0
        }), this.data.isSuperVip && this.setData({
            openPicker: !0,
            multiArrayP: e,
            multiArrayC: e[n].cityList
        });
    },
    pickerCancel: function() {
        this.setData({
            openPicker: !1
        });
    },
    pickerConfirm: function() {
        console.log("pickerConfirm--"), this.setData({
            isLoginTo: !0
        }), wx.setStorageSync("isLoginTo", !1);
        var e = this;
        setTimeout(function() {
            e.setData({
                openPicker: !1
            });
            var t = wx.getStorageSync("provinceCode"), a = wx.getStorageSync("cityCode_hn"), o = e.data.multiArrayP[e.data.multiIndex[0]].provinceId, i = e.data.multiArrayC[e.data.multiIndex[1]].areaCode, c = e.data.multiArrayC[e.data.multiIndex[1]].areaName;
            if (o == t) console.log("pickerConfirm-城市-", i), i != a && (wx.setStorageSync("cityCode_hn", i), 
            wx.setStorageSync("cityName_hn", c), wx.setStorageSync("selectHomeSecondZoneGoodsList" + t, ""), 
            wx.setStorageSync("selectCouponSpeicalZone" + t, ""), wx.setStorageSync("selectAllNoticeList" + t, ""), 
            e.getTop(), e.getBotton(), e.queryShop(), e.query()); else {
                console.log("pickerConfirm-省份-", o), console.log("pickerConfirm-城市-", i);
                var s = wx.getStorageSync("token_hn"), l = "".concat(s, "&").concat(o, "&").concat(i), g = r.EncryptDataHn(l);
                e.setData({
                    isLoading: !0
                }), (0, n.POSTHN)("/newUser/selectUserInfoByToken", g).then(function(t) {
                    if (console.log("切换省份，每次更新至最新--", t), t) if (t.successful) {
                        wx.setStorageSync("cityCode_hn", i), wx.setStorageSync("cityName_hn", c), wx.setStorageSync("isSuperVip", t.data.isSuperVip), 
                        wx.setStorageSync("provinceListVip", t.data.provinceList), wx.setStorageSync("userOutLogin", "0"), 
                        wx.setStorageSync("provinceName", t.data.provinceName), wx.setStorageSync("provinceCode", t.data.provinceCode), 
                        wx.setStorageSync("provinceCenter", t.data.provinceCenter), wx.setStorageSync("memberTypeList_hn", t.data.userCodeList);
                        t.data.userCodeList;
                        wx.setStorageSync("userTypeIndex", 0), wx.setStorageSync("vipType_hn", t.data.userCodeList[0].vipType), 
                        wx.setStorageSync("vipId_hn", t.data.userCodeList[0].codeId), wx.setStorageSync("feeAppCode_hn", t.data.userCodeList[0].feeAppCode), 
                        wx.setStorageSync("token_hn", t.data.token), wx.setStorageSync("telephone_hn", t.data.telephone), 
                        wx.setStorageSync("defaultCityName", t.data.defaultCityName), wx.setStorageSync("defaultCityId", t.data.defaultCityId), 
                        e.onShow(), e.optionTaps1(), e.getTop();
                    } else wx.setStorageSync("loginStatus_hn", "0"), wx.setStorageSync("token_hn", ""), 
                    wx.setStorageSync("userTypeIndex", 0), wx.setStorageSync("provinceCity_hn", ""), 
                    wx.setStorageSync("cityCode_hn", "*"), wx.setStorageSync("isSuperVip", !1), wx.setStorageSync("provinceListVip", []), 
                    e.onShow(), e.getTop(), e.optionTaps1();
                });
            }
        }, 1e3);
    },
    onPageScroll: function(e) {
        this.data.openPicker && (e.scrollTop > 15 ? this.setData({
            openPicker: !1
        }) : wx.pageScrollTo({
            duration: 800,
            scrollTop: 0
        }));
    },
    getStoreList: function(e, t) {
        var a = this, o = wx.getStorageSync("provinceCode");
        if (("43" == o || "32" == o) && 1 != this.data.onlyOne) {
            this.setData({
                onlyOne: !0
            });
            var i, c, s = wx.getStorageSync("cityCode_hn"), l = wx.getStorageSync("token_hn"), g = wx.getStorageSync("feeAppCode_hn");
            null == e ? ("43" == o && (i = "SJS_CODE_21041414350929023524078", c = 25), "32" == o && (i = "SJS_CODE_21042918302088128467433", 
            c = 17)) : (i = e, c = t);
            var p = {
                token: l,
                pageNo: 1,
                pageNoTwo: 1,
                pageSize: 5,
                cityCode: s,
                locationCode: "",
                latitude: this.data.latitude,
                longitude: this.data.longitude,
                sCode: i,
                mdAddress: "",
                provinceId: o,
                belongingVip: g,
                areaCode: "",
                userCityCode: s,
                accessAppParentId: c + ""
            }, u = JSON.stringify(p), d = r.EncryptDataHn(u);
            (0, n.POSTHN)("/washingCoupon/selectMergeWashingCarStoreListThree", d).then(function(e) {
                e.successful && a.setData({
                    storeList: e.data.list,
                    onlyOne: !1
                });
            });
        }
    },
    Calling: function() {},
    callStroe: function(e) {
        var t = e.currentTarget.dataset.tel;
        wx.makePhoneCall({
            phoneNumber: t
        });
    },
    handleGo: function(e) {
        var t = Number(e.currentTarget.dataset.lbsx), a = Number(e.currentTarget.dataset.lbsy), n = e.currentTarget.dataset.name;
        if (!e.currentTarget.dataset.lbsx || !e.currentTarget.dataset.lbsy) return wx.showToast({
            title: "暂未获取到当前门店位置信息",
            icon: "none"
        }), !1;
        wx.openLocation({
            latitude: t,
            longitude: a,
            name: n
        });
    },
    goSpecialArea: function(e) {
        console.log("eeee", e);
        var t = e.currentTarget.dataset.codeid, a = e.currentTarget.dataset.style, n = e.currentTarget.dataset.name, o = e.currentTarget.dataset.miniurl;
        2 == e.currentTarget.dataset.styletype ? wx.navigateTo({
            url: o
        }) : wx.navigateTo({
            url: "/package/pages/SpecialArea/SpecialArea?id=" + t + "&style=" + a + "&name=" + n
        });
    },
    storeshoplist: function(e) {
        var t = e.detail;
        wx.navigateTo({
            url: "/package/pages/newShopDetail/newShopDetail?id=" + t
        });
    },
    gomustShop: function(e) {
        var t = e.currentTarget.dataset.type, a = e.currentTarget.dataset.id;
        0 == t && wx.navigateTo({
            url: "/package/pages/newshopList/newshopList?appid=" + a
        });
    },
    gomust: function() {
        wx.navigateTo({
            url: "/package/pages/information/information"
        });
    },
    godetail: function(e) {
        var t = e.currentTarget.dataset.id;
        wx.navigateTo({
            url: "/package/pages/informationDetail/informationDetail?id=" + t
        });
    },
    onJumpGuaZi: function() {
        wx.navigateToMiniProgram({
            appId: "wx73c672777c484f48",
            path: "",
            envVersion: "release",
            success: function(e) {
                console.log(e);
            }
        });
    },
    onJumpWeiBao: function() {
        wx.navigateToMiniProgram({
            appId: "wx06a561655ab8f5b2",
            path: "pages/insure/carReinsure/main/newHomeV2/index",
            envVersion: "release",
            success: function(e) {
                console.log(e);
            }
        });
    },
    onJump4S: function() {
        wx.navigateTo({
            url: "/packagehn/pages/index/yearlyInspection/inspectionStation/inspectionStation?mdType=MD_4S"
        });
    }
});