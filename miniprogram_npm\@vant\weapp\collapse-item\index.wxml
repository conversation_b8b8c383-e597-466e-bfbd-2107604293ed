<view class="van-collapse-item custom-class {{index!==0?'van-hairline--top':''}}">
    <van-cell bind:click="onClick" border="{{border&&expanded}}" class="{{utils.bem( 'collapse-item__title',{disabled:disabled,expanded:expanded} )}}" clickable="{{clickable}}" customClass="van-cell" hoverClass="van-cell--hover" icon="{{icon}}" isLink="{{isLink}}" label="{{label}}" rightIconClass="van-cell__right-icon" size="{{size}}" title="{{title}}" titleClass="title-class" value="{{value}}">
        <slot name="title" slot="title"></slot>
        <slot name="icon" slot="icon"></slot>
        <slot name="value"></slot>
        <slot name="right-icon" slot="right-icon"></slot>
    </van-cell>
    <view animation="{{animation}}" class="{{utils.bem('collapse-item__wrapper')}}" style="height:0;">
        <view class="van-collapse-item__content content-class">
            <slot></slot>
        </view>
    </view>
</view>
<wxs module="utils" src="../wxs/utils.wxs" />