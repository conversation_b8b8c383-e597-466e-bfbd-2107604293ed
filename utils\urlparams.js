Object.defineProperty(exports, "__esModule", {
    value: !0
}), exports.ObjTree = n, exports.ObjTreePaths = i, exports.URLParams = u, exports._typeof = void 0, 
exports.isArr = o, exports.isOriginObject = t, exports.urlAddParams = function(r, e, t) {
    var o = u(e, t);
    if (r.indexOf("?") > -1) {
        var n = r.split("?");
        return o ? r.replace(/&$/, "") + (n[1] ? "&" : "") + o : r;
    }
    return o ? r + "?" + o : r;
};

var r = require("../@babel/runtime/helpers/typeof"), e = exports._typeof = "function" == typeof Symbol && "symbol" === r(Symbol.iterator) ? function(e) {
    return r(e);
} : function(e) {
    return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : r(e);
};

function t(r) {
    return r.constructor === Date || "function" == typeof r;
}

function o(r) {
    return "[object Array]" === Object.prototype.toString.call(r);
}

function n(r) {
    var o = [];
    for (var i in r) t(r[i]) || ("object" === e(r[i]) ? o.push({
        label: i,
        child: n(r[i])
    }) : o.push({
        label: i,
        val: r[i]
    }));
    return o;
}

function i(r) {
    var e = [];
    return function r(t, o) {
        for (var n in o = o || "", t) t[n].child ? r(t[n].child, o ? o + "[" + t[n].label + "]" : t[n].label) : o ? e.push(o + "[" + t[n].label + "]=" + t[n].val) : e.push(t[n].label + "=" + t[n].val);
    }(r), e;
}

function u(r, u) {
    if ("object" === (void 0 === r ? "undefined" : e(r)) && !t(r) && !o(r)) {
        var l = i(n(r)).join("&");
        return u ? encodeURI(l) : l;
    }
    return "";
}