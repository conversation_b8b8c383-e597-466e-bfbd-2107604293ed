{"pages": ["pages/welcome/welcome", "pages/login/login", "pages/login/info/info", "pages/index/index", "pages/user/user", "pages/cardRules/cardRules", "pages/maintain/maintain", "pages/uphold/uphold", "pages/load/load", "pages/user/businessHandling/businessHandling"], "requiredPrivateInfos": ["getLocation"], "permission": {"scope.userLocation": {"desc": "你的位置信息将用于小程序位置接口的效果展示"}}, "tabBar": {"color": "#000000", "selectedColor": "#DD3231", "backgroundColor": "#ffffff", "borderStyle": "black", "position": "bottom", "list": [{"selectedIconPath": "static/image/index_icon_sel.png", "iconPath": "static/image/index_icon.png", "pagePath": "pages/index/index", "text": "首页"}, {"selectedIconPath": "static/image/cardRules_icon_sel.png", "iconPath": "static/image/cardRules_icon.png", "pagePath": "pages/cardRules/cardRules", "text": "业务详情"}, {"selectedIconPath": "static/image/user_icon_sel.png", "iconPath": "static/image/user_icon.png", "pagePath": "pages/user/user", "text": "个人中心"}]}, "window": {"backgroundTextStyle": "dark", "backgroundColor": "#eaeaea", "navigationBarBackgroundColor": "#fff", "navigationBarTitleText": "12580惠出行", "navigationBarTextStyle": "black"}, "networkTimeout": {"request": 120000, "uploadFile": 300000, "downloadFile": 120000}, "sitemapLocation": "sitemap.json", "usingComponents": {"van-tabbar": "./miniprogram_npm/@vant/weapp/tabbar/index", "van-tabbar-item": "./miniprogram_npm/@vant/weapp/tabbar-item/index", "van-button": "./miniprogram_npm/@vant/weapp/button/index", "van-picker": "./miniprogram_npm/@vant/weapp/picker/index", "van-nav-bar": "./miniprogram_npm/@vant/weapp/nav-bar/index", "van-row": "./miniprogram_npm/@vant/weapp/row/index", "van-col": "./miniprogram_npm/@vant/weapp/col/index", "van-icon": "./miniprogram_npm/@vant/weapp/icon/index", "van-toast": "./miniprogram_npm/@vant/weapp/toast/index", "van-loading": "./miniprogram_npm/@vant/weapp/loading/index", "van-popup": "./miniprogram_npm/@vant/weapp/popup/index", "hczh-tab-bar": "./component/hczh-tab-bar/hczh-tab-bar"}, "subPackages": [{"root": "packagehn/", "pages": ["pages/productsIntroductionhn/productsIntroductionhn", "pages/orderconfirmationhn/orderconfirmationhn", "pages/orderconfirmationhn/ordertheResults/ordertheResults", "pages/seckillprefecturestoreInfohn/seckillprefecturestoreInfohn", "pages/couponDetailhn/couponDetail", "pages/storeInfohn/storeInfo", "pages/storeListhn/storeInfo", "pages/learnDrivinghn/learnDriving", "pages/sceniSpot/sceniSpot", "pages/index/notice/notice", "pages/index/businessHandlinghn/businessHandling", "pages/index/breakRuleshn/breakRules", "pages/index/breakRuleshn/addLicense/addLicense", "pages/index/breakRuleshn/ImproveData/ImproveData", "pages/index/breakRuleshn/couponBindPhone/couponBindPhone", "pages/index/comeOnSpecial/comeOnSpecial", "pages/index/comeOnSpecial/recharge/recharge", "pages/index/comeOnSpecial/rechargeRecord/rechargeRecord", "pages/index/comeOnSpecial/rechargedetails/rechargedetails", "pages/index/comeOnSpecial/success/success", "pages/index/comeOnSpecial/termsOfService/termsOfService", "pages/index/travelInsurance/travelInsurance", "pages/index/travelInsurance/success/success", "pages/index/carStickers/carStickers", "pages/index/carStickers/mailPage/mailPage", "pages/index/carStickers/success/success", "pages/index/legalAid/legalAid", "pages/index/legalAid/termsOfService/termsOfService", "pages/index/roadRescue/roadRescue", "pages/index/roadRescue/termsOfService/termsOfService", "pages/index/scooter/scooter", "pages/index/scooter/termsOfService/termsOfService", "pages/index/scooter/scooterTechnologicalProcess/scooterTechnologicalProcess", "pages/index/yearlyInspection/yearlyInspection", "pages/index/yearlyInspection/yearlyInspectionSix/yearlyInspectionSix", "pages/index/yearlyInspection/yearlyInspectionSix/orderComfirm/orderComfirm", "pages/index/yearlyInspection/yearlyInspectionSix/uploadSuccess/uploadSuccess", "pages/index/yearlyInspection/inspectionStation/inspectionStation", "pages/index/yearlyInspection/appointmentCheckCar/appointmentCheckCar", "pages/serviceRecordhn/serviceRecord", "pages/couponhn/coupon", "pages/couponhn/storeList/storeInfo", "pages/couponhn/scenicArea/scenicArea", "pages/couponhn/wxCoupon/wxCoupon", "pages/couponhn/success/success", "pages/errProhibithn/errProhibit", "pages/index/yearlyInspection/success/success", "pages/acknowledgementOrder/acknowledgementOrder", "pages/acknowledgementOrder/orderDetails/orderDetails", "pages/acknowledgementOrder/checkLogistics/checkLogistics", "pages/myharvestAddress/myharvestAddress", "pages/addmyharvestAddress/addmyharvestAddress", "pages/LuckyDraw/LuckyDraw", "pages/LuckyDraw/shopList", "pages/LuckyDraw/shopDetails", "pages/LuckyDraw/orderDetails", "pages/businessDetails/businessDetails", "pages/openedAvailable/openedAvailable", "pages/oilCard/oilCard", "pages/oilCardList/oilCardList", "pages/CouponExchangeNew/CouponExchangeNew", "pages/index/repairTyre/repairTyre", "pages/index/repairTyre/termsOfService/termsOfService", "pages/couponSuccess/couponSuccess"], "plugins": {"sendCoupon": {"version": "latest", "provider": "wxf3f436ba9bd4be7b"}}}, {"root": "package/", "pages": ["pages/givingRights/givingRights", "pages/comeOnSpecialOld/comeOnSpecialOld", "pages/orderConfirm/orderConfirm", "pages/couponExchange/couponExchange", "pages/dropRule/dropRule", "pages/SpecialArea/SpecialArea", "pages/petroChina/petroChina", "pages/petroChinaVoucher/petroChinaVoucher", "pages/travelBlindBox/travelBlindBox", "pages/petroChina/giftPack/giftPack"]}]}