var t = require("../common/component"), i = require("../common/relation");

(0, t.VantComponent)({
    props: {
        info: null,
        name: null,
        icon: String,
        dot: Boolean,
        iconPrefix: {
            type: String,
            value: "van-icon"
        }
    },
    relation: (0, i.useParent)("tabbar"),
    data: {
        active: !1,
        activeColor: "",
        inactiveColor: ""
    },
    methods: {
        onClick: function() {
            var t = this.parent;
            if (t) {
                var i = t.children.indexOf(this), a = this.data.name || i;
                a !== this.data.active && t.$emit("change", a);
            }
            this.$emit("click");
        },
        updateFromParent: function() {
            var t = this.parent;
            if (t) {
                var i = t.children.indexOf(this), a = t.data, e = this.data, o = (e.name || i) === a.active, n = {};
                o !== e.active && (n.active = o), a.activeColor !== e.activeColor && (n.activeColor = a.activeColor), 
                a.inactiveColor !== e.inactiveColor && (n.inactiveColor = a.inactiveColor), Object.keys(n).length > 0 && this.setData(n);
            }
        }
    }
});