/**
 * 简单的加密解密测试
 * 
 * 运行方法：
 * node test_crypto.js
 */

const CryptoTool = require('./crypto_tool.js');

console.log("🎯 12580小程序加密解密测试\n");

// ==================== 测试1：湖南版本加密解密 ====================
console.log("=".repeat(60));
console.log("📋 测试1：湖南版本加密解密");

// 测试数据
var testData = {
    sCode: "SJS_CODE_12345",
    areaCode: "430100"
};

console.log("🎯 测试数据:", testData);

// 加密
var encrypted = CryptoTool.EncryptDataHn(testData);
if (encrypted) {
    console.log("🔐 加密成功");
    
    // 模拟服务器响应格式
    var serverResponse = {
        key: encrypted.ksynum,
        content: encrypted.cmcxncxn,
        iv: encrypted.avd
    };
    
    // 解密
    var decrypted = CryptoTool.DecryptKeyHn(serverResponse);
    if (decrypted) {
        console.log("🔓 解密成功:", decrypted);
        
        // 验证
        var originalStr = JSON.stringify(testData);
        var decryptedStr = JSON.stringify(decrypted);
        console.log("✅ 验证结果:", originalStr === decryptedStr ? "通过" : "失败");
    } else {
        console.log("❌ 解密失败");
    }
} else {
    console.log("❌ 加密失败");
}

// ==================== 测试2：解密真实数据 ====================
console.log("\n" + "=".repeat(60));
console.log("📋 测试2：解密你提供的真实数据");

var realData = {
    "content": "Z5KNaR1xKweWGg2UUKPJ1xuppg7NE+MSeduoUrt7OlfofxEG6MA/EmFSVN1PN3TPOk2L9yWmtpwyxCPCrO77GhHZ889e7oTvXRVRM6F5vsQKIOuCY2NxFgqCt996rz1jCjP2nNkexi5UDgC+FYLGtAjsyR0bymB93y2TfMz/JK78PO47882bxE+2odl3BrIUD3QZ299aMMtsfirDcFmdgoS5Y72DGUHul1o6KCtP2AK7JmObE9oJUeodcspplF2PJ4Lg7s/kTgXxruU/fnfwUo5QrzTCmRaHK9QOBMTn4hxGXbNWXjhSB92e30K/E07zyLZBk8yfqSLlM6U94T/128M8i0Lnc9aCKIM7YmeSeHiZh+BlXM4O4gj8bk51PNNKsVeW7owo5TCBUkbyXLb6zA==",
    "key": "S5OUYtFrpKMZUGXPDE7TTT0HvZ0dCQpyA6DNznsWnUTMSFtkAXvRSPMWSve+z8Gsv4xOJJtgPLrAw5qeqly0HHgZpWyjDzOjahjeaJdkA7zoUtayySGX3yfUSCja1NtJOPGCn5OzJ6JJTg5OCfDdH0rj1KB06zHQ0YtsP5MTaMGSBj2ZkU4AkbHF3/MfpSuDnUYIevWg+uv3ywi3h5zBwyL8RjiqoVzGDy6pwLmp8o2Z+Gj6IJ8QYooItYNaQ7SG196vO8xfZd+sRm/RHen9dk4WqAEV8z6/FHkgvoGAVPrJVHLcNBEJee5irNIrQxeNP17UkOytbEvSxAkNXUPK4A==",
    "iv": "hihSPyR1EvP5$*!*"
};

console.log("🎯 真实数据（部分）:", {
    key: realData.key.substring(0, 50) + "...",
    content: realData.content.substring(0, 50) + "...",
    iv: realData.iv
});

// 智能解密
var smartResult = CryptoTool.smartDecrypt(realData);
if (smartResult) {
    console.log("🎉 智能解密成功！");
    console.log("📋 检测到版本:", smartResult.version);
    console.log("🔓 解密结果:", smartResult.data);
} else {
    console.log("❌ 智能解密失败");
}

// ==================== 测试3：普通版本测试 ====================
console.log("\n" + "=".repeat(60));
console.log("📋 测试3：普通版本加密解密");

var normalTestData = "Hello World 测试";
console.log("🎯 普通版本测试数据:", normalTestData);

var normalEncrypted = CryptoTool.EncryptData(normalTestData);
if (normalEncrypted) {
    console.log("🔐 普通版本加密成功");
    
    var normalResponse = {
        key: normalEncrypted.ksynum,
        content: normalEncrypted.cmcxncxn,
        iv: normalEncrypted.avd
    };
    
    var normalDecrypted = CryptoTool.DecryptKey(normalResponse);
    if (normalDecrypted) {
        console.log("🔓 普通版本解密成功:", normalDecrypted);
        console.log("✅ 验证结果:", normalTestData === normalDecrypted ? "通过" : "失败");
    } else {
        console.log("❌ 普通版本解密失败");
    }
} else {
    console.log("❌ 普通版本加密失败");
}

// ==================== 测试4：不同数据类型 ====================
console.log("\n" + "=".repeat(60));
console.log("📋 测试4：不同数据类型测试");

var testCases = [
    "简单字符串",
    "包含中文的字符串",
    {name: "张三", age: 25},
    ["数组", "测试", 123],
    "43&business_handling_code&28.2282&112.9388&test_token&***********&APP001&/pages/index/index&mini"
];

testCases.forEach(function(testCase, index) {
    console.log("\n--- 测试用例 " + (index + 1) + " ---");
    console.log("数据:", testCase);
    
    var encrypted = CryptoTool.EncryptDataHn(testCase);
    if (encrypted) {
        var response = {
            key: encrypted.ksynum,
            content: encrypted.cmcxncxn,
            iv: encrypted.avd
        };
        var decrypted = CryptoTool.DecryptKeyHn(response);
        
        var originalStr = typeof testCase === 'object' ? JSON.stringify(testCase) : String(testCase);
        var decryptedStr = typeof decrypted === 'object' ? JSON.stringify(decrypted) : String(decrypted);
        
        console.log("结果:", originalStr === decryptedStr ? "✅ 成功" : "❌ 失败");
    } else {
        console.log("结果: ❌ 加密失败");
    }
});

console.log("\n🎉 所有测试完成！");
console.log("💡 如果所有测试都通过，说明工具运行正常");
console.log("💡 你可以使用 CryptoTool.EncryptDataHn() 和 CryptoTool.DecryptKeyHn() 来处理数据");
