// 湖南版本加密解密测试模块
// 引入必要的模块
var transmission = require("./utils/transmission.js").rsaAesJs;
var aesFunction = require("./utils/aesFunction.js").AESUtil;

// 测试类
var CryptoTester = {
    
    // 测试请求加密
    testEncryptRequest: function(originalData) {
        console.log("=== 测试请求加密 ===");
        console.log("原始请求数据:", originalData);
        console.log("原始数据类型:", typeof originalData);
        
        try {
            // 如果是对象，转换为JSON字符串
            var dataToEncrypt = typeof originalData === 'object' ? JSON.stringify(originalData) : originalData;
            console.log("待加密字符串:", dataToEncrypt);
            
            // 执行加密
            var encryptedData = transmission.EncryptDataHn(dataToEncrypt);
            console.log("加密结果:");
            console.log("  - avd (IV):", encryptedData.avd);
            console.log("  - ksynum (RSA加密的AES密钥):", encryptedData.ksynum);
            console.log("  - cmcxncxn (AES加密的数据):", encryptedData.cmcxncxn);
            
            return encryptedData;
        } catch (error) {
            console.error("加密失败:", error);
            return null;
        }
    },
    
    // 测试响应解密
    testDecryptResponse: function(encryptedResponse) {
        console.log("\n=== 测试响应解密 ===");
        console.log("加密的响应数据:", encryptedResponse);
        
        try {
            // 执行解密
            var decryptedData = transmission.DecryptKeyHn(encryptedResponse);
            console.log("解密后的原始数据:", decryptedData);
            console.log("解密数据类型:", typeof decryptedData);
            
            return decryptedData;
        } catch (error) {
            console.error("解密失败:", error);
            return null;
        }
    },
    
    // 完整的加密解密测试
    testFullCycle: function(testData) {
        console.log("\n========== 完整加密解密测试 ==========");
        
        // 1. 加密测试数据
        var encrypted = this.testEncryptRequest(testData);
        if (!encrypted) {
            console.error("加密失败，测试终止");
            return false;
        }
        
        // 2. 模拟服务器响应格式（通常服务器会返回类似的结构）
        var mockServerResponse = {
            key: encrypted.ksynum,     // RSA加密的AES密钥
            content: encrypted.cmcxncxn, // AES加密的内容
            iv: encrypted.avd          // IV
        };
        
        console.log("\n模拟服务器响应格式:", mockServerResponse);
        
        // 3. 解密响应
        var decrypted = this.testDecryptResponse(mockServerResponse);
        
        // 4. 验证结果
        var originalStr = typeof testData === 'object' ? JSON.stringify(testData) : testData;
        var decryptedStr = typeof decrypted === 'object' ? JSON.stringify(decrypted) : decrypted;
        
        console.log("\n=== 验证结果 ===");
        console.log("原始数据:", originalStr);
        console.log("解密数据:", decryptedStr);
        console.log("数据一致性:", originalStr === decryptedStr ? "✅ 通过" : "❌ 失败");
        
        return originalStr === decryptedStr;
    },
    
    // 测试AES密钥和IV生成
    testKeyGeneration: function() {
        console.log("\n=== 测试密钥生成 ===");
        
        for (var i = 0; i < 3; i++) {
            var aesKey = aesFunction.getAesKey();
            var iv = aesFunction.getIv();
            
            console.log("第" + (i + 1) + "次生成:");
            console.log("  AES密钥:", aesKey, "长度:", aesKey.length);
            console.log("  IV:", iv, "长度:", iv.length);
        }
    },
    
    // 运行所有测试
    runAllTests: function() {
        console.log("开始运行湖南版本加密解密测试...\n");
        
        // 测试1: 简单字符串
        console.log("【测试1】简单字符串");
        this.testFullCycle("Hello World");
        
        // 测试2: JSON对象
        console.log("\n【测试2】JSON对象");
        var testObj = {
            sCode: "SJS_CODE_12345",
            areaCode: "430100",
            timestamp: new Date().getTime()
        };
        this.testFullCycle(testObj);
        
        // 测试3: 复杂数据
        console.log("\n【测试3】复杂数据");
        var complexData = {
            provinceCode: "43",
            businessCode: "business_handling_code",
            latitude: "28.2282",
            longitude: "112.9388",
            token: "test_token_12345",
            telephone: "***********",
            feeAppCode: "APP001",
            page: "/pages/index/index",
            platform: "mini"
        };
        this.testFullCycle(JSON.stringify(complexData));
        
        // 测试4: 密钥生成
        this.testKeyGeneration();
        
        console.log("\n所有测试完成！");
    }
};

// 导出测试模块
module.exports = CryptoTester;

// 如果直接运行此文件，执行测试
if (typeof wx === 'undefined') {
    // 在Node.js环境中运行
    console.log("在Node.js环境中运行测试（部分功能可能不可用）");
} else {
    // 在小程序环境中运行
    console.log("在小程序环境中运行测试");
}
