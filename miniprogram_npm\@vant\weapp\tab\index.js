var e = require("../common/relation");

(0, require("../common/component").VantComponent)({
    relation: (0, e.useParent)("tabs"),
    props: {
        dot: {
            type: <PERSON><PERSON><PERSON>,
            observer: "update"
        },
        info: {
            type: null,
            observer: "update"
        },
        title: {
            type: String,
            observer: "update"
        },
        disabled: {
            type: <PERSON><PERSON><PERSON>,
            observer: "update"
        },
        titleStyle: {
            type: String,
            observer: "update"
        },
        name: {
            type: null,
            value: ""
        }
    },
    data: {
        active: !1
    },
    methods: {
        getComputedName: function() {
            return "" !== this.data.name ? this.data.name : this.index;
        },
        updateRender: function(e, t) {
            var a = t.data;
            this.inited = this.inited || e, this.setData({
                active: e,
                shouldRender: this.inited || !a.lazyRender,
                shouldShow: e || a.animated
            });
        },
        update: function() {
            this.parent && this.parent.updateTabs();
        }
    }
});