var t = require("../../../../@babel/runtime/helpers/slicedToArray"), i = require("../common/component"), e = require("../common/utils");

(0, i.VantComponent)({
    props: {
        text: {
            type: String,
            value: "",
            observer: "init"
        },
        mode: {
            type: String,
            value: ""
        },
        url: {
            type: String,
            value: ""
        },
        openType: {
            type: String,
            value: "navigate"
        },
        delay: {
            type: Number,
            value: 1
        },
        speed: {
            type: Number,
            value: 60,
            observer: "init"
        },
        scrollable: null,
        leftIcon: {
            type: String,
            value: ""
        },
        color: String,
        backgroundColor: String,
        background: String,
        wrapable: Boolean
    },
    data: {
        show: !0
    },
    created: function() {
        this.resetAnimation = wx.createAnimation({
            duration: 0,
            timingFunction: "linear"
        });
    },
    destroyed: function() {
        this.timer && clearTimeout(this.timer);
    },
    mounted: function() {
        this.init();
    },
    methods: {
        init: function() {
            var i = this;
            (0, e.requestAnimationFrame)(function() {
                Promise.all([ (0, e.getRect)(i, ".van-notice-bar__content"), (0, e.getRect)(i, ".van-notice-bar__wrap") ]).then(function(e) {
                    var n = t(e, 2), a = n[0], r = n[1], o = i.data, l = o.speed, s = o.scrollable, c = o.delay;
                    if (null != a && null != r && a.width && r.width && !1 !== s && (s || r.width < a.width)) {
                        var u = (r.width + a.width) / l * 1e3;
                        i.wrapWidth = r.width, i.contentWidth = a.width, i.duration = u, i.animation = wx.createAnimation({
                            duration: u,
                            timingFunction: "linear",
                            delay: c
                        }), i.scroll(!0);
                    }
                });
            });
        },
        scroll: function() {
            var t = this, i = arguments.length > 0 && void 0 !== arguments[0] && arguments[0];
            this.timer && clearTimeout(this.timer), this.timer = null, this.setData({
                animationData: this.resetAnimation.translateX(i ? 0 : this.wrapWidth).step().export()
            }), (0, e.requestAnimationFrame)(function() {
                t.setData({
                    animationData: t.animation.translateX(-t.contentWidth).step().export()
                });
            }), this.timer = setTimeout(function() {
                t.scroll();
            }, this.duration);
        },
        onClickIcon: function(t) {
            "closeable" === this.data.mode && (this.timer && clearTimeout(this.timer), this.timer = null, 
            this.setData({
                show: !1
            }), this.$emit("close", t.detail));
        },
        onClick: function(t) {
            this.$emit("click", t);
        }
    }
});