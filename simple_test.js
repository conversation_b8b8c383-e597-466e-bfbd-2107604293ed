// 简单的加密解密测试脚本
// 可以直接在小程序控制台中复制粘贴运行

// 测试函数
function testCrypto() {
    console.log("========== 湖南版本加密解密测试 ==========");
    
    // 引入必要的模块
    var transmission = require("./utils/transmission.js").rsaAesJs;
    
    // 测试数据
    var testCases = [
        {
            name: "简单字符串",
            data: "Hello World 测试"
        },
        {
            name: "JSON字符串",
            data: JSON.stringify({
                sCode: "SJS_CODE_12345",
                areaCode: "430100",
                timestamp: Date.now()
            })
        },
        {
            name: "业务数据格式",
            data: "43&business_handling_code&28.2282&112.9388&test_token&***********&APP001&/pages/index/index&mini"
        }
    ];
    
    // 运行测试
    testCases.forEach(function(testCase, index) {
        console.log("\n--- 测试 " + (index + 1) + ": " + testCase.name + " ---");
        console.log("原始数据:", testCase.data);
        
        try {
            // 1. 加密数据
            console.log("步骤1: 加密数据...");
            var encrypted = transmission.EncryptDataHn(testCase.data);
            console.log("加密结果:");
            console.log("  IV (avd):", encrypted.avd);
            console.log("  RSA加密的AES密钥 (ksynum):", encrypted.ksynum);
            console.log("  AES加密的数据 (cmcxncxn):", encrypted.cmcxncxn);
            
            // 2. 模拟服务器响应格式
            console.log("步骤2: 模拟服务器响应...");
            var serverResponse = {
                key: encrypted.ksynum,
                content: encrypted.cmcxncxn,
                iv: encrypted.avd
            };
            console.log("服务器响应格式:", serverResponse);
            
            // 3. 解密数据
            console.log("步骤3: 解密数据...");
            var decrypted = transmission.DecryptKeyHn(serverResponse);
            console.log("解密结果:", decrypted);
            
            // 4. 验证结果
            var isSuccess = testCase.data === decrypted;
            console.log("验证结果:", isSuccess ? "✅ 成功" : "❌ 失败");
            
            if (!isSuccess) {
                console.log("原始长度:", testCase.data.length);
                console.log("解密长度:", decrypted.length);
                console.log("差异位置:", findDifference(testCase.data, decrypted));
            }
            
        } catch (error) {
            console.error("❌ 测试失败:", error);
            console.error("错误堆栈:", error.stack);
        }
    });
    
    console.log("\n========== 测试完成 ==========");
}

// 查找字符串差异的辅助函数
function findDifference(str1, str2) {
    var minLength = Math.min(str1.length, str2.length);
    for (var i = 0; i < minLength; i++) {
        if (str1[i] !== str2[i]) {
            return {
                position: i,
                original: str1[i],
                decrypted: str2[i]
            };
        }
    }
    if (str1.length !== str2.length) {
        return {
            position: minLength,
            lengthDiff: str1.length - str2.length
        };
    }
    return null;
}

// 测试密钥生成
function testKeyGeneration() {
    console.log("\n========== 密钥生成测试 ==========");
    
    var aesFunction = require("./utils/aesFunction.js").AESUtil;
    
    for (var i = 0; i < 5; i++) {
        console.log("第 " + (i + 1) + " 次生成:");
        var aesKey = aesFunction.getAesKey();
        var iv = aesFunction.getIv();
        console.log("  AES密钥:", aesKey, "(长度: " + aesKey.length + ")");
        console.log("  IV:", iv, "(长度: " + iv.length + ")");
    }
}

// 测试实际请求数据格式
function testRealRequestFormat() {
    console.log("\n========== 实际请求格式测试 ==========");
    
    var transmission = require("./utils/transmission.js").rsaAesJs;
    
    // 模拟从存储中获取的数据
    var mockStorageData = {
        provinceCode: "43",
        cityCode_hn: "430100",
        latitude: "28.2282",
        longitude: "112.9388",
        token_hn: "mock_token_12345",
        telephone_hn: "***********",
        feeAppCode_hn: "APP001"
    };
    
    // 测试不同的业务场景
    var scenarios = [
        {
            name: "优惠券查询",
            data: {
                sCode: "SJS_CODE_12345",
                areaCode: mockStorageData.cityCode_hn
            }
        },
        {
            name: "业务办理",
            data: mockStorageData.provinceCode + "&" + "business_handling_code" + "&" + 
                  mockStorageData.latitude + "&" + mockStorageData.longitude + "&" + 
                  mockStorageData.token_hn + "&" + mockStorageData.telephone_hn + "&" + 
                  mockStorageData.feeAppCode_hn + "&" + "/pages/index/index" + "&" + "mini"
        },
        {
            name: "用户信息验证",
            data: mockStorageData.token_hn + "&" + mockStorageData.provinceCode + "&" + mockStorageData.cityCode_hn
        }
    ];
    
    scenarios.forEach(function(scenario) {
        console.log("\n--- " + scenario.name + " ---");
        var dataToEncrypt = typeof scenario.data === 'object' ? JSON.stringify(scenario.data) : scenario.data;
        console.log("原始数据:", dataToEncrypt);
        
        try {
            var encrypted = transmission.EncryptDataHn(dataToEncrypt);
            console.log("加密成功，数据长度:", encrypted.cmcxncxn.length);
            
            // 验证解密
            var serverResponse = {
                key: encrypted.ksynum,
                content: encrypted.cmcxncxn,
                iv: encrypted.avd
            };
            var decrypted = transmission.DecryptKeyHn(serverResponse);
            console.log("解密验证:", dataToEncrypt === decrypted ? "✅ 通过" : "❌ 失败");
            
        } catch (error) {
            console.error("处理失败:", error.message);
        }
    });
}

// 导出测试函数（如果在模块环境中）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        testCrypto: testCrypto,
        testKeyGeneration: testKeyGeneration,
        testRealRequestFormat: testRealRequestFormat
    };
}

// 如果直接运行，执行所有测试
if (typeof wx !== 'undefined') {
    // 在小程序环境中
    console.log("在小程序环境中运行测试");
    
    // 可以直接调用测试函数
    // testCrypto();
    // testKeyGeneration();
    // testRealRequestFormat();
} else {
    console.log("请在小程序环境中运行此测试");
}
