var e = require("../common/component"), n = require("../mixins/button"), o = require("../common/version"), t = [ n.button ];

(0, o.canIUseFormFieldButton)() && t.push("wx://form-field-button"), (0, e.VantComponent)({
    mixins: t,
    classes: [ "hover-class", "loading-class" ],
    data: {
        baseStyle: ""
    },
    props: {
        formType: String,
        icon: String,
        classPrefix: {
            type: String,
            value: "van-icon"
        },
        plain: Boolean,
        block: Boolean,
        round: Boolean,
        square: Boolean,
        loading: <PERSON><PERSON><PERSON>,
        hairline: <PERSON><PERSON><PERSON>,
        disabled: <PERSON>olean,
        loadingText: String,
        customStyle: String,
        loadingType: {
            type: String,
            value: "circular"
        },
        type: {
            type: String,
            value: "default"
        },
        dataset: null,
        size: {
            type: String,
            value: "normal"
        },
        loadingSize: {
            type: String,
            value: "20px"
        },
        color: String
    },
    methods: {
        onClick: function(e) {
            var n = this;
            this.$emit("click", e);
            var o = this.data, t = o.canIUseGetUserProfile, i = o.openType, a = o.getUserProfileDesc, l = o.lang;
            "getUserInfo" === i && t && wx.getUserProfile({
                desc: a || "  ",
                lang: l || "en",
                complete: function(e) {
                    n.$emit("getuserinfo", e);
                }
            });
        }
    }
});