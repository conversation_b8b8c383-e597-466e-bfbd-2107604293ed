var e = require("../common/utils"), t = require("../common/component"), i = require("./props");

(0, t.VantComponent)({
    field: !0,
    classes: [ "input-class", "right-icon-class", "label-class" ],
    props: Object.assign(Object.assign(Object.assign(Object.assign({}, i.commonProps), i.inputProps), i.textareaProps), {
        size: String,
        icon: String,
        label: String,
        error: <PERSON>olean,
        center: Boolean,
        isLink: Boolean,
        leftIcon: String,
        rightIcon: String,
        autosize: null,
        required: <PERSON><PERSON>an,
        iconClass: String,
        clickable: <PERSON><PERSON><PERSON>,
        inputAlign: String,
        customStyle: String,
        errorMessage: String,
        arrowDirection: String,
        showWordLimit: Boolean,
        errorMessageAlign: String,
        readonly: {
            type: <PERSON><PERSON><PERSON>,
            observer: "setShowClear"
        },
        clearable: {
            type: <PERSON><PERSON><PERSON>,
            observer: "setShowClear"
        },
        clearTrigger: {
            type: String,
            value: "focus"
        },
        border: {
            type: <PERSON><PERSON><PERSON>,
            value: !0
        },
        titleWidth: {
            type: String,
            value: "6.2em"
        },
        clearIcon: {
            type: String,
            value: "clear"
        }
    }),
    data: {
        focused: !1,
        innerValue: "",
        showClear: !1
    },
    created: function() {
        this.value = this.data.value, this.setData({
            innerValue: this.value
        });
    },
    methods: {
        onInput: function(e) {
            var t = (e.detail || {}).value, i = void 0 === t ? "" : t;
            this.value = i, this.setShowClear(), this.emitChange();
        },
        onFocus: function(e) {
            this.focused = !0, this.setShowClear(), this.$emit("focus", e.detail);
        },
        onBlur: function(e) {
            this.focused = !1, this.setShowClear(), this.$emit("blur", e.detail);
        },
        onClickIcon: function() {
            this.$emit("click-icon");
        },
        onClickInput: function(e) {
            this.$emit("click-input", e.detail);
        },
        onClear: function() {
            var t = this;
            this.setData({
                innerValue: ""
            }), this.value = "", this.setShowClear(), (0, e.nextTick)(function() {
                t.emitChange(), t.$emit("clear", "");
            });
        },
        onConfirm: function(e) {
            var t = (e.detail || {}).value, i = void 0 === t ? "" : t;
            this.value = i, this.setShowClear(), this.$emit("confirm", i);
        },
        setValue: function(e) {
            this.value = e, this.setShowClear(), "" === e && this.setData({
                innerValue: ""
            }), this.emitChange();
        },
        onLineChange: function(e) {
            this.$emit("linechange", e.detail);
        },
        onKeyboardHeightChange: function(e) {
            this.$emit("keyboardheightchange", e.detail);
        },
        emitChange: function() {
            var t = this;
            this.setData({
                value: this.value
            }), (0, e.nextTick)(function() {
                t.$emit("input", t.value), t.$emit("change", t.value);
            });
        },
        setShowClear: function() {
            var e = this.data, t = e.clearable, i = e.readonly, n = e.clearTrigger, a = this.focused, o = this.value, s = !1;
            t && !i && (s = !!o && ("always" === n || "focus" === n && a));
            this.setData({
                showClear: s
            });
        },
        noop: function() {}
    }
});