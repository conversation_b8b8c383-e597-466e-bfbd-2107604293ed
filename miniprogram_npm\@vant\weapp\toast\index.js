(0, require("../common/component").VantComponent)({
    props: {
        show: <PERSON><PERSON><PERSON>,
        mask: <PERSON><PERSON><PERSON>,
        message: String,
        forbidClick: <PERSON><PERSON><PERSON>,
        zIndex: {
            type: Number,
            value: 1e3
        },
        type: {
            type: String,
            value: "text"
        },
        loadingType: {
            type: String,
            value: "circular"
        },
        position: {
            type: String,
            value: "middle"
        }
    },
    methods: {
        noop: function() {}
    }
});