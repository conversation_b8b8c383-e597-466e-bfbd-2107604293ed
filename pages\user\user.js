(e = require("@vant/weapp/dialog/dialog")) && e.__esModule;

var e, t = require("../../utils/http.js");

var a = require("../../utils/util.js").unction, n = require("../../utils/transmission.js").rsaAesJs, o = getApp();

Page({
    data: {
        tabActive: 2,
        loginStatus: "0",
        userName: "去登录",
        isLoading: !1,
        navList: [],
        latitude: "",
        longitude: "",
        wifiBool: !1,
        avatarUrl: "",
        pathImg: t.baseUrlImg,
        currentOil: 0,
        isShowOil: 0,
        isOverlay: !1,
        checked: !1
    },
    onChooseAvatar: function(e) {
        var t = e.detail.avatarUrl;
        wx.setStorageSync("avatarUrl", t), this.setData({
            avatarUrl: t
        });
    },
    onLoad: function() {
        var e = wx.getStorageSync("avatarUrl");
        wx.hideHomeButton(), wx.showShareMenu({
            withShareTicket: !0
        }), this.setData({
            avatarUrl: e
        }), "0" === wx.getStorageSync("loginStatus_hn") ? o.hcx.setProfile({
            user_type: "1",
            business_type: "HCZH",
            platform_type: "client_2",
            cp_name: "wz",
            page_type: "个人中心页",
            province: wx.getStorageSync("provinceName"),
            city: wx.getStorageSync("cityName_hn")
        }) : "1" === wx.getStorageSync("loginStatus_hn") && void 0 !== wx.getStorageSync("memberTypeList_hn") && wx.getStorageSync("memberTypeList_hn").length > 0 ? o.hcx.setProfile({
            user_type: "3",
            phoneNumber: wx.getStorageSync("telephone_hn"),
            business_type: "HCZH",
            platform_type: "client_2",
            cp_name: "wz",
            page_type: "个人中心页",
            province: wx.getStorageSync("provinceName"),
            city: wx.getStorageSync("cityName_hn")
        }) : o.hcx.setProfile({
            user_type: "2",
            phoneNumber: wx.getStorageSync("telephone_hn"),
            business_type: "HCZH",
            platform_type: "client_2",
            cp_name: "wz",
            page_type: "个人中心页",
            province: wx.getStorageSync("provinceName"),
            city: wx.getStorageSync("cityName_hn")
        });
    },
    onCheckboxChange: function(e) {
        this.setData({
            checked: e.detail
        });
    },
    closeOverlay: function() {
        this.setData({
            isOverlay: !1,
            checked: !1
        });
    },
    submit: function() {
        if (this.data.checked) {
            this.setData({
                isOverlay: !1
            });
            var e = this, a = wx.getStorageSync("token_hn"), o = n.EncryptDataHn(a);
            (0, t.POSTHN)("/newUser/userLogout", o).then(function(t) {
                e.setData({
                    isOverlay: !1
                }), t.successful ? (wx.setStorageSync("userLogin", "1"), wx.setStorageSync("userTypeIndex", 0), 
                wx.setStorageSync("token_hn", ""), wx.setStorageSync("loginStatus_hn", "0"), wx.setStorageSync("telephone_hn", ""), 
                wx.setStorageSync("isSuperVip", !1), wx.setStorageSync("provinceListVip", []), e.setData({
                    loginStatus: "0",
                    userName: "做个有身份的人，请登录享权益吧。"
                })) : (wx.setStorageSync("provinceListVip", []), wx.setStorageSync("token_hn", ""), 
                wx.setStorageSync("loginStatus_hn", "0"), e.setData({
                    loginStatus: "0",
                    userName: "做个有身份的人，请登录享权益吧。"
                }), wx.showToast({
                    title: "成功退出登录",
                    icon: "none"
                }));
            });
        } else wx.showToast({
            title: "请您确认申请注销信息并勾选。",
            icon: "none"
        });
    },
    onShow: function() {
        console.log("--onShow---111"), o.getServeTime();
        var e = wx.getStorageSync("loginStatus_hn"), t = wx.getStorageSync("telephone_hn"), a = wx.getStorageSync("memberTypeList_hn"), n = wx.getStorageSync("userTypeIndex");
        a.length > 0 && this.setData({
            isShowOil: a[n].isShowOil,
            currentOil: a[n].restOilDrop
        }), "1" == e ? this.setData({
            userName: t,
            loginStatus: e
        }) : this.setData({
            loginStatus: "0",
            userName: "做个有身份的人，请登录享权益吧。"
        }), this.userList();
    },
    getUserInfoImg: function() {
        var e = this, t = wx.getStorageSync("avatarUrl");
        "" != t && null != t || wx.getUserProfile({
            desc: "用于完善会员资料",
            success: function(t) {
                var a = t.userInfo.avatarUrl;
                console.log("getUserProfile", t.userInfo), wx.setStorageSync("avatarUrl", a), e.setData({
                    avatarUrl: a
                });
            }
        });
    },
    handleOut: function() {
        this.setData({
            isOverlay: !0,
            checked: !1
        });
    },
    handleCall: function() {
        wx.makePhoneCall({
            phoneNumber: "4000125806"
        });
    },
    goToDropRule: function() {
        wx.navigateTo({
            url: "/package/pages/dropRule/dropRule"
        });
    },
    handleNav: a(function(e) {
        var t = e.currentTarget.dataset.item, a = wx.getStorageSync("loginStatus_hn");
        if (console.log("loginStatus---", a), "1" == a) wx.navigateTo({
            url: t.url
        }); else {
            wx.showToast({
                icon: "none",
                title: "请您先登录再来领取权益吧！"
            });
            setTimeout(function() {
                wx.navigateTo({
                    url: "/pages/login/login"
                });
            }, 1e3);
        }
    }),
    handleLogin: function() {
        wx.navigateTo({
            url: "/pages/login/login"
        });
    },
    userList: function() {
        var e = this, a = wx.getStorageSync("provinceCode"), r = wx.getStorageSync("getPersonCenterMenu" + a);
        if (null == r || "" == r) {
            var s = wx.getStorageSync("latitude"), i = wx.getStorageSync("longitude"), l = wx.getStorageSync("token_hn"), c = wx.getStorageSync("provinceCode");
            "" == l && (l = "*"), "" != c && null != c || (c = "*"), null == s && (s = 0), null == i && (i = 0);
            var g = "".concat(l, "&").concat(c, "&").concat("mini", "&").concat(s, "&").concat(i);
            console.log("/personCenter/getPersonCenterMenu", g);
            var u = n.EncryptDataHn(g);
            console.log("/personCenter/getPersonCenterMenu", u), e.setData({
                isLoading: !0,
                wifiBool: !1
            }), o.onNetworkStatusChange().then(function(a) {
                a ? (0, t.POSTHN)("/personCenter/getPersonCenterMenu", u).then(function(t) {
                    console.log("列表内容", t), e.setData({
                        isLoading: !1
                    }), null == t && e.setData({
                        navList: [],
                        wifiBool: !0
                    }), t.successful ? (wx.setStorageSync("getPersonCenterMenu" + c, t), t.data != [] && t.data.length, 
                    e.setData({
                        navList: t.data,
                        wifiBool: !1
                    })) : e.setData({
                        navList: [],
                        wifiBool: !0
                    });
                }) : e.setData({
                    navList: [],
                    isLoading: !1,
                    wifiBool: !0
                });
            });
        } else e.setData({
            navList: r.data
        });
    },
    userhandNavToJd: function() {
        wx.navigateToMiniProgram({
            appId: "wx1edf489cb248852c",
            path: "/pages/proxy/union/union?spreadUrl=https://u.jd.com/UC2pYEN"
        });
    },
    userhandNav: a(function(e) {
        var t = e.currentTarget.dataset.item;
        console.log("点击了", e.currentTarget.dataset.item.modelTypeCode);
        var a = wx.getStorageSync("loginStatus_hn");
        if ("personal_center_service_tel" == t.modelTypeCode) return wx.makePhoneCall({
            phoneNumber: t.serviceTel
        }), !1;
        if ("personal_info_ protect" == t.modelTypeCode) return wx.navigateTo({
            url: "/pages/login/info/info"
        }), !1;
        if ("1" != a) {
            wx.showToast({
                icon: "none",
                title: "请您先登录再来领取权益吧！"
            });
            setTimeout(function() {
                wx.navigateTo({
                    url: "/pages/login/login"
                });
            }, 1e3);
        } else "personal_center_my_car" == t.modelTypeCode ? (console.log("111"), wx.navigateTo({
            url: "/packagehn/pages/index/breakRuleshn/breakRules"
        })) : "personal_center_my_coupon" == t.modelTypeCode ? wx.navigateTo({
            url: "/packagehn/pages/couponhn/coupon"
        }) : "personal_center_my_service_history" == t.modelTypeCode ? wx.navigateTo({
            url: "/packagehn/pages/serviceRecordhn/serviceRecord"
        }) : "personal_center_my_post_address" == t.modelTypeCode ? wx.navigateTo({
            url: "/packagehn/pages/myharvestAddress/myharvestAddress"
        }) : "personal_center_my_order_list" == t.modelTypeCode ? wx.navigateTo({
            url: "/packagehn/pages/acknowledgementOrder/acknowledgementOrder"
        }) : "personal_center_my_oil_card" == t.modelTypeCode ? wx.navigateTo({
            url: "/packagehn/pages/oilCardList/oilCardList"
        }) : "oil_type" == t.modelTypeCode ? wx.navigateTo({
            url: "/packagehn/pages/index/comeOnSpecial/rechargeRecord/rechargeRecord"
        }) : "personal_center_scenic" == t.modelTypeCode ? wx.navigateTo({
            url: "/pages/user/businessHandling/businessHandling"
        }) : "person_order_type_code" == t.modelTypeCode && wx.navigateTo({
            url: "/package/pages/order/order"
        });
    }),
    orderorder: function() {
        wx.navigateTo({
            url: "/package/pages/order/order"
        });
    }
});