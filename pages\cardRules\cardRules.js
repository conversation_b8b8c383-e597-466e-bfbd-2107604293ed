var e = require("../../utils/http.js"), t = require("../../utils/transmission.js").rsaAesJs, n = getApp();

Page({
    data: {
        showDealWith: !1,
        selectVal: "",
        memberGrade: [],
        currentType: "2",
        currentHtml: "",
        tabActive: 1,
        isLoading: !1,
        introduction: !1,
        memberGradeCode: "",
        available: !1,
        latitude: "*",
        longitude: "*",
        memberNone: !1
    },
    onLoad: function(e) {
        console.log("hide onLoadonLoadonLoad success"), n.getServeTime();
    },
    onShow: function() {
        var e = wx.getStorageSync("loginStatus_hn"), t = wx.getStorageSync("memberTypeList_hn");
        "1" == e && t.length < 1 ? this.setData({
            memberNone: !0
        }) : this.setData({
            memberNone: !1
        }), console.log("hide onShowonShowonShowonShow success"), wx.hideHomeButton({
            success: function() {
                console.log("hide home success");
            },
            fail: function() {
                console.log("hide home fail");
            },
            complete: function() {
                console.log("hide home complete");
            }
        }), this.getContent();
    },
    getContent: function() {
        var n = this, a = wx.getStorageSync("provinceCode"), o = wx.getStorageSync("feeAppCode_hn"), s = wx.getStorageSync("selectBusinessDetails" + a);
        if (console.log("---dataStr---", s), "collection_rules_content" !== o && this.setData({
            showDealWith: !0
        }), null == s || "" == s) {
            this.setData({
                isLoading: !0
            });
            var r = wx.getStorageSync("latitude"), c = wx.getStorageSync("longitude"), l = wx.getStorageSync("token_hn");
            "" == l && (l = "*"), "" != a && null != a || (a = "*");
            var i = "".concat(a, "&").concat(r, "&").concat(c, "&").concat(l);
            console.log(i);
            var g = t.EncryptDataHn(i);
            (0, e.POSTHN)("/activityRulesInfo/selectBusinessDetails", g).then(function(e) {
                if (e.successful) {
                    wx.setStorageSync("selectBusinessDetails" + a, e);
                    var t = e.data, s = [];
                    if (null != t) {
                        var r = t[0].memberGradeContent;
                        r = r.replace(/<img/g, '<img style="max-width:100%;height:auto"'), n.setData({
                            selectVal: t[0].memberGradeCode,
                            currentHtml: r
                        });
                        for (var c = 0; c < t.length; c++) {
                            var l = t[c].memberGradeContent;
                            l = l.replace(/<img/g, '<img style="max-width:100%;height:auto"'), t[c].memberGradeContent = l, 
                            t[c].memberGradeCode == o ? n.setData({
                                selectVal: o,
                                currentHtml: l
                            }) : n.setData({
                                isLoading: !1
                            });
                        }
                        t.forEach(function(e, t) {
                            var n = Math.floor(t / 3);
                            s[n] || (s[n] = []), s[n].push(e);
                        }), n.setData({
                            memberGrade: s,
                            isLoading: !1
                        });
                    } else n.setData({
                        isLoading: !1
                    });
                    console.log("业务详情返回数据", e);
                } else n.setData({
                    isLoading: !1
                });
            });
        } else {
            var u = s;
            if (u.successful) {
                var d = u.data, m = [];
                if (null != d) {
                    var h = d[0].memberGradeContent;
                    h = h.replace(/<img/g, '<img style="max-width:100%;height:auto"'), this.setData({
                        selectVal: d[0].memberGradeCode,
                        currentHtml: h
                    });
                    for (var S = 0; S < d.length; S++) {
                        var w = d[S].memberGradeContent;
                        w = w.replace(/<img/g, '<img style="max-width:100%;height:auto"'), d[S].memberGradeContent = w, 
                        d[S].memberGradeCode == o && this.setData({
                            selectVal: o,
                            currentHtml: w
                        });
                    }
                    d.forEach(function(e, t) {
                        var n = Math.floor(t / 3);
                        m[n] || (m[n] = []), m[n].push(e);
                    }), console.log("--pages--", m), this.setData({
                        memberGrade: m
                    });
                }
            }
        }
    },
    handleSkipCoupon: function(e) {
        var t = e.currentTarget.dataset.item, n = t.memberGradeCode, a = t.memberGradeContent;
        this.setData({
            showDealWith: "collection_rules_content" !== n,
            selectVal: n,
            currentHtml: a
        });
    },
    BuyOpen: function() {
        var n = wx.getStorageSync("latitude"), a = wx.getStorageSync("longitude"), o = wx.getStorageSync("token_hn"), s = wx.getStorageSync("telephone_hn"), r = this.data.selectVal, c = wx.getStorageSync("provinceCode");
        "" != o && null != o || (o = "*"), "" != s && null != s || (s = "*"), "" != r && null != r || (r = "*"), 
        "" != c && null != c || (c = "*");
        var l = "".concat(c, "&").concat("business_handling_code", "&").concat(n, "&").concat(a, "&").concat(o, "&").concat(s, "&").concat(r, "&", "/pages/index/index", "&", "mini");
        console.log(l);
        var i = t.EncryptDataHn(l);
        (0, e.POSTHN)("/activityRulesInfo/selectTextContentByCode", i).then(function(e) {
            if (e.successful) {
                wx.setStorageSync("selectTextContentByCode" + c, e);
                debugger;
                var t = e.data.url, n = e.data.textContent;
                wx.setStorageSync("urlStr", t), wx.setStorageSync("urlStrContent", n), null != n && "" != n && wx.showToast({
                    icon: "none",
                    title: n
                });
                setTimeout(function() {
                    "http" == t.substr(0, 4) && wx.navigateTo({
                        url: "/packagehn/pages/index/businessHandlinghn/businessHandling"
                    });
                }, 1e3);
            } else wx.setStorageSync("urlStr", ""), wx.setStorageSync("urlStrContent", ""), 
            wx.setStorageSync("selectTextContentByCode" + c, "");
        });
    }
});