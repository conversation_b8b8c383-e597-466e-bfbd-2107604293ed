/**
 * 湖南版本真实解密工具
 * 使用小程序中的真实RSA私钥和AES解密逻辑
 * 可以解密真实的服务器响应数据
 */

var RealDecryptTool = {
    
    // 湖南版本的RSA私钥（从小程序中提取）
    HN_PRIVATE_KEY: `**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`,

    // Base64转Hex（从小程序中提取的逻辑）
    b64tohex: function(b64) {
        try {
            var binary = '';
            if (typeof atob !== 'undefined') {
                binary = atob(b64);
            } else if (typeof Buffer !== 'undefined') {
                binary = Buffer.from(b64, 'base64').toString('binary');
            } else {
                throw new Error('Base64解码不支持');
            }
            
            var hex = '';
            for (var i = 0; i < binary.length; i++) {
                var h = binary.charCodeAt(i).toString(16);
                hex += h.length === 1 ? '0' + h : h;
            }
            return hex;
        } catch (e) {
            console.error("❌ Base64转Hex失败:", e.message);
            return null;
        }
    },

    // 使用Web Crypto API进行RSA解密（如果可用）
    rsaDecryptWithWebCrypto: async function(encryptedHex) {
        if (typeof crypto === 'undefined' || !crypto.subtle) {
            throw new Error('Web Crypto API不可用');
        }

        try {
            // 将PEM格式的私钥转换为ArrayBuffer
            var pemHeader = "-----BEGIN PRIVATE KEY-----";
            var pemFooter = "-----END PRIVATE KEY-----";
            var pemContents = this.HN_PRIVATE_KEY.substring(pemHeader.length, this.HN_PRIVATE_KEY.length - pemFooter.length);
            var binaryDerString = atob(pemContents.replace(/\s/g, ''));
            var binaryDer = new Uint8Array(binaryDerString.length);
            for (var i = 0; i < binaryDerString.length; i++) {
                binaryDer[i] = binaryDerString.charCodeAt(i);
            }

            // 导入私钥
            var privateKey = await crypto.subtle.importKey(
                "pkcs8",
                binaryDer,
                {
                    name: "RSA-OAEP",
                    hash: "SHA-1"
                },
                false,
                ["decrypt"]
            );

            // 将hex转换为ArrayBuffer
            var encryptedBytes = new Uint8Array(encryptedHex.length / 2);
            for (var i = 0; i < encryptedHex.length; i += 2) {
                encryptedBytes[i / 2] = parseInt(encryptedHex.substr(i, 2), 16);
            }

            // 解密
            var decrypted = await crypto.subtle.decrypt(
                "RSA-OAEP",
                privateKey,
                encryptedBytes
            );

            // 转换为字符串
            var decoder = new TextDecoder();
            return decoder.decode(decrypted);
        } catch (e) {
            console.error("❌ Web Crypto RSA解密失败:", e.message);
            return null;
        }
    },

    // 简化的AES解密（使用Web Crypto API）
    aesDecryptWithWebCrypto: async function(ciphertext, key, iv) {
        if (typeof crypto === 'undefined' || !crypto.subtle) {
            throw new Error('Web Crypto API不可用');
        }

        try {
            // 将Base64密文转换为ArrayBuffer
            var ciphertextBytes = Uint8Array.from(atob(ciphertext), c => c.charCodeAt(0));
            
            // 将密钥和IV转换为ArrayBuffer
            var keyBytes = new TextEncoder().encode(key);
            var ivBytes = new TextEncoder().encode(iv);

            // 导入AES密钥
            var aesKey = await crypto.subtle.importKey(
                "raw",
                keyBytes,
                { name: "AES-CBC" },
                false,
                ["decrypt"]
            );

            // 解密
            var decrypted = await crypto.subtle.decrypt(
                {
                    name: "AES-CBC",
                    iv: ivBytes
                },
                aesKey,
                ciphertextBytes
            );

            // 转换为字符串
            var decoder = new TextDecoder();
            return decoder.decode(decrypted);
        } catch (e) {
            console.error("❌ Web Crypto AES解密失败:", e.message);
            return null;
        }
    },

    // 模拟小程序的解密逻辑（简化版本）
    simulateDecryption: function(responseData) {
        console.log("🔍 模拟解密过程（简化版本）:");
        console.log("📦 输入数据:", responseData);

        try {
            // 1. 提取数据
            var encryptedKey = responseData.key;
            var encryptedContent = responseData.content;
            var iv = responseData.iv;

            console.log("🔑 加密的AES密钥:", encryptedKey);
            console.log("📄 加密的内容:", encryptedContent);
            console.log("🎲 IV:", iv);

            // 2. Base64转Hex
            var hexKey = this.b64tohex(encryptedKey);
            if (!hexKey) {
                throw new Error("密钥格式转换失败");
            }
            console.log("🔧 Hex格式密钥:", hexKey);

            // 3. 模拟RSA解密（这里只是演示，实际需要真正的RSA解密）
            console.log("⚠️  注意：这是模拟解密，需要真正的RSA库才能解密实际数据");
            console.log("📋 要解密此数据，需要:");
            console.log("   1. 使用湖南版本的RSA私钥");
            console.log("   2. 对hex格式的密钥进行RSA解密得到AES密钥");
            console.log("   3. 使用AES密钥和IV对content进行AES-CBC-PKCS7解密");
            console.log("   4. 解析JSON结果");

            return {
                status: "需要真正的RSA解密",
                encryptedKey: encryptedKey,
                hexKey: hexKey,
                encryptedContent: encryptedContent,
                iv: iv,
                privateKey: "湖南版本RSA私钥已准备"
            };

        } catch (error) {
            console.error("❌ 模拟解密失败:", error.message);
            return null;
        }
    },

    // 尝试使用Web Crypto API解密（如果支持）
    tryWebCryptoDecrypt: async function(responseData) {
        console.log("🚀 尝试使用Web Crypto API解密:");
        
        try {
            // 1. 提取数据
            var encryptedKey = responseData.key;
            var encryptedContent = responseData.content;
            var iv = responseData.iv;

            // 2. Base64转Hex
            var hexKey = this.b64tohex(encryptedKey);
            if (!hexKey) {
                throw new Error("密钥格式转换失败");
            }

            // 3. RSA解密AES密钥
            console.log("🔓 尝试RSA解密AES密钥...");
            var aesKey = await this.rsaDecryptWithWebCrypto(hexKey);
            if (!aesKey) {
                throw new Error("RSA解密失败");
            }
            console.log("✅ AES密钥解密成功:", aesKey);

            // 4. AES解密内容
            console.log("🔓 尝试AES解密内容...");
            var decryptedContent = await this.aesDecryptWithWebCrypto(encryptedContent, aesKey, iv);
            if (!decryptedContent) {
                throw new Error("AES解密失败");
            }
            console.log("✅ 内容解密成功:", decryptedContent);

            // 5. 解析JSON
            var result = JSON.parse(decryptedContent);
            console.log("🎯 最终解密结果:", result);
            return result;

        } catch (error) {
            console.error("❌ Web Crypto解密失败:", error.message);
            return null;
        }
    },

    // 主要的解密函数
    decryptResponse: function(responseData) {
        console.log("\n========== 湖南版本真实数据解密 ==========");
        
        // 首先尝试模拟解密（显示解密步骤）
        var simulationResult = this.simulateDecryption(responseData);
        
        // 如果支持Web Crypto API，尝试真实解密
        if (typeof crypto !== 'undefined' && crypto.subtle) {
            console.log("\n🔬 检测到Web Crypto API支持，尝试真实解密...");
            return this.tryWebCryptoDecrypt(responseData);
        } else {
            console.log("\n⚠️  Web Crypto API不可用，只能显示解密步骤");
            console.log("💡 建议：在支持Web Crypto API的现代浏览器中运行此工具");
            return simulationResult;
        }
    },

    // 测试你提供的真实数据
    testRealData: function() {
        console.log("🧪 测试真实的响应数据解密:");
        
        var realResponseData = {
            "content": "Z5KNaR1xKweWGg2UUKPJ1xuppg7NE+MSeduoUrt7OlfofxEG6MA/EmFSVN1PN3TPOk2L9yWmtpwyxCPCrO77GhHZ889e7oTvXRVRM6F5vsQKIOuCY2NxFgqCt996rz1jCjP2nNkexi5UDgC+FYLGtAjsyR0bymB93y2TfMz/JK78PO47882bxE+2odl3BrIUD3QZ299aMMtsfirDcFmdgoS5Y72DGUHul1o6KCtP2AK7JmObE9oJUeodcspplF2PJ4Lg7s/kTgXxruU/fnfwUo5QrzTCmRaHK9QOBMTn4hxGXbNWXjhSB92e30K/E07zyLZBk8yfqSLlM6U94T/128M8i0Lnc9aCKIM7YmeSeHiZh+BlXM4O4gj8bk51PNNKsVeW7owo5TCBUkbyXLb6zA==",
            "key": "S5OUYtFrpKMZUGXPDE7TTT0HvZ0dCQpyA6DNznsWnUTMSFtkAXvRSPMWSve+z8Gsv4xOJJtgPLrAw5qeqly0HHgZpWyjDzOjahjeaJdkA7zoUtayySGX3yfUSCja1NtJOPGCn5OzJ6JJTg5OCfDdH0rj1KB06zHQ0YtsP5MTaMGSBj2ZkU4AkbHF3/MfpSuDnUYIevWg+uv3ywi3h5zBwyL8RjiqoVzGDy6pwLmp8o2Z+Gj6IJ8QYooItYNaQ7SG196vO8xfZd+sRm/RHen9dk4WqAEV8z6/FHkgvoGAVPrJVHLcNBEJee5irNIrQxeNP17UkOytbEvSxAkNXUPK4A==",
            "iv": "hihSPyR1EvP5$*!*"
        };

        return this.decryptResponse(realResponseData);
    }
};

// 使用说明
console.log("🎉 湖南版本真实解密工具已加载！");
console.log("\n📖 使用方法:");
console.log("1. RealDecryptTool.testRealData() - 测试你提供的真实数据");
console.log("2. RealDecryptTool.decryptResponse(responseData) - 解密任意响应数据");
console.log("\n⚠️  注意：此工具需要在支持Web Crypto API的现代浏览器中运行才能进行真实解密");

// 自动测试真实数据
console.log("\n🔥 自动测试你提供的真实数据:");
RealDecryptTool.testRealData();

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = RealDecryptTool;
}
