var e = require("../common/component"), i = require("../common/relation"), n = require("../mixins/link");

(0, e.VantComponent)({
    relation: (0, i.useParent)("grid"),
    classes: [ "content-class", "icon-class", "text-class" ],
    mixins: [ n.link ],
    props: {
        icon: String,
        iconColor: String,
        iconPrefix: {
            type: String,
            value: "van-icon"
        },
        dot: Boolean,
        info: null,
        badge: null,
        text: String,
        useSlot: Boolean
    },
    data: {
        viewStyle: ""
    },
    mounted: function() {
        this.updateStyle();
    },
    methods: {
        updateStyle: function() {
            if (this.parent) {
                var e = this.parent, i = e.data, n = e.children, t = i.columnNum, o = i.border, r = i.square, c = i.gutter, a = i.clickable, l = i.center, s = i.direction, u = i.reverse, d = i.iconSize;
                this.setData({
                    center: l,
                    border: o,
                    square: r,
                    gutter: c,
                    clickable: a,
                    direction: s,
                    reverse: u,
                    iconSize: d,
                    index: n.indexOf(this),
                    columnNum: t
                });
            }
        },
        onClick: function() {
            this.$emit("click"), this.jumpLink();
        }
    }
});