/**
 * 独立的湖南版本加密解密测试工具
 * 完全独立，无需依赖其他文件，可直接运行
 * 
 * 使用方法：
 * 1. CryptoTester.testEncryptDecrypt('测试数据') - 测试完整流程
 * 2. CryptoTester.encryptRequest({key: 'value'}) - 加密请求数据
 * 3. CryptoTester.decryptResponse({key: '...', content: '...', iv: '...'}) - 解密服务器响应
 */

var CryptoTester = {
    
    // ==================== 工具函数 ====================
    
    // 生成UUID
    generateUUID: function() {
        var chars = "0123456789abcdef";
        var uuid = [];
        for (var i = 0; i < 36; i++) {
            uuid[i] = chars.substr(Math.floor(Math.random() * 16), 1);
        }
        uuid[14] = "4";
        uuid[19] = chars.substr((uuid[19] & 0x3) | 0x8, 1);
        uuid[8] = uuid[13] = uuid[18] = uuid[23] = "-";
        return uuid.join("");
    },
    
    // 生成AES密钥（32字符）
    generateAESKey: function() {
        var uuid = this.generateUUID();
        var base64 = this.base64Encode(uuid);
        return base64.substring(2, 34);
    },
    
    // 生成IV（16字符）
    generateIV: function() {
        var uuid = this.generateUUID();
        var base64 = this.base64Encode(uuid);
        return base64.substring(2, 18);
    },
    
    // Base64编码
    base64Encode: function(str) {
        if (typeof btoa !== 'undefined') {
            return btoa(str);
        }
        // 简化的Base64编码
        var chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';
        var result = '';
        var i = 0;
        while (i < str.length) {
            var a = str.charCodeAt(i++);
            var b = i < str.length ? str.charCodeAt(i++) : 0;
            var c = i < str.length ? str.charCodeAt(i++) : 0;
            var bitmap = (a << 16) | (b << 8) | c;
            result += chars.charAt((bitmap >> 18) & 63);
            result += chars.charAt((bitmap >> 12) & 63);
            result += i - 2 < str.length ? chars.charAt((bitmap >> 6) & 63) : '=';
            result += i - 1 < str.length ? chars.charAt(bitmap & 63) : '=';
        }
        return result;
    },
    
    // Base64解码
    base64Decode: function(str) {
        if (typeof atob !== 'undefined') {
            return atob(str);
        }
        // 简化的Base64解码
        var chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';
        var result = '';
        var i = 0;
        str = str.replace(/[^A-Za-z0-9+/]/g, '');
        while (i < str.length) {
            var encoded1 = chars.indexOf(str.charAt(i++));
            var encoded2 = chars.indexOf(str.charAt(i++));
            var encoded3 = chars.indexOf(str.charAt(i++));
            var encoded4 = chars.indexOf(str.charAt(i++));
            var bitmap = (encoded1 << 18) | (encoded2 << 12) | (encoded3 << 6) | encoded4;
            result += String.fromCharCode((bitmap >> 16) & 255);
            if (encoded3 !== 64) result += String.fromCharCode((bitmap >> 8) & 255);
            if (encoded4 !== 64) result += String.fromCharCode(bitmap & 255);
        }
        return result;
    },
    
    // 简化的AES加密（仅用于演示）
    simpleAESEncrypt: function(plaintext, key, iv) {
        console.log("🔐 AES加密:");
        console.log("  明文:", plaintext);
        console.log("  密钥:", key);
        console.log("  IV:", iv);
        
        // 简化的加密逻辑（实际应使用标准AES-CBC-PKCS7）
        var result = this.base64Encode(plaintext + "||" + key.substring(0, 8) + "||" + iv.substring(0, 8));
        console.log("  密文:", result);
        return result;
    },
    
    // 简化的AES解密
    simpleAESDecrypt: function(ciphertext, key, iv) {
        console.log("🔓 AES解密:");
        console.log("  密文:", ciphertext);
        console.log("  密钥:", key);
        console.log("  IV:", iv);
        
        try {
            var decoded = this.base64Decode(ciphertext);
            var parts = decoded.split("||");
            if (parts.length === 3 && parts[1] === key.substring(0, 8) && parts[2] === iv.substring(0, 8)) {
                console.log("  明文:", parts[0]);
                return parts[0];
            }
            throw new Error("解密验证失败");
        } catch (e) {
            console.error("❌ AES解密失败:", e.message);
            return null;
        }
    },
    
    // 简化的RSA加密
    simpleRSAEncrypt: function(plaintext) {
        console.log("🔐 RSA加密:");
        console.log("  明文:", plaintext);
        
        // 简化的RSA加密（实际应使用真正的RSA算法）
        var result = this.base64Encode("RSA:" + plaintext + ":HN");
        console.log("  密文:", result);
        return result;
    },
    
    // 简化的RSA解密
    simpleRSADecrypt: function(ciphertext) {
        console.log("🔓 RSA解密:");
        console.log("  密文:", ciphertext);
        
        try {
            var decoded = this.base64Decode(ciphertext);
            if (decoded.startsWith("RSA:") && decoded.endsWith(":HN")) {
                var result = decoded.substring(4, decoded.length - 3);
                console.log("  明文:", result);
                return result;
            }
            throw new Error("无效的RSA密文格式");
        } catch (e) {
            console.error("❌ RSA解密失败:", e.message);
            return null;
        }
    },
    
    // Base64转Hex
    b64tohex: function(b64) {
        try {
            var binary = this.base64Decode(b64);
            var hex = '';
            for (var i = 0; i < binary.length; i++) {
                var h = binary.charCodeAt(i).toString(16);
                hex += h.length === 1 ? '0' + h : h;
            }
            return hex;
        } catch (e) {
            console.error("❌ Base64转Hex失败:", e.message);
            return null;
        }
    },
    
    // Hex转Base64
    hex2b64: function(hex) {
        try {
            var binary = '';
            for (var i = 0; i < hex.length; i += 2) {
                binary += String.fromCharCode(parseInt(hex.substr(i, 2), 16));
            }
            return this.base64Encode(binary);
        } catch (e) {
            console.error("❌ Hex转Base64失败:", e.message);
            return null;
        }
    },
    
    // ==================== 主要功能 ====================
    
    // 湖南版本请求加密
    encryptRequest: function(data) {
        console.log("\n========== 湖南版本请求加密 ==========");
        console.log("📝 原始数据:", data);
        
        try {
            // 1. 转换为字符串
            var dataString = typeof data === 'object' ? JSON.stringify(data) : String(data);
            console.log("📄 字符串数据:", dataString);
            
            // 2. 生成AES密钥和IV
            var aesKey = this.generateAESKey();
            var iv = this.generateIV();
            console.log("🔑 生成AES密钥:", aesKey);
            console.log("🎲 生成IV:", iv);
            
            // 3. AES加密数据
            var encryptedData = this.simpleAESEncrypt(dataString, aesKey, iv);
            if (!encryptedData) {
                throw new Error("AES加密失败");
            }
            
            // 4. RSA加密AES密钥
            var encryptedKey = this.simpleRSAEncrypt(aesKey);
            if (!encryptedKey) {
                throw new Error("RSA加密失败");
            }
            
            // 5. 构造最终结果
            var result = {
                avd: iv,                                    // IV（明文传输）
                ksynum: this.hex2b64(encryptedKey),        // RSA加密的AES密钥（转换为Base64）
                cmcxncxn: encryptedData                    // AES加密的数据
            };
            
            console.log("✅ 加密完成:");
            console.log("  avd (IV):", result.avd);
            console.log("  ksynum (加密密钥):", result.ksynum);
            console.log("  cmcxncxn (加密数据):", result.cmcxncxn);
            
            return result;
            
        } catch (error) {
            console.error("❌ 加密失败:", error.message);
            return null;
        }
    },
    
    // 湖南版本响应解密
    decryptResponse: function(encryptedResponse) {
        console.log("\n========== 湖南版本响应解密 ==========");
        console.log("📦 加密响应:", encryptedResponse);
        
        try {
            // 1. 验证输入格式
            if (!encryptedResponse || !encryptedResponse.key || !encryptedResponse.content || !encryptedResponse.iv) {
                throw new Error("无效的响应格式，缺少必要字段");
            }
            
            // 2. 提取数据
            var encryptedKey = encryptedResponse.key;
            var encryptedContent = encryptedResponse.content;
            var iv = encryptedResponse.iv;
            
            console.log("🔑 加密的密钥:", encryptedKey);
            console.log("📄 加密的内容:", encryptedContent);
            console.log("🎲 IV:", iv);
            
            // 3. Base64转Hex（如果需要）
            var hexKey = this.b64tohex(encryptedKey);
            if (!hexKey) {
                throw new Error("密钥格式转换失败");
            }
            
            // 4. RSA解密AES密钥
            var aesKey = this.simpleRSADecrypt(hexKey);
            if (!aesKey) {
                throw new Error("RSA解密AES密钥失败");
            }
            
            // 5. AES解密数据
            var decryptedData = this.simpleAESDecrypt(encryptedContent, aesKey, iv);
            if (!decryptedData) {
                throw new Error("AES解密数据失败");
            }
            
            // 6. 尝试解析JSON
            var result;
            try {
                result = JSON.parse(decryptedData);
                console.log("📋 解析为JSON对象:", result);
            } catch (e) {
                result = decryptedData;
                console.log("📄 保持为字符串:", result);
            }
            
            console.log("✅ 解密完成");
            return result;
            
        } catch (error) {
            console.error("❌ 解密失败:", error.message);
            return null;
        }
    },
    
    // 完整的加密解密测试
    testEncryptDecrypt: function(testData) {
        console.log("\n🧪 ========== 完整加密解密测试 ==========");
        console.log("🎯 测试数据:", testData);
        
        // 1. 加密数据
        var encrypted = this.encryptRequest(testData);
        if (!encrypted) {
            console.error("❌ 测试失败：加密阶段出错");
            return false;
        }
        
        // 2. 模拟服务器响应格式
        var serverResponse = {
            key: encrypted.ksynum,
            content: encrypted.cmcxncxn,
            iv: encrypted.avd
        };
        console.log("\n📡 模拟服务器响应:", serverResponse);
        
        // 3. 解密响应
        var decrypted = this.decryptResponse(serverResponse);
        if (!decrypted) {
            console.error("❌ 测试失败：解密阶段出错");
            return false;
        }
        
        // 4. 验证结果
        var originalStr = typeof testData === 'object' ? JSON.stringify(testData) : String(testData);
        var decryptedStr = typeof decrypted === 'object' ? JSON.stringify(decrypted) : String(decrypted);
        
        var isSuccess = originalStr === decryptedStr;
        
        console.log("\n🔍 ========== 验证结果 ==========");
        console.log("📝 原始数据:", originalStr);
        console.log("🔓 解密数据:", decryptedStr);
        console.log("✅ 数据一致性:", isSuccess ? "通过 ✅" : "失败 ❌");
        
        if (!isSuccess) {
            console.log("📏 原始长度:", originalStr.length);
            console.log("📏 解密长度:", decryptedStr.length);
        }
        
        return isSuccess;
    },
    
    // 运行多个测试用例
    runAllTests: function() {
        console.log("🚀 开始运行所有测试用例...\n");
        
        var testCases = [
            "Hello World",
            "测试中文字符",
            "123456789",
            {name: "张三", age: 25, city: "长沙"},
            {sCode: "SJS_CODE_12345", areaCode: "430100"},
            "43&business_handling_code&28.2282&112.9388&test_token&***********&APP001&/pages/index/index&mini"
        ];
        
        var passCount = 0;
        var totalCount = testCases.length;
        
        for (var i = 0; i < testCases.length; i++) {
            console.log("\n" + "=".repeat(50));
            console.log("📋 测试用例 " + (i + 1) + "/" + totalCount);
            
            if (this.testEncryptDecrypt(testCases[i])) {
                passCount++;
            }
        }
        
        console.log("\n" + "=".repeat(50));
        console.log("📊 测试总结:");
        console.log("✅ 通过: " + passCount + "/" + totalCount);
        console.log("❌ 失败: " + (totalCount - passCount) + "/" + totalCount);
        console.log("📈 成功率: " + Math.round(passCount / totalCount * 100) + "%");
        
        return passCount === totalCount;
    }
};

// ==================== 使用示例 ====================
console.log("🎉 湖南版本独立加密解密测试工具已加载！");
console.log("\n📖 使用方法:");
console.log("1. CryptoTester.testEncryptDecrypt('测试数据') - 测试完整流程");
console.log("2. CryptoTester.encryptRequest({key: 'value'}) - 加密请求数据");
console.log("3. CryptoTester.decryptResponse({key: '...', content: '...', iv: '...'}) - 解密服务器响应");
console.log("4. CryptoTester.runAllTests() - 运行所有测试用例");

// 自动运行一个简单测试
console.log("\n🔥 自动运行简单测试:");
CryptoTester.testEncryptDecrypt("Hello 湖南!");

// 导出模块（如果在Node.js环境中）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CryptoTester;
}
