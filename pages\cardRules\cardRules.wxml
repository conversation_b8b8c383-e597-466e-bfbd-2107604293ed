<view class="container">
    <swiper autoplay="{{false}}" circular="{{false}}" class="s-view" duration="{{500}}" indicatorActiveColor="#fff" indicatorColor="rgba(255, 255, 255, .3)" indicatorDots="{{false}}" interval="{{5000}}">
        <swiper-item class="s-view" wx:for="{{memberGrade}}" wx:for-index="outIndex" wx:for-item="outItem" wx:key="outIndex">
            <view bindtap="handleSkipCoupon" class=" {{selectVal==item.memberGradeCode?'s-view-itmes-sel':'s-view-itmes'}} " data-item="{{item}}" wx:if="{{item}}" wx:for="{{outItem}}" wx:key="index">{{item.memberGradeName}}</view>
        </swiper-item>
    </swiper>
    <view class="s-view-b">
        <rich-text nodes="{{currentHtml}}" wx:if="{{currentHtml!=''}}"></rich-text>
        <view class="tc" wx:if="{{available}}">暂无业务详情展示</view>
    </view>
</view>
<view bindtap="BuyOpen" class="buyLogin" wx:if="{{memberNone&&showDealWith}}">
    <image src="/static/image/gobuy.png"></image>
</view>
<van-loading class="loading" color="#DD3231" type="spinner" wx:if="{{isLoading}}"></van-loading>
<view class="copyright1 tc ft12">
    <view style="font-size:22rpx;font-family:PingFang SC;color:rgba(157,156,156,1);">©中国移动通信集团江苏有限公司版权所有</view>
    <view style="font-size:22rpx;font-family:PingFang SC;color:rgba(157,156,156,1);">苏 ICP备11070397号-21</view>
    <view style="font-size:22rpx;font-family:PingFang SC;color:rgba(157,156,156,1);">（中国移动通信集团委托江苏有限公司支撑全国12580惠出行全网平台）</view>
</view>
